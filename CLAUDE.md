# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

**Purpose**: Daily news aggregator that collects crypto/RWA content, evaluates relevance with AI, and publishes weekly newsletters to Notion.

## Quick Start

```bash
# Setup
pip install -r requirements.txt
pip install -e .  # Required for imports to work

# Run daily collection
python scripts/main.py --enable-email-alerts
```

## Environment Variables

Create `.env` file from `.env.example` with these required keys:
- `SUPABASE_DB_URL` - PostgreSQL connection string
- `ANTHROPIC_API_KEY` - For AI relevance evaluation
- `X_BEARER_TOKEN` - Twitter API access
- `TELEGRAM_*` - Telegram credentials (session string expires)
- `NOTION_*` - Publishing destination
- `EMAIL_*` - SMTP settings for alerts
- `GOOGLE_*` - Form and service account

## Key Commands

```bash
# Test database connection
python test_supabase_connection.py

# Run with specific components
python scripts/main.py --skip-telegram --skip-notion  # Debug mode
python scripts/main.py --email-dry-run  # Test email alerts

# Common test commands
python tests/test_time_sensitive.py
python tests/notion/test_notion_connection.py
python tests/storage/test_all_pg_modules.py
```

## Architecture

**Daily collector → Weekly publisher**
1. Runs daily at 09:00 UTC via GitHub Actions
2. Collects from X, Telegram, RSS, Google Form
3. Deduplicates by URL (prioritizes primary sources)
4. AI evaluates relevance + time sensitivity
5. Sends email alerts for urgent items
6. Publishes to weekly Notion page (Thursday-Thursday)

**Database**: PostgreSQL on Supabase (migrated from SQLite)
- Storage modules: `*_pg.py` files (SQLite `*.py` files kept for compatibility)
- Connection: `core/storage/pg_utils.py`
- State tracking: `daily_runs`, `publication_state`, `x_collection_state`

**Main entry point**: `scripts/main.py`

## Important Patterns

**Error Handling**: Each source fails independently
```python
for source in data_sources:
    try:
        items = source.fetch_items(config)
    except Exception as e:
        logger.error(f"Error: {e}")
        # Continue with other sources
```

**Deduplication**: URL-based with source priority
1. `google_news_rss` (highest - primary articles)
2. `x_list_scraper` (social media)
3. `telegram` (commentary)
4. `google_form` (lowest - manual submissions)

**Time-Sensitive Detection**: `core/time_sensitive_service.py`
- Keywords: "breaking", "urgent", "alert"
- Only RELEVANT items trigger emails
- Limit: 10 items per email run

## Common Issues

**Telegram Session Expired**
```bash
python sources/telegram/telegram_setup.py  # Generate new session
# Update TELEGRAM_SESSION_STRING in .env and GitHub Secrets
```

**"No module named 'core'"**
- Run: `pip install -e .`

**Debug database state**
```bash
psql "$SUPABASE_DB_URL" -c "SELECT * FROM daily_runs ORDER BY run_timestamp DESC LIMIT 5;"
```

**No new items to publish**
- Check watermark: `SELECT * FROM daily_runs WHERE status = 'completed' ORDER BY run_timestamp DESC LIMIT 1;`
- Items may already be published or marked NOT_RELEVANT

## Adding Features

**New Data Source**:
1. Create `sources/newsource/collector.py` implementing:
   - `get_source_name() -> str`
   - `fetch_items(config) -> List[NewsItem]`
2. Add to `scripts/main.py` data_sources list
3. Add config to `core/config.py`

**Database Changes**:
1. Update `core/storage/db_init_pg.py`
2. Update `core/models.py`
3. Update relevant `*_pg.py` storage module
4. Test: `python tests/storage/test_all_pg_modules.py`

## GitHub Actions

**Daily Collection** (`daily-collection.yml`):
- Runs: 09:00 UTC daily
- Command: `python scripts/main.py --daily-collection-hours 24 --enable-email-alerts`
- Logs: Download from workflow artifacts

**Required Secrets**: All environment variables above

## Key Files to Know

- `scripts/main.py` - Main orchestration logic
- `core/config.py` - All configuration and env vars
- `core/deduplication.py` - URL-based dedup logic
- `core/time_sensitive_service.py` - Email alert detection
- `sources/notion/notion_publisher.py` - Weekly page creation

## Notes

- X/Twitter: Rate limit ~25 requests/15min, pagination state saved
- Telegram: Session can expire without warning  
- Notion: Creates new page each Thursday
- Emails: Only for time-sensitive + relevant items
- Supabase: Free tier works fine, connection string in project settings