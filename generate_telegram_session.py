#!/usr/bin/env python3
"""
Generate a Telegram session string for GitHub Actions.
This creates a base64-encoded session that can be used as a secret.

SECURITY NOTE: This script outputs the session string to console only.
Never save session strings to files!
"""

import os
import base64
import asyncio
from telethon import Telegram<PERSON>lient
from telethon.sessions import StringSession
from telethon.errors import SessionPasswordNeededError
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def generate_session():
    # Get credentials from .env
    api_id_str = os.getenv('TELEGRAM_API_ID')
    api_hash = os.getenv('TELEGRAM_API_HASH')
    
    if not api_id_str or not api_hash:
        print("Error: TELEGRAM_API_ID and TELEGRAM_API_HASH must be set in .env file")
        return
    
    try:
        api_id = int(api_id_str)
    except ValueError:
        print(f"Error: TELEGRAM_API_ID must be a number, got: {api_id_str}")
        return
    
    print(f"Using API ID: {api_id}")
    print("Creating new session...\n")
    
    # Create a new client with StringSession
    client = TelegramClient(StringSession(), api_id, api_hash)
    
    try:
        await client.connect()
        
        # Interactive login
        phone_number = input("Please enter your phone number (with country code): ")
        await client.send_code_request(phone_number)
        
        try:
            code = input("Enter the verification code: ")
            await client.sign_in(phone_number, code)
        except SessionPasswordNeededError:
            password = input("Two-factor authentication enabled. Enter password: ")
            await client.sign_in(password=password)
        
        # Get the session string
        session_string = client.session.save()
        
        # Encode it in base64 for GitHub secrets
        encoded = base64.b64encode(session_string.encode()).decode()
        
        print("\n" + "="*80)
        print("✅ SESSION CREATED SUCCESSFULLY!")
        print("="*80)
        
        print("\n📋 STEP 1: Copy this session string:")
        print("-" * 80)
        print(encoded)
        print("-" * 80)
        
        print("\n📝 STEP 2: Add to GitHub Secrets:")
        print("1. Go to your GitHub repository")
        print("2. Click Settings → Secrets and variables → Actions")
        print("3. Click 'New repository secret'")
        print("4. Name: TELEGRAM_SESSION_STRING")
        print("5. Value: Paste the string from Step 1")
        print("6. Click 'Add secret'")
        
        print("\n⚠️  SECURITY NOTES:")
        print("- NEVER commit this session string to your repository")
        print("- NEVER share this session string publicly")
        print("- This session gives full access to your Telegram account")
        print("- If compromised, immediately revoke it from Telegram settings")
        
        print("\n🔄 SESSION EXPIRATION:")
        print("- Sessions can expire if you log out all devices in Telegram")
        print("- If the session expires, run this script again to generate a new one")
        print("- Update the GitHub Secret with the new session string")
        
        print("\n✅ After adding the secret, your GitHub Actions will be able to access Telegram!")
        print("="*80)
        
    except Exception as e:
        print(f"\n❌ Error during session generation: {e}")
        return
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(generate_session())