-- Migration: Add deduplication tracking fields to news_items and tweet_items tables
-- Date: 2025-01-24
-- Purpose: Track duplicate items while preserving all data for analysis

-- Add deduplication fields to news_items table
ALTER TABLE news_items
ADD COLUMN IF NOT EXISTS is_duplicate BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS duplicate_of_url TEXT,
ADD COLUMN IF NOT EXISTS duplicate_of_item_type TEXT, -- 'news_item' or 'tweet_item'
ADD COLUMN IF NOT EXISTS duplicate_of_item_id TEXT,   -- ID of the canonical item
ADD COLUMN IF NOT EXISTS deduplication_reason TEXT;

-- Add deduplication fields to tweet_items table  
ALTER TABLE tweet_items
ADD COLUMN IF NOT EXISTS is_duplicate BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS duplicate_of_url TEXT,
ADD COLUMN IF NOT EXISTS duplicate_of_item_type TEXT, -- 'news_item' or 'tweet_item'
ADD COLUMN IF NOT EXISTS duplicate_of_item_id TEXT,   -- ID of the canonical item
ADD COLUMN IF NOT EXISTS deduplication_reason TEXT;

-- Create indexes for efficient querying of duplicates
CREATE INDEX IF NOT EXISTS idx_news_items_is_duplicate ON news_items(is_duplicate);
CREATE INDEX IF NOT EXISTS idx_news_items_duplicate_of_url ON news_items(duplicate_of_url);
CREATE INDEX IF NOT EXISTS idx_tweet_items_is_duplicate ON tweet_items(is_duplicate);
CREATE INDEX IF NOT EXISTS idx_tweet_items_duplicate_of_url ON tweet_items(duplicate_of_url);

-- Add comments for documentation
COMMENT ON COLUMN news_items.is_duplicate IS 'True if this item is a duplicate of another item';
COMMENT ON COLUMN news_items.duplicate_of_url IS 'The normalized URL this item is a duplicate of';
COMMENT ON COLUMN news_items.duplicate_of_item_type IS 'Type of the canonical item (news_item or tweet_item)';
COMMENT ON COLUMN news_items.duplicate_of_item_id IS 'ID or identifier of the canonical item';
COMMENT ON COLUMN news_items.deduplication_reason IS 'Programmatic reason why this was marked as duplicate';

COMMENT ON COLUMN tweet_items.is_duplicate IS 'True if this item is a duplicate of another item';
COMMENT ON COLUMN tweet_items.duplicate_of_url IS 'The normalized URL this item is a duplicate of';
COMMENT ON COLUMN tweet_items.duplicate_of_item_type IS 'Type of the canonical item (news_item or tweet_item)';
COMMENT ON COLUMN tweet_items.duplicate_of_item_id IS 'ID or identifier of the canonical item';
COMMENT ON COLUMN tweet_items.deduplication_reason IS 'Programmatic reason why this was marked as duplicate';