#!/usr/bin/env python3
"""
Test suite for week boundary utilities.
"""

import unittest
from datetime import datetime, timedelta
import pytz

from core.week_boundary_utils import (
    get_current_week_start,
    get_next_week_cutoff,
    get_week_date_range,
    is_in_current_week,
    get_week_number,
    calculate_daily_window
)


class TestWeekBoundaryUtils(unittest.TestCase):
    """Test the week boundary utility functions."""
    
    def setUp(self):
        """Set up Pacific timezone for tests."""
        self.pacific_tz = pytz.timezone('US/Pacific')
    
    def test_get_current_week_start(self):
        """Test getting the start of the current week."""
        week_start = get_current_week_start()
        
        # Should be a Thursday at midnight
        self.assertEqual(week_start.weekday(), 3)  # Thursday = 3 in Python
        self.assertEqual(week_start.hour, 0)
        self.assertEqual(week_start.minute, 0)
        self.assertEqual(week_start.second, 0)
        self.assertEqual(week_start.microsecond, 0)
        
        # Should be within the last 7 days
        now = datetime.now(self.pacific_tz)
        days_diff = (now - week_start).days
        self.assertGreaterEqual(days_diff, 0)
        self.assertLess(days_diff, 7)
    
    def test_get_next_week_cutoff(self):
        """Test getting the cutoff for next week."""
        current_start = get_current_week_start()
        next_cutoff = get_next_week_cutoff()
        
        # Should be exactly 7 days after current week start
        diff = next_cutoff - current_start
        self.assertEqual(diff.days, 7)
        self.assertEqual(diff.seconds, 0)
        
        # Should also be a Thursday at midnight
        self.assertEqual(next_cutoff.weekday(), 3)
        self.assertEqual(next_cutoff.hour, 0)
        self.assertEqual(next_cutoff.minute, 0)
    
    def test_get_week_date_range(self):
        """Test formatting week date ranges."""
        # Test with a specific date in 2025
        test_date = datetime(2025, 1, 2)  # Thursday, Jan 2, 2025
        date_range = get_week_date_range(test_date)
        self.assertEqual(date_range, "Jan 02 - Jan 08, 2025")
        
        # Test year boundary
        year_boundary = datetime(2023, 12, 28)  # Thursday, Dec 28, 2023
        date_range = get_week_date_range(year_boundary)
        self.assertEqual(date_range, "Dec 28, 2023 - Jan 03, 2024")
        
        # Test current week (format only, not exact dates)
        current_range = get_week_date_range()
        self.assertIn(" - ", current_range)
        self.assertIn(str(datetime.now().year), current_range)
    
    def test_is_in_current_week(self):
        """Test checking if timestamp is in current week."""
        now = datetime.now(self.pacific_tz)
        
        # Current time should be in current week
        self.assertTrue(is_in_current_week(now))
        
        # Last week should not be in current week
        last_week = now - timedelta(days=7)
        self.assertFalse(is_in_current_week(last_week))
        
        # Next week should not be in current week
        next_week = now + timedelta(days=7)
        self.assertFalse(is_in_current_week(next_week))
        
        # Test edge cases
        week_start = get_current_week_start()
        next_cutoff = get_next_week_cutoff()
        
        # Start of week should be included
        self.assertTrue(is_in_current_week(week_start))
        
        # One second before next week should be included
        self.assertTrue(is_in_current_week(next_cutoff - timedelta(seconds=1)))
        
        # Exact cutoff should not be included
        self.assertFalse(is_in_current_week(next_cutoff))
    
    def test_get_week_number(self):
        """Test getting ISO week numbers."""
        # Test specific date
        test_date = datetime(2024, 1, 8)  # Monday, Jan 8, 2024
        year, week = get_week_number(test_date)
        self.assertEqual(year, 2024)
        self.assertEqual(week, 2)  # Second week of 2024
        
        # Test year boundary edge case
        # Dec 31, 2023 is a Sunday, ISO week calculation is independent of our Thursday boundary
        year_end = datetime(2023, 12, 31)
        year, week = get_week_number(year_end)
        self.assertEqual(year, 2023)
        self.assertEqual(week, 52)
        
        # Test current week returns valid values
        current_year, current_week = get_week_number()
        self.assertGreaterEqual(current_year, 2025)
        self.assertGreaterEqual(current_week, 1)
        self.assertLessEqual(current_week, 53)
    
    def test_calculate_daily_window(self):
        """Test calculating daily collection windows."""
        # Test default 24-hour window
        end_time = datetime.now()
        start, end = calculate_daily_window(end_time)
        
        diff = end - start
        self.assertEqual(diff.total_seconds(), 24 * 3600)
        self.assertEqual(end, end_time)
        
        # Test custom window
        start, end = calculate_daily_window(end_time, hours=12)
        diff = end - start
        self.assertEqual(diff.total_seconds(), 12 * 3600)
        
        # Test with None (should use current time)
        start, end = calculate_daily_window()
        self.assertIsInstance(start, datetime)
        self.assertIsInstance(end, datetime)
        self.assertLess(start, end)


if __name__ == '__main__':
    unittest.main()