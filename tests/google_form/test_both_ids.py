"""
Test script to check both potential Google Form Sheet IDs.

This script tests both Google Sheet IDs to help determine which is the correct one.
"""

from datetime import datetime

from sources.google_form.google_forms import get_sheets_service, fetch_form_submissions
from core.config import GOOGLE_CREDENTIALS_FILE
from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_sheet_id(sheet_id):
    """Test a specific Google Sheet ID."""
    print(f"\n=== Testing Google Sheet ID: {sheet_id} ===\n")
    
    try:
        # Set up credentials
        service = get_sheets_service()
        if not service:
            logger.error("Failed to initialize Google Sheets service")
            return False
            
        # Try to get the spreadsheet metadata
        logger.info(f"Fetching spreadsheet metadata for ID: {sheet_id}")
        spreadsheet = service.spreadsheets().get(spreadsheetId=sheet_id).execute()
        
        # Print spreadsheet title
        title = spreadsheet['properties']['title']
        logger.info(f"Successfully accessed spreadsheet: {title}")
        
        # Try to get the first sheet
        sheets = spreadsheet.get('sheets', [])
        if not sheets:
            logger.warning("No sheets found in the spreadsheet")
            return False
            
        sheet_title = sheets[0]['properties']['title']
        logger.info(f"First sheet title: {sheet_title}")
        
        # Try to get the headers
        logger.info("Fetching sheet headers...")
        result = service.spreadsheets().values().get(
            spreadsheetId=sheet_id,
            range=f"{sheet_title}!1:1"
        ).execute()
        
        values = result.get('values', [[]])
        if not values or not values[0]:
            logger.warning("No headers found in the first row")
            return False
            
        headers = values[0]
        logger.info(f"Found {len(headers)} columns in the form responses sheet")
        logger.info(f"Headers: {', '.join(headers)}")
        
        # Try to get a few rows of data
        logger.info("Fetching sample data...")
        result = service.spreadsheets().values().get(
            spreadsheetId=sheet_id,
            range=f"{sheet_title}!A1:F10"  # Get up to 10 rows
        ).execute()
        
        values = result.get('values', [])
        if len(values) <= 1:
            logger.warning("No data found in the spreadsheet (only headers)")
            return False
            
        logger.info(f"Found {len(values)-1} rows of data (excluding headers)")
        
        # Print a sample of the data (first 3 rows)
        print("\nSample data (first 3 rows):")
        for i, row in enumerate(values[1:4]):  # Skip header, take up to 3 rows
            print(f"\nRow {i+1}:")
            for j, cell in enumerate(row):
                if j < len(headers):
                    print(f"  {headers[j]}: {cell}")
                else:
                    print(f"  Column {j+1}: {cell}")
        
        # Try to fetch submissions using our regular function
        logger.info("Trying to fetch submissions using our regular function...")
        submissions = fetch_form_submissions(sheet_id=sheet_id)
        
        if submissions:
            logger.info(f"Successfully fetched {len(submissions)} submissions")
            print("\nProcessed submissions:")
            for i, item in enumerate(submissions[:3]):  # Show first 3 submissions
                print(f"\n--- Submission {i+1} ---")
                print(f"Title: {item.title}")
                print(f"Link: {item.link}")
                print(f"Published: {item.published_at.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Publisher: {item.publisher}")
                print(f"Summary: {item.summary}")
        else:
            logger.warning("No submissions could be fetched or processed")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing sheet ID {sheet_id}: {e}")
        return False

def main():
    """Test both potential Google Sheet IDs."""
    print("\n=== Testing Both Potential Google Form Sheet IDs ===\n")
    
    # The two IDs we're testing
    id1 = "15O_GGK_gTjOyuwRSBHTWEgqM1jRXQ5jSkCYFdxw9ukA"  # Currently working ID
    id2 = "1ECg1IijrX4TCC8jPzrEFrefvzTAbpI3__soYDtDVOhg"  # ID from memories
    
    # Test the first ID
    success1 = test_sheet_id(id1)
    
    # Test the second ID
    success2 = test_sheet_id(id2)
    
    # Summarize results
    print("\n=== Summary ===\n")
    print(f"ID 1 ({id1}): {'✅ Accessible' if success1 else '❌ Not accessible'}")
    print(f"ID 2 ({id2}): {'✅ Accessible' if success2 else '❌ Not accessible'}")
    
    if success1 and success2:
        print("\nBoth IDs are accessible. Please review the content of each to determine which is the correct one.")
    elif success1:
        print("\nOnly ID 1 is accessible. This is likely the correct one to use.")
    elif success2:
        print("\nOnly ID 2 is accessible. This is likely the correct one to use.")
    else:
        print("\nNeither ID is accessible. Please check your credentials and permissions.")

if __name__ == "__main__":
    main()
