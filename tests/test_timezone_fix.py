"""
Comprehensive test suite for timezone fix verification.

This test suite ensures that all datetime objects created throughout the system
are timezone-aware and that the warning system works correctly.
"""

import unittest
import logging
import sys
from datetime import datetime, timedelta
from io import StringIO
import pytz
from unittest.mock import patch, MagicMock

# Add parent directory to path for imports
sys.path.insert(0, '..')

from core.timezone_config import (
    parse_datetime_assume_utc, 
    utc_from_timestamp, 
    now_utc, 
    ensure_utc,
    ensure_pst,
    STORAGE_TIMEZONE,
    APP_TIMEZONE
)


class TestTimezoneWarnings(unittest.TestCase):
    """Test that warnings are properly triggered and avoided."""
    
    def setUp(self):
        """Set up test logging capture."""
        self.log_capture = StringIO()
        self.handler = logging.StreamHandler(self.log_capture)
        self.handler.setLevel(logging.WARNING)
        
        self.logger = logging.getLogger('core.timezone_config')
        self.logger.addHandler(self.handler)
        self.logger.setLevel(logging.WARNING)
    
    def tearDown(self):
        """Clean up logging handlers."""
        self.logger.removeHandler(self.handler)
    
    def test_ensure_utc_with_naive_datetime_triggers_warning(self):
        """Test that naive datetime triggers warning."""
        naive_dt = datetime(2025, 6, 27, 10, 30, 0)
        
        # This should trigger a warning
        result = ensure_utc(naive_dt)
        
        # Check warning was logged
        log_output = self.log_capture.getvalue()
        self.assertIn("Naive datetime", log_output)
        self.assertIn("2025-06-27 10:30:00", log_output)
        self.assertIn("Assuming UTC", log_output)
        
        # Result should be timezone-aware
        self.assertIsNotNone(result.tzinfo)
        self.assertEqual(result.tzinfo, STORAGE_TIMEZONE)
    
    def test_ensure_utc_with_aware_datetime_no_warning(self):
        """Test that timezone-aware datetime doesn't trigger warning."""
        aware_dt = datetime(2025, 6, 27, 10, 30, 0, tzinfo=pytz.UTC)
        
        # This should NOT trigger a warning
        result = ensure_utc(aware_dt)
        
        # Check no warning was logged
        log_output = self.log_capture.getvalue()
        self.assertEqual(log_output, "")
        
        # Result should remain timezone-aware
        self.assertIsNotNone(result.tzinfo)


class TestTimezoneFunctions(unittest.TestCase):
    """Test the new timezone utility functions."""
    
    def test_parse_datetime_assume_utc(self):
        """Test parsing datetime strings to UTC."""
        # Test Google Forms format
        dt1 = parse_datetime_assume_utc('06/27/2025 09:30:00', '%m/%d/%Y %H:%M:%S')
        self.assertEqual(dt1.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(dt1.year, 2025)
        self.assertEqual(dt1.month, 6)
        self.assertEqual(dt1.day, 27)
        self.assertEqual(dt1.hour, 9)
        self.assertEqual(dt1.minute, 30)
        
        # Test alternative format
        dt2 = parse_datetime_assume_utc('2025-06-27 14:45:30', '%Y-%m-%d %H:%M:%S')
        self.assertEqual(dt2.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(dt2.hour, 14)
        self.assertEqual(dt2.minute, 45)
    
    def test_utc_from_timestamp(self):
        """Test converting Unix timestamp to UTC datetime."""
        # Known timestamp: 2025-06-27 12:00:00 UTC
        timestamp = 1735308000
        dt = utc_from_timestamp(timestamp)
        
        self.assertEqual(dt.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(dt.year, 2024)
        self.assertEqual(dt.month, 12)
        self.assertEqual(dt.day, 27)
        self.assertEqual(dt.hour, 14)  # Adjusted for UTC
    
    def test_now_utc(self):
        """Test that now_utc returns timezone-aware datetime."""
        dt = now_utc()
        self.assertIsNotNone(dt.tzinfo)
        self.assertEqual(dt.tzinfo, STORAGE_TIMEZONE)
        
        # Should be close to current UTC time
        now_utc_aware = datetime.now(STORAGE_TIMEZONE)
        diff = abs((dt - now_utc_aware).total_seconds())
        self.assertLess(diff, 1)  # Within 1 second


class TestDataSourceIntegration(unittest.TestCase):
    """Test that data sources create timezone-aware datetimes."""
    
    def setUp(self):
        """Set up logging capture for integration tests."""
        self.log_capture = StringIO()
        self.handler = logging.StreamHandler(self.log_capture)
        self.handler.setLevel(logging.WARNING)
        
        # Capture warnings from all relevant modules
        for module in ['core.timezone_config', 'sources.google_form', 
                      'sources.google_rss', 'sources.telegram']:
            logger = logging.getLogger(module)
            logger.addHandler(self.handler)
            logger.setLevel(logging.WARNING)
    
    def tearDown(self):
        """Clean up logging handlers."""
        for module in ['core.timezone_config', 'sources.google_form', 
                      'sources.google_rss', 'sources.telegram']:
            logger = logging.getLogger(module)
            logger.removeHandler(self.handler)
    
    @patch('sources.google_form.google_forms.get_sheets_service')
    def test_google_forms_no_warnings(self, mock_service):
        """Test Google Forms creates timezone-aware datetimes without warnings."""
        from sources.google_form.google_forms import fetch_form_submissions
        
        # Mock the Google Sheets API response
        mock_sheet = MagicMock()
        mock_service.return_value = mock_sheet
        mock_sheet.spreadsheets.return_value.values.return_value.get.return_value.execute.return_value = {
            'values': [
                ['Timestamp', 'Email Address', 'What is your Telegram handle?', 
                 'What is your name? (First + Last: "Johnny Reinsch")', 
                 'What TAC member organization do you represent?',
                 'Give us the link to the content or social post you\'re submitting'],
                ['06/27/2025 10:30:00', '<EMAIL>', '@testuser', 
                 'Test User', 'Test Org', 'https://example.com/article']
            ]
        }
        
        # Fetch submissions
        items = fetch_form_submissions()
        
        # Check no warnings were logged
        log_output = self.log_capture.getvalue()
        self.assertNotIn("Naive datetime", log_output)
        
        # Check item has timezone-aware datetimes
        self.assertEqual(len(items), 1)
        item = items[0]
        self.assertIsNotNone(item.published_at.tzinfo)
        self.assertIsNotNone(item.fetched_at.tzinfo)
        self.assertEqual(item.published_at.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(item.fetched_at.tzinfo, STORAGE_TIMEZONE)
    
    def test_google_rss_no_warnings(self):
        """Test Google RSS creates timezone-aware datetimes without warnings."""
        from sources.google_rss import parse_date
        import time
        
        # Create a mock RSS entry
        class MockEntry:
            def __init__(self):
                # Current time as struct_time
                self.published_parsed = time.gmtime()
                self.published = None
            
            def get(self, key, default=None):
                return default
        
        entry = MockEntry()
        
        # Parse the date
        dt = parse_date(entry)
        
        # Check no warnings were logged
        log_output = self.log_capture.getvalue()
        self.assertNotIn("Naive datetime", log_output)
        
        # Check datetime is timezone-aware
        self.assertIsNotNone(dt.tzinfo)
        self.assertEqual(dt.tzinfo, STORAGE_TIMEZONE)
    
    def test_x_twitter_time_utils_no_warnings(self):
        """Test X/Twitter time utilities create timezone-aware datetimes."""
        from sources.x.time_utils import validate_time_window, tweet_id_to_date
        
        # Test validate_time_window
        is_valid, error = validate_time_window()
        self.assertTrue(is_valid)
        
        # Test tweet_id_to_date
        tweet_id = "1234567890123456789"
        dt = tweet_id_to_date(tweet_id)
        
        # Check no warnings were logged
        log_output = self.log_capture.getvalue()
        self.assertNotIn("Naive datetime", log_output)
        
        # Check datetime is timezone-aware
        self.assertIsNotNone(dt.tzinfo)
        self.assertEqual(dt.tzinfo, STORAGE_TIMEZONE)


class TestTimezoneConversions(unittest.TestCase):
    """Test timezone conversion functionality."""
    
    def test_utc_to_pst_conversion(self):
        """Test converting UTC to PST/PDT."""
        # Create a UTC datetime
        utc_dt = parse_datetime_assume_utc('2025-06-27 15:00:00', '%Y-%m-%d %H:%M:%S')
        
        # Convert to PST
        pst_dt = ensure_pst(utc_dt)
        
        # In June, PST is actually PDT (UTC-7)
        expected_hour = 8  # 15:00 UTC = 08:00 PDT
        self.assertEqual(pst_dt.hour, expected_hour)
        self.assertEqual(pst_dt.tzinfo.zone, 'America/Los_Angeles')
    
    def test_pst_to_utc_conversion(self):
        """Test converting PST/PDT to UTC."""
        # Create a PST datetime
        pst_dt = APP_TIMEZONE.localize(datetime(2025, 6, 27, 8, 0, 0))
        
        # Convert to UTC
        utc_dt = ensure_utc(pst_dt)
        
        # In June, PST is actually PDT (UTC-7)
        expected_hour = 15  # 08:00 PDT = 15:00 UTC
        self.assertEqual(utc_dt.hour, expected_hour)
        self.assertEqual(utc_dt.tzinfo, STORAGE_TIMEZONE)
    
    def test_daylight_saving_transition(self):
        """Test timezone conversion around daylight saving transitions."""
        # Test date in winter (PST, UTC-8)
        winter_utc = parse_datetime_assume_utc('2025-01-15 20:00:00', '%Y-%m-%d %H:%M:%S')
        winter_pst = ensure_pst(winter_utc)
        self.assertEqual(winter_pst.hour, 12)  # 20:00 UTC = 12:00 PST
        
        # Test date in summer (PDT, UTC-7)
        summer_utc = parse_datetime_assume_utc('2025-07-15 20:00:00', '%Y-%m-%d %H:%M:%S')
        summer_pdt = ensure_pst(summer_utc)
        self.assertEqual(summer_pdt.hour, 13)  # 20:00 UTC = 13:00 PDT


class TestTimeSensitiveFeatures(unittest.TestCase):
    """Test that time-sensitive features work correctly with timezone-aware datetimes."""
    
    def test_time_window_filtering(self):
        """Test filtering items by time window."""
        from sources.x.time_utils import is_tweet_in_time_window
        
        # Create timezone-aware datetimes
        tweet_time = parse_datetime_assume_utc('2025-06-27 10:00:00', '%Y-%m-%d %H:%M:%S')
        start_time = parse_datetime_assume_utc('2025-06-27 09:00:00', '%Y-%m-%d %H:%M:%S')
        end_time = parse_datetime_assume_utc('2025-06-27 11:00:00', '%Y-%m-%d %H:%M:%S')
        
        # Tweet should be in window
        self.assertTrue(is_tweet_in_time_window(tweet_time, start_time, end_time))
        
        # Tweet before window
        early_tweet = parse_datetime_assume_utc('2025-06-27 08:00:00', '%Y-%m-%d %H:%M:%S')
        self.assertFalse(is_tweet_in_time_window(early_tweet, start_time, end_time))
        
        # Tweet after window
        late_tweet = parse_datetime_assume_utc('2025-06-27 12:00:00', '%Y-%m-%d %H:%M:%S')
        self.assertFalse(is_tweet_in_time_window(late_tweet, start_time, end_time))
    
    def test_week_boundary_calculation(self):
        """Test weekly boundary calculations with timezone-aware datetimes."""
        from core.week_boundary_utils import get_current_week_start, get_next_week_cutoff, is_in_current_week
        
        # Get current week start
        week_start = get_current_week_start()
        
        # Should be timezone-aware
        self.assertIsNotNone(week_start.tzinfo)
        
        # Should be in PST/PDT (US/Pacific is the actual zone name used)
        self.assertEqual(week_start.tzinfo.zone, 'US/Pacific')
        
        # Should be Thursday at midnight
        self.assertEqual(week_start.weekday(), 3)  # Thursday
        self.assertEqual(week_start.hour, 0)
        self.assertEqual(week_start.minute, 0)
        self.assertEqual(week_start.second, 0)
        
        # Get next week cutoff
        next_cutoff = get_next_week_cutoff()
        self.assertIsNotNone(next_cutoff.tzinfo)
        self.assertEqual(next_cutoff.tzinfo.zone, 'US/Pacific')
        
        # Should be exactly 7 days after week start
        diff = (next_cutoff - week_start).days
        self.assertEqual(diff, 7)


class TestEndToEndScenarios(unittest.TestCase):
    """Test complete data flow scenarios."""
    
    def test_data_ingestion_to_storage_flow(self):
        """Test complete flow from data ingestion to storage."""
        # Simulate data ingestion
        ingested_time = parse_datetime_assume_utc('2025-06-27 10:30:00', '%Y-%m-%d %H:%M:%S')
        fetched_time = now_utc()
        
        # Both should be UTC timezone-aware
        self.assertEqual(ingested_time.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(fetched_time.tzinfo, STORAGE_TIMEZONE)
        
        # Simulate storage (would normally go to database)
        stored_data = {
            'published_at': ingested_time.isoformat(),
            'fetched_at': fetched_time.isoformat()
        }
        
        # Simulate retrieval and display conversion
        from core.timezone_config import ensure_utc
        retrieved_published = ensure_utc(datetime.fromisoformat(stored_data['published_at']))
        retrieved_fetched = ensure_utc(datetime.fromisoformat(stored_data['fetched_at']))
        
        # Convert to PST for display
        display_published = ensure_pst(retrieved_published)
        display_fetched = ensure_pst(retrieved_fetched)
        
        # All should have proper timezones
        self.assertEqual(retrieved_published.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(retrieved_fetched.tzinfo, STORAGE_TIMEZONE)
        self.assertEqual(display_published.tzinfo.zone, 'America/Los_Angeles')
        self.assertEqual(display_fetched.tzinfo.zone, 'America/Los_Angeles')


def run_all_tests():
    """Run all timezone tests and provide summary."""
    print("=" * 70)
    print("COMPREHENSIVE TIMEZONE FIX TEST SUITE")
    print("=" * 70)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestTimezoneWarnings,
        TestTimezoneFunctions,
        TestDataSourceIntegration,
        TestTimezoneConversions,
        TestTimeSensitiveFeatures,
        TestEndToEndScenarios
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Summary
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("\n✅ ALL TESTS PASSED! Timezone fix is working correctly.")
    else:
        print("\n❌ Some tests failed. Please review the errors above.")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)