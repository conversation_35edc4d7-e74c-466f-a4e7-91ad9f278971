#!/usr/bin/env python3
"""
Verify that the weekly compilation contains all items from the source page.
"""

import logging
import argparse
from typing import Dict, List, Set, Tuple
from notion_client import Client

from sources.notion.notion_publisher import get_notion_client
from sources.notion.notion_utils import format_notion_uuid
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.create_weekly_compilation import fetch_all_blocks, get_block_text
from core.storage.publication_state_pg import get_current_weekly_page_id

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def extract_all_toggles(blocks: List[Dict]) -> Set[str]:
    """Extract all toggle block texts for comparison."""
    toggles = set()
    
    for block in blocks:
        if block.get('type') == 'toggle':
            toggle_text = get_block_text(block)
            if toggle_text:
                toggles.add(toggle_text)
    
    return toggles


def count_items_by_category(blocks: List[Dict]) -> Dict[str, int]:
    """Count items in each category."""
    counts = {
        'time_sensitive': 0,
        'relevant': 0,
        'needs_review': 0,
        'not_relevant': 0,
        'uncategorized': 0
    }
    
    current_category = 'uncategorized'
    
    for block in blocks:
        block_type = block.get('type')
        
        if block_type == 'heading_3':
            header_text = get_block_text(block)
            
            if '🚨 Time-Sensitive Items' in header_text:
                current_category = 'time_sensitive'
            elif '✅' in header_text and 'Relevant' in header_text:
                current_category = 'relevant'
            elif '🔍' in header_text and 'Review' in header_text:
                current_category = 'needs_review'
            elif '❌' in header_text and 'Not Relevant' in header_text:
                current_category = 'not_relevant'
        
        elif block_type == 'toggle' and current_category in counts:
            counts[current_category] += 1
    
    return counts


def verify_compilation(source_page_id: str, compilation_page_id: str) -> None:
    """Verify compilation completeness and accuracy."""
    try:
        client = get_notion_client()
        if not client:
            logger.error("Failed to create Notion client")
            return
        
        # Format page IDs
        source_page_id = format_notion_uuid(source_page_id)
        compilation_page_id = format_notion_uuid(compilation_page_id)
        
        logger.info("Fetching source page blocks...")
        source_blocks = fetch_all_blocks(client, source_page_id)
        
        logger.info("Fetching compilation page blocks...")
        compilation_blocks = fetch_all_blocks(client, compilation_page_id)
        
        # Extract all toggles
        source_toggles = extract_all_toggles(source_blocks)
        compilation_toggles = extract_all_toggles(compilation_blocks)
        
        # Count by category
        source_counts = count_items_by_category(source_blocks)
        compilation_counts = count_items_by_category(compilation_blocks)
        
        # Report results
        logger.info("\n=== VERIFICATION REPORT ===")
        
        logger.info(f"\nTotal unique items:")
        logger.info(f"  Source page: {len(source_toggles)} items")
        logger.info(f"  Compilation: {len(compilation_toggles)} items")
        
        logger.info(f"\nItems by category in source:")
        for category, count in source_counts.items():
            if count > 0:
                logger.info(f"  {category}: {count}")
        
        logger.info(f"\nItems by category in compilation:")
        for category, count in compilation_counts.items():
            if count > 0:
                logger.info(f"  {category}: {count}")
        
        # Check for missing items
        missing = source_toggles - compilation_toggles
        if missing:
            logger.warning(f"\n⚠️  MISSING ITEMS: {len(missing)} items not found in compilation:")
            for item in list(missing)[:5]:  # Show first 5
                logger.warning(f"  - {item[:100]}...")
            if len(missing) > 5:
                logger.warning(f"  ... and {len(missing) - 5} more")
        
        # Check for extra items (shouldn't happen)
        extra = compilation_toggles - source_toggles
        if extra:
            logger.warning(f"\n⚠️  EXTRA ITEMS: {len(extra)} items in compilation not in source:")
            for item in list(extra)[:5]:
                logger.warning(f"  - {item[:100]}...")
        
        if not missing and not extra:
            logger.info("\n✅ SUCCESS: All items accounted for!")
        
        # Check for duplicates in source
        source_toggle_list = []
        for block in source_blocks:
            if block.get('type') == 'toggle':
                toggle_text = get_block_text(block)
                if toggle_text:
                    source_toggle_list.append(toggle_text)
        
        duplicates = len(source_toggle_list) - len(source_toggles)
        if duplicates > 0:
            logger.info(f"\n📋 Found {duplicates} duplicate items in source (correctly deduplicated in compilation)")
        
    except Exception as e:
        logger.error(f"Error during verification: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Verify weekly compilation completeness')
    parser.add_argument('source_page_id', help='Source weekly page ID')
    parser.add_argument('compilation_page_id', help='Compilation page ID to verify')
    
    args = parser.parse_args()
    
    verify_compilation(args.source_page_id, args.compilation_page_id)


if __name__ == "__main__":
    main()