"""
Test script for verifying the import structure.

This script tests that all the necessary modules can be imported correctly.
"""

from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_imports():
    """Test that all necessary modules can be imported."""
    logger.info("Testing core imports...")
    
    # Test core imports
    from core.models import NewsItem, TweetItem
    from core.config import X_BEARER_TOKEN, X_LIST_ID
    from core.storage import init_db, save_news_items
    
    logger.info("Core imports successful")
    
    logger.info("Testing sources imports...")
    
    # Test sources imports
    from sources.x.api_client import XApiClient
    from sources.x.state_manager import XStateManager
    from sources.x.tweet_processor import XTweetProcessor
    from sources.x.collector import XCollector
    
    logger.info("Sources imports successful")
    
    return True

if __name__ == "__main__":
    success = test_imports()
    if success:
        logger.info("All imports tested successfully")
    else:
        logger.error("Import test failed")
