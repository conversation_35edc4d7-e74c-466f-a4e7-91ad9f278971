#!/usr/bin/env python3
"""
Test script for the Google News RSS feed functionality.
This script tests only the RSS feed fetching and parsing, without database storage or LLM components.
"""

import sys
from pprint import pprint
from core.config import KEYWORDS
from sources.google_rss import fetch_all_keywords
from core.utils.logging import get_logger

logger = get_logger(__name__)

def main():
    """Test the Google News RSS feed functionality using our modules."""
    logger.info("Testing Google News RSS feed functionality")
    
    # Fetch news from Google News RSS using our module
    logger.info(f"Fetching news for keywords: {', '.join(KEYWORDS)}")
    news_items = fetch_all_keywords(KEYWORDS, items_per_keyword=3)  # Limit to 3 items per keyword for testing
    
    if not news_items:
        logger.warning("No news items fetched. Exiting.")
        return
    
    # Display results
    print(f"\n=== Retrieved {len(news_items)} unique news items ===\n")
    
    for i, item in enumerate(news_items, 1):
        print(f"Item {i}:")
        print(f"Title: {item.title}")
        print(f"Publisher: {item.publisher}")
        print(f"Published: {item.published_at}")
        print(f"Link: {item.link}")
        print(f"Summary: {item.summary[:150]}..." if len(item.summary) > 150 else f"Summary: {item.summary}")
        print("-" * 80)
    
    # Print raw data of first item for inspection
    if news_items:
        print("\n=== Raw Pydantic model of first item ===")
        pprint(news_items[0].model_dump())
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()