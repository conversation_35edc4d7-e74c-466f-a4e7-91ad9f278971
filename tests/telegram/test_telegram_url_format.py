"""Unit tests for Telegram URL formatting."""

import unittest
from datetime import datetime
from sources.telegram.telegram_scraper import message_to_news_item
from core.timezone_config import now_utc


class TestTelegramURLFormat(unittest.TestCase):
    """Test cases for Telegram message URL formatting."""
    
    def test_channel_with_at_symbol(self):
        """Test that @ symbol is stripped from channel name in URLs."""
        message = {
            'id': 2470,
            'text': 'Test message with a link https://example.com/article',
            'date': datetime.now(),
            'channel': '@RWAxyzNewswire'
        }
        
        news_item = message_to_news_item(message)
        
        # URL should not contain @ symbol
        self.assertEqual(str(news_item.link), 'https://t.me/RWAxyzNewswire/2470')
        self.assertNotIn('@', str(news_item.link))
    
    def test_channel_without_at_symbol(self):
        """Test that channel names without @ work correctly."""
        message = {
            'id': 123,
            'text': 'Another test message',
            'date': datetime.now(),
            'channel': 'TestChannel'
        }
        
        news_item = message_to_news_item(message)
        
        # URL should be correctly formatted
        self.assertEqual(str(news_item.link), 'https://t.me/TestChannel/123')
    
    def test_channel_with_multiple_at_symbols(self):
        """Test edge case with multiple @ symbols."""
        message = {
            'id': 456,
            'text': 'Edge case test',
            'date': datetime.now(),
            'channel': '@@WeirdChannel'
        }
        
        news_item = message_to_news_item(message)
        
        # All leading @ symbols should be stripped
        self.assertEqual(str(news_item.link), 'https://t.me/WeirdChannel/456')
    
    def test_publisher_format(self):
        """Test that publisher field preserves @ symbol."""
        message = {
            'id': 789,
            'text': 'Publisher test',
            'date': datetime.now(),
            'channel': '@RWAxyzNewswire'
        }
        
        news_item = message_to_news_item(message)
        
        # Publisher should still show @ symbol for display
        self.assertEqual(news_item.publisher, 'Telegram: @RWAxyzNewswire')


if __name__ == '__main__':
    unittest.main()