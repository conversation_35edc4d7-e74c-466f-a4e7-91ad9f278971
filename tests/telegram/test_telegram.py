import pytest
from core.utils.logging import get_logger
from sources.telegram.telegram_scraper import fetch_telegram_news

logger = get_logger(__name__)

@pytest.mark.integration
@pytest.mark.slow
def test_telegram_scraper():
    """Test the Telegram scraper functionality."""
    logger.info("Testing Telegram scraper...")
    
    # Fetch news from Telegram
    items = fetch_telegram_news()
    
    if not items:
        logger.warning("No items fetched from Telegram.")
        return
    
    # Print results
    logger.info(f"Successfully fetched {len(items)} items from Telegram channels.")
    
    print("\n=== Telegram News Items ===")
    for i, item in enumerate(items[:5], 1):  # Show first 5 items
        print(f"\n{i}. {item.title}")
        print(f"Source: {item.publisher}")
        print(f"Published: {item.published_at.strftime('%Y-%m-%d %H:%M')}")
        print(f"Link: {item.link}")
        print(f"Summary: {item.summary[:150]}...")
    
    if len(items) > 5:
        print(f"\n... and {len(items) - 5} more items")
    
    print("\n=== Test Complete ===")

# Remove main() call - pytest will handle test execution 