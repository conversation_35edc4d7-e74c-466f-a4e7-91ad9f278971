#!/usr/bin/env python3
"""
Test script for Telegram message truncation.

This script tests the improved message truncation logic in the Telegram scraper.
"""

from datetime import datetime

from sources.telegram.telegram_scraper import message_to_news_item
from core.models import NewsItem
from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_message_truncation():
    """Test the simple message truncation logic using Telegram's maximum limit."""
    print("\n=== Testing Telegram Message Truncation ===\n")

    # Telegram's maximum message length
    TELEGRAM_MAX_LENGTH = 4096

    # Test cases with different message lengths
    test_cases = [
        {
            'name': 'Short message (no truncation)',
            'text': 'This is a short message that should not be truncated.',
            'expected_truncated': False
        },
        {
            'name': 'Medium message (no truncation)',
            'text': 'This is a medium length message. ' * 50,  # About 1500 characters
            'expected_truncated': False
        },
        {
            'name': 'Message at limit (no truncation)',
            'text': 'X' * TELEGRAM_MAX_LENGTH,
            'expected_truncated': False
        },
        {
            'name': 'Message exceeding limit (truncation)',
            'text': 'X' * (TELEGRAM_MAX_LENGTH + 100),  # Exceeds limit by 100 characters
            'expected_truncated': True
        },
        {
            'name': 'Very long message (truncation)',
            'text': 'This is a very long message. ' * 500,  # About 15000 characters
            'expected_truncated': True
        }
    ]

    for i, case in enumerate(test_cases):
        print(f"\n[Test {i+1}] {case['name']}")

        # Create a mock message
        message = {
            'id': 12345,
            'text': case['text'],
            'date': datetime.now(),
            'channel': 'test_channel'
        }

        # Convert to NewsItem
        news_item = message_to_news_item(message)

        # Check if truncation happened as expected
        # We need to check if the truncation message is present, not just length
        # This is because there might be small differences in whitespace handling
        truncation_msg = '... [Message continues. Click the link to read the full message.]'
        is_truncated = truncation_msg in news_item.summary
        print(f"Original length: {len(case['text'])} characters")
        print(f"Summary length: {len(news_item.summary)} characters")
        print(f"Truncation message present: {is_truncated}")

        # Print additional information about the summary

        # Print a sample of the summary
        print("\nSummary preview:")
        preview_length = min(200, len(news_item.summary))
        print(f"{news_item.summary[:preview_length]}...")

        if is_truncated:
            print("\nEnd of summary:")
            end_preview_length = min(100, len(news_item.summary))
            print(f"...{news_item.summary[-end_preview_length:]}")

        # Verify expectations
        assert is_truncated == case['expected_truncated'], f"Expected truncated={case['expected_truncated']}, got {is_truncated}"

        if is_truncated:
            assert '... [Message continues' in news_item.summary, "Truncation message not included in summary"

        # Verify that messages are truncated at exactly the right length
        if is_truncated:
            # The summary should be the max length plus the length of the truncation message
            truncation_msg = '... [Message continues. Click the link to read the full message.]'
            expected_length = TELEGRAM_MAX_LENGTH + len(truncation_msg)
            assert len(news_item.summary) == expected_length, f"Expected length {expected_length}, got {len(news_item.summary)}"

    print("\n=== All tests passed! ===")
    return True

if __name__ == "__main__":
    test_message_truncation()
