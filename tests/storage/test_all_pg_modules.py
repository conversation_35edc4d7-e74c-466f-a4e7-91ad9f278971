#!/usr/bin/env python3
"""
Comprehensive test for all PostgreSQL storage modules.
Tests that all modules work together correctly.
"""
import sys
import os
from datetime import datetime, timezone, timedelta

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import all PostgreSQL modules
from core.storage.db_init_pg import init_db
from core.storage.daily_runs_pg import start_run, complete_run, get_last_successful_run_timestamp
from core.storage.publication_state_pg import update_publication_timestamp, update_weekly_page_id, is_new_week
from core.storage.news_items_pg import save_news_items, get_recent_items as get_recent_news, update_item_relevance
from core.storage.tweet_items_pg import save_tweet_items, get_recent_tweets, update_tweet_relevance
from core.storage.x_collection_pg import initialize_collection_state, update_collection_state, get_active_collection

# Import models
from core.models import NewsItem, TweetItem
from core.enums import RelevanceStatus
from pydantic import HttpUrl

def test_database_init():
    """Test database initialization."""
    print("1. Testing database initialization...")
    if init_db():
        print("✅ Database initialized successfully")
        return True
    else:
        print("❌ Database initialization failed")
        return False

def test_daily_runs():
    """Test daily runs functionality."""
    print("\n2. Testing daily runs module...")
    
    # Start a run
    run_id = start_run('test')
    if not run_id:
        print("❌ Failed to start run")
        return False
    print(f"✅ Started run ID: {run_id}")
    
    # Complete the run
    if not complete_run(run_id, items_fetched=5, items_published=3):
        print("❌ Failed to complete run")
        return False
    print("✅ Completed run")
    
    # Get last successful timestamp
    last_ts = get_last_successful_run_timestamp()
    print(f"✅ Last successful run: {last_ts}")
    
    return True

def test_publication_state():
    """Test publication state functionality."""
    print("\n3. Testing publication state module...")
    
    # Update publication timestamp
    if not update_publication_timestamp():
        print("❌ Failed to update publication timestamp")
        return False
    print("✅ Updated publication timestamp")
    
    # Update weekly page ID
    test_page_id = "test-notion-page-id-123"
    if not update_weekly_page_id(test_page_id):
        print("❌ Failed to update weekly page ID")
        return False
    print(f"✅ Updated weekly page ID: {test_page_id}")
    
    # Check if new week
    new_week = is_new_week()
    print(f"✅ Is new week: {new_week}")
    
    return True

def test_news_items():
    """Test news items functionality."""
    print("\n4. Testing news items module...")
    
    # Create test news items
    items = []
    for i in range(3):
        item = NewsItem(
            title=f"Test News {i+1}",
            link=f"https://example.com/news/{i+1}",
            published_at=datetime.now(timezone.utc) - timedelta(hours=i),
            summary=f"Test summary for news item {i+1}",
            publisher=f"Test Publisher {i+1}",
            source="test_source",
            fetched_at=datetime.now(timezone.utc)
        )
        items.append(item)
    
    # Save items
    saved = save_news_items(items)
    print(f"✅ Saved {saved} news items")
    
    # Get recent items
    recent = get_recent_news(limit=5)
    print(f"✅ Retrieved {len(recent)} recent news items")
    
    # Update relevance
    if recent:
        success = update_item_relevance(recent[0].link, RelevanceStatus.RELEVANT)
        print(f"✅ Updated relevance: {success}")
    
    return True

def test_tweet_items():
    """Test tweet items functionality."""
    print("\n5. Testing tweet items module...")
    
    # Create test tweets
    tweets = []
    for i in range(3):
        tweet = TweetItem(
            tweet_id=f"test_tweet_{i+1}_{datetime.now().timestamp()}",
            text=f"Test tweet content {i+1}",
            created_at=datetime.now(timezone.utc) - timedelta(hours=i),
            fetched_at=datetime.now(timezone.utc),
            author_id=f"test_author_{i+1}",
            author_username=f"testuser{i+1}",
            author_name=f"Test User {i+1}",
            source="test_source"
        )
        tweets.append(tweet)
    
    # Save tweets
    saved = save_tweet_items(tweets)
    print(f"✅ Saved {saved} tweets")
    
    # Get recent tweets
    recent = get_recent_tweets(limit=5)
    print(f"✅ Retrieved {len(recent)} recent tweets")
    
    # Update relevance
    if recent:
        success = update_tweet_relevance(recent[0].tweet_id, RelevanceStatus.RELEVANT)
        print(f"✅ Updated tweet relevance: {success}")
    
    return True

def test_x_collection():
    """Test X collection state functionality."""
    print("\n6. Testing X collection state module...")
    
    # Initialize collection
    start = datetime.now(timezone.utc) - timedelta(hours=1)
    end = datetime.now(timezone.utc)
    collection_id = initialize_collection_state(start, end)
    if not collection_id:
        print("❌ Failed to initialize collection")
        return False
    print(f"✅ Initialized collection: {collection_id}")
    
    # Update collection state
    success = update_collection_state(
        collection_id,
        tweets_collected=10,
        requests_made=2,
        pagination_token="next_page_token"
    )
    print(f"✅ Updated collection state: {success}")
    
    # Get active collection
    active = get_active_collection()
    if active:
        print(f"✅ Found active collection: {active['collection_id']}")
        
        # Complete the collection
        update_collection_state(collection_id, status='complete')
        print("✅ Completed collection")
    
    return True

def test_integration():
    """Test integration between modules."""
    print("\n7. Testing integration between modules...")
    
    # Simulate a full collection cycle
    run_id = start_run('integration_test')
    if not run_id:
        print("❌ Failed to start integration test run")
        return False
    
    # Collect some data
    news_saved = save_news_items([
        NewsItem(
            title="Integration Test News",
            link=f"https://example.com/integration/{datetime.now().timestamp()}",
            published_at=datetime.now(timezone.utc),
            summary="Integration test news item",
            publisher="Test Publisher",
            source="integration_test",
            fetched_at=datetime.now(timezone.utc),
            relevance=RelevanceStatus.RELEVANT,
            is_time_sensitive=True,
            timeliness_reason="Test deadline tomorrow"
        )
    ])
    
    tweets_saved = save_tweet_items([
        TweetItem(
            tweet_id=f"integration_tweet_{datetime.now().timestamp()}",
            text="Integration test tweet",
            created_at=datetime.now(timezone.utc),
            fetched_at=datetime.now(timezone.utc),
            author_id="test_author",
            author_username="testuser",
            author_name="Test User",
            source="integration_test",
            relevance=RelevanceStatus.RELEVANT
        )
    ])
    
    # Complete the run
    complete_run(run_id, items_fetched=news_saved + tweets_saved, items_published=news_saved + tweets_saved)
    
    # Update publication state
    update_publication_timestamp()
    
    print(f"✅ Integration test completed: {news_saved} news + {tweets_saved} tweets")
    
    return True

def main():
    """Run all tests."""
    print("=== PostgreSQL Storage Modules Test Suite ===\n")
    
    # Check if database URL is set
    db_url = os.getenv("SUPABASE_DB_URL")
    if not db_url:
        print("❌ SUPABASE_DB_URL not set in environment")
        return
    
    tests = [
        ("Database Init", test_database_init),
        ("Daily Runs", test_daily_runs),
        ("Publication State", test_publication_state),
        ("News Items", test_news_items),
        ("Tweet Items", test_tweet_items),
        ("X Collection", test_x_collection),
        ("Integration", test_integration)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} test failed")
        except Exception as e:
            print(f"\n❌ {test_name} test error: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"Tests passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("\n✅ All PostgreSQL modules are working correctly!")
        print("\nNext steps:")
        print("1. Update imports in main code to use _pg modules")
        print("2. Run integration tests with real workflow")
        print("3. Deploy to production")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()