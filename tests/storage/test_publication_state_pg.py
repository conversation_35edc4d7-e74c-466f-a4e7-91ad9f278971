#!/usr/bin/env python3
"""
Test PostgreSQL version of publication_state module
"""
import sys
import os
from datetime import datetime, timedelta
import pytz

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import PostgreSQL version
from core.storage.publication_state_pg import (
    init_publication_state,
    is_new_week,
    get_current_weekly_page_id,
    update_weekly_page_id,
    update_publication_timestamp,
    get_last_publication_timestamp,
    PACIFIC_TZ
)

def test_init():
    """Test table initialization/verification."""
    print("Testing init_publication_state()...")
    result = init_publication_state()
    print(f"  Table verification: {'✅ Success' if result else '❌ Failed'}")
    return result

def test_publication_timestamp():
    """Test publication timestamp operations."""
    print("\nTesting publication timestamp operations...")
    
    # Update with current time
    test_time = datetime.now()
    success = update_publication_timestamp(test_time)
    print(f"  Update timestamp: {'✅ Success' if success else '❌ Failed'}")
    
    # Retrieve and verify
    retrieved = get_last_publication_timestamp()
    if retrieved:
        # Compare timestamps (allow 1 second difference for processing)
        diff = abs((retrieved - test_time).total_seconds())
        matches = diff < 1
        print(f"  Retrieve timestamp: {'✅ Matches' if matches else '❌ Mismatch'}")
        print(f"    Stored: {test_time}")
        print(f"    Retrieved: {retrieved}")
    else:
        print("  Retrieve timestamp: ❌ Failed to retrieve")
        return False
    
    return success and matches

def test_weekly_page_operations():
    """Test weekly page ID operations."""
    print("\nTesting weekly page operations...")
    
    # Test page ID
    test_page_id = "test-page-123456789"
    
    # Calculate this week's Thursday
    now = datetime.now(PACIFIC_TZ)
    days_since_thursday = (now.weekday() - 3) % 7
    this_thursday = (now - timedelta(days=days_since_thursday)).replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    
    # Update page ID
    success = update_weekly_page_id(test_page_id, this_thursday)
    print(f"  Update page ID: {'✅ Success' if success else '❌ Failed'}")
    
    # Retrieve and verify
    retrieved_id = get_current_weekly_page_id()
    matches = retrieved_id == test_page_id
    print(f"  Retrieve page ID: {'✅ Matches' if matches else '❌ Mismatch'}")
    if not matches:
        print(f"    Expected: {test_page_id}")
        print(f"    Got: {retrieved_id}")
    
    # Test is_new_week (should be False since we just set current week)
    new_week = is_new_week()
    print(f"  is_new_week(): {'❌ Should be False' if new_week else '✅ Correctly False'}")
    
    # Test with last week's date
    last_week = this_thursday - timedelta(days=7)
    update_weekly_page_id("old-page-id", last_week)
    new_week_after = is_new_week()
    print(f"  is_new_week() after old date: {'✅ Correctly True' if new_week_after else '❌ Should be True'}")
    
    return success and matches and not new_week and new_week_after

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\nTesting edge cases...")
    
    # Test with None values
    try:
        # Should use current time
        success = update_publication_timestamp(None)
        print(f"  Update with None timestamp: {'✅ Success' if success else '❌ Failed'}")
    except Exception as e:
        print(f"  Update with None timestamp: ❌ Error: {e}")
        success = False
    
    # Test empty database scenario
    # (This is simulated by checking behavior when no data exists)
    
    return success

def main():
    """Run all tests."""
    print("=== PostgreSQL Publication State Test Suite ===\n")
    
    # Check if database URL is set
    db_url = os.getenv("SUPABASE_DB_URL")
    if not db_url:
        print("❌ SUPABASE_DB_URL not set in environment")
        return
    
    # Run tests
    tests = [
        ("Initialization", test_init),
        ("Publication Timestamp", test_publication_timestamp),
        ("Weekly Page Operations", test_weekly_page_operations),
        ("Edge Cases", test_edge_cases)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} test failed")
        except Exception as e:
            print(f"\n❌ {test_name} test error: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"Tests passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("\n✅ All tests passed! PostgreSQL publication_state module is working correctly.")
        print("\nNext steps:")
        print("1. Replace imports in main code to use publication_state_pg")
        print("2. Run integration tests")
        print("3. Proceed with other storage modules")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()