#!/usr/bin/env python3
"""
Test PostgreSQL utility functions
"""
import pytest
from datetime import datetime

from core.storage.pg_utils import (
    get_connection, execute_query, fetch_all, fetch_one,
    execute_many, insert_returning, table_exists, 
    adapt_sqlite_query, row_to_dict
)

def test_connection():
    """Test basic connection."""
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()
        assert version is not None
        assert 'version' in version

def test_table_exists():
    """Test table existence check."""
    # Check known tables
    tables = ['news_items', 'tweet_items', 'publication_state', 'x_collection_state']
    for table in tables:
        assert table_exists(table), f"Table {table} should exist"
    
    # Check non-existent table
    assert not table_exists('fake_table_xyz'), "Non-existent table should return False"

def test_fetch_operations():
    """Test fetch operations."""
    # Test fetch_all
    items = fetch_all("SELECT * FROM news_items LIMIT 5")
    assert isinstance(items, list), "fetch_all should return a list"
    assert len(items) <= 5, "Should return at most 5 items"
    
    # Test fetch_one
    item = fetch_one("SELECT COUNT(*) as count FROM news_items")
    assert item is not None, "fetch_one should return a result"
    assert 'count' in item, "Result should have count field"
    assert item['count'] >= 0, "Count should be non-negative"
    
    # Test with parameters
    recent = fetch_all(
        "SELECT * FROM news_items WHERE fetched_at > %s LIMIT 3",
        (datetime(2025, 1, 1),)
    )
    assert isinstance(recent, list), "fetch_all with params should return a list"

def test_insert_operations():
    """Test insert operations."""
    
    # Create a test table
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_pg_utils (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                value INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    # Test single insert with RETURNING
    test_id = insert_returning(
        "INSERT INTO test_pg_utils (name, value) VALUES (%s, %s)",
        ("test_item", 42)
    )
    assert test_id is not None, "insert_returning should return an ID"
    assert isinstance(test_id, int), "ID should be an integer"
    
    # Test execute_many
    test_data = [
        ("batch_1", 10),
        ("batch_2", 20),
        ("batch_3", 30)
    ]
    rows = execute_many(
        "INSERT INTO test_pg_utils (name, value) VALUES (%s, %s)",
        test_data
    )
    assert rows == 3, "Should insert 3 rows"
    
    # Verify inserts
    results = fetch_all("SELECT * FROM test_pg_utils ORDER BY id DESC LIMIT 4")
    assert len(results) == 4, "Should find 4 test records"
    
    # Clean up
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("DROP TABLE test_pg_utils")

def test_query_adaptation():
    """Test SQLite to PostgreSQL query adaptation."""
    # This should log warnings about needed adaptations
    sqlite_query = "INSERT OR REPLACE INTO items (id, name) VALUES (?, ?)"
    pg_query = adapt_sqlite_query(sqlite_query)
    assert pg_query != sqlite_query, "Query should be adapted"
    assert "?" not in pg_query, "PostgreSQL query should not contain ?"
    assert "%s" in pg_query, "PostgreSQL query should contain %s"

def test_row_conversion():
    """Test row to dict conversion."""
    # Fetch a row and convert
    row = fetch_one("SELECT 1 as id, 'test' as name, true as active")
    dict_result = row_to_dict(row)
    assert isinstance(dict_result, dict), "Should return a dictionary"
    assert dict_result.get('id') == 1, "ID should be 1"
    assert dict_result.get('name') == 'test', "Name should be 'test'"
    assert dict_result.get('active') is True, "Active should be True"