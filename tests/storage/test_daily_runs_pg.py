#!/usr/bin/env python3
"""
Test PostgreSQL version of daily_runs module
"""
import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.storage.daily_runs_pg import (
    init_daily_runs,
    start_run,
    complete_run,
    fail_run,
    get_recent_runs,
    get_last_successful_run,
    get_last_successful_run_timestamp
)

def test_all():
    """Run all tests."""
    print("=== PostgreSQL Daily Runs Test ===\n")
    
    # Initialize table
    print("1. Initializing table...")
    if not init_daily_runs():
        print("❌ Failed to initialize table")
        return
    print("✅ Table initialized")
    
    # Start a run
    print("\n2. Starting a run...")
    run_id = start_run('test')
    if not run_id:
        print("❌ Failed to start run")
        return
    print(f"✅ Started run with ID: {run_id}")
    
    # Complete the run
    print("\n3. Completing the run...")
    if not complete_run(run_id, items_fetched=10, items_published=8, notion_page_id="test-page-123"):
        print("❌ Failed to complete run")
        return
    print("✅ Run completed")
    
    # Start and fail a run
    print("\n4. Testing failed run...")
    fail_id = start_run('test')
    if fail_id and fail_run(fail_id, "Test error message"):
        print("✅ Failed run recorded")
    else:
        print("❌ Failed to record failed run")
    
    # Get recent runs
    print("\n5. Getting recent runs...")
    recent = get_recent_runs(5)
    print(f"✅ Found {len(recent)} recent runs")
    for run in recent[:2]:  # Show first 2
        print(f"   - Run {run['id']}: {run['status']} ({run['run_type']})")
    
    # Get last successful run
    print("\n6. Getting last successful run...")
    last_run = get_last_successful_run()
    if last_run:
        print(f"✅ Last successful run: ID {last_run['id']}, fetched {last_run['items_fetched']} items")
    else:
        print("❌ No successful run found")
    
    # Get last successful timestamp
    print("\n7. Getting last successful timestamp...")
    timestamp = get_last_successful_run_timestamp()
    print(f"✅ Last successful timestamp: {timestamp}")
    
    print("\n✅ All tests passed!")

if __name__ == "__main__":
    test_all()