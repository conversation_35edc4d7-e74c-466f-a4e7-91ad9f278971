#!/usr/bin/env python3
"""
Test script for email functionality only (no AI calls).
Creates a dummy Notion page with time-sensitive items and sends test email.
"""

import pytest
from datetime import datetime, timezone
from core.models import NewsItem
from core.enums import RelevanceStatus
from core.email_alerts import send_email_alert
from core.utils.logging import get_logger
from sources.notion.notion_publisher import get_notion_client, append_to_existing_page
from sources.notion.subpage_creator import create_timestamped_subpage
from core.config import NOTION_PAGE_ID

logger = get_logger(__name__)

def create_dummy_time_sensitive_items():
    """Create dummy news items that are marked as time-sensitive."""
    items = [
        NewsItem(
            title="URGENT: Polygon Announces Emergency Network Upgrade Tomorrow",
            link="https://example.com/polygon-upgrade",
            published_at=datetime.now(timezone.utc),
            summary="Polygon has announced an emergency network upgrade scheduled for tomorrow at 14:00 UTC. All validators must update their nodes before the deadline.",
            publisher="Polygon Official",
            source="test",
            relevance=RelevanceStatus.RELEVANT,
            is_time_sensitive=True,
            timeliness_reason="Network upgrade happening tomorrow - validators must act within 24 hours"
        ),
        NewsItem(
            title="Circle Opening USDC Minting for 48 Hours Only",
            link="https://example.com/circle-minting",
            published_at=datetime.now(timezone.utc),
            summary="Circle announced a limited 48-hour window for institutional partners to mint USDC at reduced fees. Applications close Friday at midnight EST.",
            publisher="Circle Blog",
            source="test",
            relevance=RelevanceStatus.RELEVANT,
            is_time_sensitive=True,
            timeliness_reason="Limited 48-hour minting window closing Friday"
        ),
        NewsItem(
            title="Breaking: SEC Announces Emergency Comment Period on RWA Rules",
            link="https://example.com/sec-comment",
            published_at=datetime.now(timezone.utc),
            summary="The SEC has opened an emergency 72-hour comment period on proposed tokenized securities regulations. Industry feedback needed by end of week.",
            publisher="SEC.gov",
            source="test",
            relevance=RelevanceStatus.RELEVANT,
            is_time_sensitive=True,
            timeliness_reason="SEC comment period closes in 72 hours"
        ),
        # Also add some non-time-sensitive items for comparison
        NewsItem(
            title="BCG Report: RWA Market Analysis Q4 2024",
            link="https://example.com/bcg-report",
            published_at=datetime.now(timezone.utc),
            summary="Boston Consulting Group releases comprehensive analysis of the tokenized asset market, showing 300% year-over-year growth.",
            publisher="BCG",
            source="test",
            relevance=RelevanceStatus.RELEVANT,
            is_time_sensitive=False
        )
    ]
    
    return items

def create_test_notion_page():
    """Create a test Notion page with time-sensitive items."""
    logger.info("Creating test Notion page...")
    
    try:
        client = get_notion_client()
        if not client:
            logger.error("Could not create Notion client")
            return None
            
        # Create a test subpage
        test_page_id = create_timestamped_subpage(
            client, 
            NOTION_PAGE_ID, 
            since_timestamp=datetime.now()
        )
        
        if not test_page_id:
            logger.error("Failed to create test Notion page")
            return None
            
        logger.info(f"Created test Notion page: {test_page_id}")
        
        # Create dummy items
        items = create_dummy_time_sensitive_items()
        
        # Append items to the page
        success = append_to_existing_page(test_page_id, items, datetime.now())
        
        if success:
            logger.info("Successfully populated test Notion page with time-sensitive items")
            return test_page_id
        else:
            logger.error("Failed to populate test Notion page")
            return None
            
    except Exception as e:
        logger.error(f"Error creating test Notion page: {e}")
        return None

@pytest.mark.integration
@pytest.mark.slow
def test_email_with_notion_link():
    """Test email functionality with link to Notion page."""
    logger.info("Testing email notification system...")
    
    # Create test Notion page
    test_page_id = create_test_notion_page()
    
    if not test_page_id:
        logger.error("Could not create test Notion page, aborting email test")
        return
    
    # Create dummy items for email
    items = create_dummy_time_sensitive_items()
    
    # Get time-sensitive items
    time_sensitive_items = [(item, item.timeliness_reason) for item in items if item.is_time_sensitive]
    
    # Create custom test email
    notion_url = f"https://notion.so/{test_page_id.replace('-', '')}"
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .header {{ background-color: #2c3e50; color: white; padding: 20px; text-align: center; }}
            .content {{ padding: 20px; }}
            .test-notice {{ background-color: #fffacd; padding: 15px; border-left: 4px solid #ffd700; margin: 20px 0; }}
            .item {{ margin-bottom: 30px; border-left: 4px solid #3498db; padding-left: 15px; }}
            .item-title {{ font-weight: bold; font-size: 18px; margin-bottom: 5px; }}
            .item-reason {{ background-color: #e8f4f8; padding: 10px; border-radius: 5px; margin: 10px 0; }}
            .button {{ background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚨 TEST: Time-Sensitive TAC News Alert</h1>
            <p>{datetime.now().strftime('%B %d, %Y')}</p>
        </div>
        
        <div class="content">
            <div class="test-notice">
                <strong>⚠️ This is a TEST email</strong><br>
                Testing the time-sensitive alert system. Please confirm receipt and check the Notion page.
            </div>
            
            <p>The following {len(time_sensitive_items)} item(s) have been identified as time-sensitive:</p>
            
            {"".join(f'''
            <div class="item">
                <div class="item-title">{item.title}</div>
                <div class="item-reason">
                    <strong>⏰ Time-sensitive because:</strong> {reason}
                </div>
            </div>
            ''' for item, reason in time_sensitive_items)}
            
            <a href="{notion_url}" class="button">
                View Test Page in Notion →
            </a>
            
            <p><strong>Please verify:</strong></p>
            <ul>
                <li>Email received successfully</li>
                <li>Time-sensitive items are clearly visible</li>
                <li>Notion link works and shows the special time-sensitive section</li>
                <li>Overall formatting looks good</li>
            </ul>
        </div>
    </body>
    </html>
    """
    
    # Send test email
    logger.info("Sending test email...")
    success = send_email_alert(
        subject="🚨 TEST: TAC Time-Sensitive News Alert System",
        html_content=html_content
    )
    
    if success:
        logger.info(f"""
✅ Test email sent successfully!

Please ask your client to:
1. Check their email for the test message
2. Click the Notion link: {notion_url}
3. Verify the time-sensitive section appears at the top
4. Provide feedback on the format

The test Notion page will show:
- 🚨 Time-Sensitive Items section at the top
- Regular daily sections below
- Clear visual indicators for urgent content
        """)
    else:
        logger.error("Failed to send test email")

def main():
    """Run email-only test."""
    import os
    if not os.getenv("EMAIL_ENABLED", "").lower() == "true":
        logger.error("""
Email is not enabled. To run this test:
1. Set up SMTP credentials in your environment
2. Run: EMAIL_ENABLED=true python tests/test_email_only.py
        """)
        return
        
    test_email_with_notion_link()

if __name__ == "__main__":
    main()