"""
Test script for Notion integration.

This script tests the connection to the Notion API and verifies that
we can access and modify the specified page.

Usage:
    python test_notion_integration.py

This is a utility script for testing and troubleshooting the Notion integration.
It allows you to verify that your API token and page ID are correctly configured
before implementing the full integration.
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from notion_client import Client

from core.utils.logging import get_logger

logger = get_logger(__name__)

# Load environment variables
load_dotenv()

def test_notion_connection():
    """Test basic connection to the Notion API."""
    try:
        # Get Notion API token from environment
        notion_token = os.getenv("NOTION_API_TOKEN")
        if not notion_token:
            logger.error("NOTION_API_TOKEN not set in .env file")
            return False

        # Initialize Notion client
        logger.info("Initializing Notion client...")
        notion = Client(auth=notion_token)

        # Test API connection with a simple users.me call
        logger.info("Testing API connection...")
        user_info = notion.users.me()

        logger.info(f"Successfully connected to Notion API as: {user_info['name']} (ID: {user_info['id']})")
        return True

    except Exception as e:
        logger.error(f"Error connecting to Notion API: {e}")
        return False

def test_page_access():
    """Test access to the specified Notion page."""
    try:
        # Get Notion API token and page ID from environment
        notion_token = os.getenv("NOTION_API_TOKEN")

        # Force the correct page ID
        page_id = "1e4f98cc0d5080728077c50a131ef639"

        # Print the page ID for debugging
        print(f"Using NOTION_PAGE_ID: {page_id}")

        if not notion_token:
            logger.error("NOTION_API_TOKEN not set in .env file")
            return False

        if not page_id:
            logger.error("NOTION_PAGE_ID not set in .env file")
            return False

        # Format the page ID correctly with hyphens if needed
        # Notion API expects format like: 1cff98cc-0d50-8077-ba43-f8ce49d067ef
        if len(page_id) == 32 and "-" not in page_id:
            formatted_page_id = f"{page_id[0:8]}-{page_id[8:12]}-{page_id[12:16]}-{page_id[16:20]}-{page_id[20:]}"
            logger.info(f"Reformatted page ID from {page_id} to {formatted_page_id}")
            page_id = formatted_page_id

        # Initialize Notion client
        notion = Client(auth=notion_token)

        # Try to retrieve the page
        logger.info(f"Attempting to access page with ID: {page_id}")
        page = notion.pages.retrieve(page_id)

        # For a blank page, we might not be able to extract a title easily
        # Just check if we can access the page at all
        logger.info(f"Successfully accessed page with ID: {page_id}")

        # Try to list the page's children to further verify access
        children = notion.blocks.children.list(page_id)
        logger.info(f"Page has {len(children['results'])} child blocks")

        return True

    except Exception as e:
        logger.error(f"Error accessing Notion page: {e}")
        return False

def test_add_content():
    """Test adding content to the specified Notion page."""
    try:
        # Get Notion API token and page ID from environment
        notion_token = os.getenv("NOTION_API_TOKEN")
        page_id = os.getenv("NOTION_PAGE_ID")

        if not notion_token or not page_id:
            logger.error("NOTION_API_TOKEN or NOTION_PAGE_ID not set in .env file")
            return False

        # Format the page ID correctly with hyphens if needed
        if len(page_id) == 32 and "-" not in page_id:
            formatted_page_id = f"{page_id[0:8]}-{page_id[8:12]}-{page_id[12:16]}-{page_id[16:20]}-{page_id[20:]}"
            logger.info(f"Reformatted page ID from {page_id} to {formatted_page_id}")
            page_id = formatted_page_id

        # Initialize Notion client
        notion = Client(auth=notion_token)

        # Create a test block with current timestamp
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"Adding test content to page {page_id}...")

        # Add a heading and paragraph to the page
        notion.blocks.children.append(
            page_id,
            children=[
                {
                    "object": "block",
                    "type": "heading_2",
                    "heading_2": {
                        "rich_text": [
                            {
                                "type": "text",
                                "text": {
                                    "content": f"Test Content ({current_time})"
                                }
                            }
                        ]
                    }
                },
                {
                    "object": "block",
                    "type": "paragraph",
                    "paragraph": {
                        "rich_text": [
                            {
                                "type": "text",
                                "text": {
                                    "content": "This is a test paragraph added by the TAC News Aggregation System. "
                                }
                            },
                            {
                                "type": "text",
                                "text": {
                                    "content": "If you can see this, the Notion integration is working correctly!"
                                },
                                "annotations": {
                                    "bold": True
                                }
                            }
                        ]
                    }
                }
            ]
        )

        logger.info("Successfully added test content to the page")
        return True

    except Exception as e:
        logger.error(f"Error adding content to Notion page: {e}")
        return False

def create_new_page():
    """Create a new page in the workspace."""
    try:
        # Get Notion API token from environment
        notion_token = os.getenv("NOTION_API_TOKEN")

        if not notion_token:
            logger.error("NOTION_API_TOKEN not set in .env file")
            return False

        # Initialize Notion client
        notion = Client(auth=notion_token)

        # Create a new page in the workspace
        logger.info("Creating a new page in the workspace...")

        # Current timestamp for unique page name
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        new_page = notion.pages.create(
            parent={
                "type": "workspace",
                # No need for parent_id when creating in workspace root
            },
            properties={
                "title": {
                    "title": [
                        {
                            "text": {
                                "content": f"TAC Newsletter Test ({current_time})"
                            }
                        }
                    ]
                }
            },
            children=[
                {
                    "object": "block",
                    "type": "heading_1",
                    "heading_1": {
                        "rich_text": [
                            {
                                "type": "text",
                                "text": {
                                    "content": "TAC Weekly Newsletter"
                                }
                            }
                        ]
                    }
                },
                {
                    "object": "block",
                    "type": "paragraph",
                    "paragraph": {
                        "rich_text": [
                            {
                                "type": "text",
                                "text": {
                                    "content": "This is a test page created by the TAC News Aggregation System. "
                                }
                            },
                            {
                                "type": "text",
                                "text": {
                                    "content": "If you can see this, the Notion integration is working correctly!"
                                },
                                "annotations": {
                                    "bold": True
                                }
                            }
                        ]
                    }
                }
            ]
        )

        page_id = new_page["id"]
        logger.info(f"Successfully created new page with ID: {page_id}")

        # Print URL for easy access
        page_url = f"https://notion.so/{page_id.replace('-', '')}"
        logger.info(f"Page URL: {page_url}")

        return True

    except Exception as e:
        logger.error(f"Error creating new page: {e}")
        return False

def main():
    """Run the Notion integration tests."""
    print("\n=== Testing Notion Integration ===\n")

    # Test API connection
    print("Testing connection to Notion API...")
    if not test_notion_connection():
        print("\nFailed to connect to Notion API. Check your API token and internet connection.")
        return

    print("\nAPI connection successful!")

    # Try to create a new page instead of accessing existing one
    print("\nTrying to create a new page in the workspace...")
    if not create_new_page():
        print("\nFailed to create a new page. Check your permissions.")

        # Fall back to testing existing page access
        print("\nFalling back to testing access to specified Notion page...")
        if not test_page_access():
            print("\nFailed to access the specified Notion page. Check your page ID and permissions.")
            return
    else:
        print("\nNew page created successfully!")
        print("You can check your Notion workspace to see the new page.")

    print("\n=== Test Complete ===")
    print("The Notion integration is working correctly.")

if __name__ == "__main__":
    main()
