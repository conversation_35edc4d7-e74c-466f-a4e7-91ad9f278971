"""
Test script for Notion publisher.

This script tests the Notion publisher by publishing sample news items to a timestamped Notion subpage.
"""

import sys
import os
from datetime import datetime, timedelta

from core.models import NewsItem
from sources.notion.notion_publisher import test_notion_connection, publish_to_notion
from core.utils.logging import get_logger

logger = get_logger(__name__)

def create_sample_news_items():
    """Create sample news items for testing."""
    now = datetime.now()

    return [
        NewsItem(
            title="Sample Google News Article",
            link="https://example.com/news/1",
            published_at=now - timedelta(hours=2),
            summary="This is a sample article from Google News about tokenized assets.",
            publisher="Example News",
            source="google_news",
            fetched_at=now,
            relevance=True
        ),
        NewsItem(
            title="Sample Telegram Post",
            link="https://t.me/example/123",
            published_at=now - timedelta(hours=5),
            summary="This is a sample post from Telegram discussing real-world assets and their tokenization.",
            publisher="Crypto Channel",
            source="telegram",
            fetched_at=now,
            relevance=True
        ),
        NewsItem(
            title="Sample Form Submission",
            link="https://example.org/blog/rwa",
            published_at=now - timedelta(hours=8),
            summary="This is a sample submission from a TAC member about the latest developments in RWA.",
            publisher="TAC Member: Example Organization",
            source="google_form",
            fetched_at=now,
            relevance=True
        )
    ]

def main():
    """Test the Notion publisher."""
    print("\n=== Testing Notion Publisher with Timestamped Subpage ===\n")

    # Test Notion connection
    print("Testing connection to Notion...")
    if not test_notion_connection():
        print("\nFailed to connect to Notion. Check your API token and page ID.")
        return

    print("\nConnection successful!")

    # Create sample news items
    print("\nCreating sample news items...")
    news_items = create_sample_news_items()
    print(f"Created {len(news_items)} sample news items")

    # Publish to Notion with a since_timestamp
    since_timestamp = datetime.now() - timedelta(days=7)
    print("\nPublishing to Notion as a timestamped subpage with content since timestamp...")
    print(f"Using since_timestamp: {since_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

    if publish_to_notion(news_items, since_timestamp=since_timestamp):
        print("\nSuccessfully published news items to a timestamped Notion subpage!")
        print("Check your Notion page to see the new subpage with the current timestamp and since date.")
    else:
        print("\nFailed to publish news items to Notion.")

    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
