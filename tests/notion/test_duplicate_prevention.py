#!/usr/bin/env python3
"""
Test duplicate page prevention in create_weekly_notion_page.
"""

import unittest
from unittest.mock import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, call
from datetime import datetime
from sources.notion.subpage_creator import create_weekly_notion_page


class TestDuplicatePrevention(unittest.TestCase):
    """Test that create_weekly_notion_page doesn't create duplicates."""
    
    def test_returns_existing_page_if_found(self):
        """Test that function returns existing page ID when title matches."""
        # Mock the Notion client
        mock_client = Mock()
        
        # Mock the response from listing child blocks
        # Simulate that a page with matching title already exists
        mock_client.blocks.children.list.return_value = {
            "results": [
                {
                    "type": "paragraph",
                    "paragraph": {}
                },
                {
                    "type": "child_page",
                    "id": "existing-page-id-123",
                    "child_page": {
                        "title": "TAC Weekly Newsletter - Jun 01 - Jun 07, 2025"
                    }
                },
                {
                    "type": "divider",
                    "divider": {}
                }
            ],
            "has_more": False
        }
        
        # Set week start to June 1, 2025 (Sunday)
        week_start = datetime(2025, 6, 1, 0, 0, 0)
        
        # Call the function
        result = create_weekly_notion_page(
            client=mock_client,
            parent_page_id="parent-page-id",
            week_start_date=week_start
        )
        
        # Verify it returned the existing page ID
        self.assertEqual(result, "existing-page-id-123")
        
        # Verify it didn't try to create a new page
        mock_client.pages.create.assert_not_called()
        
        # Verify it searched for existing pages
        mock_client.blocks.children.list.assert_called_once_with(
            block_id="parent-page-id"
        )
    
    def test_creates_new_page_if_not_found(self):
        """Test that function creates new page when no matching title exists."""
        # Mock the Notion client
        mock_client = Mock()
        
        # Mock empty response from listing child blocks
        mock_client.blocks.children.list.return_value = {
            "results": [
                {
                    "type": "paragraph",
                    "paragraph": {}
                },
                {
                    "type": "child_page",
                    "id": "other-page-id",
                    "child_page": {
                        "title": "Some Other Page"
                    }
                }
            ],
            "has_more": False
        }
        
        # Mock the page creation response
        mock_client.pages.create.return_value = {
            "id": "new-page-id-456"
        }
        
        # Mock adding blocks (initial structure)
        mock_client.blocks.children.append.return_value = {}
        
        # Set week start to June 1, 2025 (Sunday)
        week_start = datetime(2025, 6, 1, 0, 0, 0)
        
        # Call the function
        result = create_weekly_notion_page(
            client=mock_client,
            parent_page_id="parent-page-id",
            week_start_date=week_start
        )
        
        # Verify it returned the new page ID
        self.assertEqual(result, "new-page-id-456")
        
        # Verify it tried to create a new page
        mock_client.pages.create.assert_called_once()
        
        # Verify the page was created with correct title
        create_call = mock_client.pages.create.call_args
        self.assertEqual(
            create_call[1]["properties"]["title"]["title"][0]["text"]["content"],
            "TAC Weekly Newsletter - Jun 01 - Jun 07, 2025"
        )
    
    def test_handles_pagination(self):
        """Test that function correctly handles paginated results."""
        # Mock the Notion client
        mock_client = Mock()
        
        # First page of results (no matching title)
        first_response = {
            "results": [
                {"type": "child_page", "id": "page1", "child_page": {"title": "Other Page 1"}},
                {"type": "child_page", "id": "page2", "child_page": {"title": "Other Page 2"}}
            ],
            "has_more": True,
            "next_cursor": "cursor-123"
        }
        
        # Second page of results (has matching title)
        second_response = {
            "results": [
                {"type": "child_page", "id": "page3", "child_page": {"title": "Other Page 3"}},
                {
                    "type": "child_page",
                    "id": "existing-weekly-page",
                    "child_page": {"title": "TAC Weekly Newsletter - Jun 01 - Jun 07, 2025"}
                }
            ],
            "has_more": False
        }
        
        # Set up mock to return different responses
        mock_client.blocks.children.list.side_effect = [first_response, second_response]
        
        # Set week start to June 1, 2025 (Sunday)
        week_start = datetime(2025, 6, 1, 0, 0, 0)
        
        # Call the function
        result = create_weekly_notion_page(
            client=mock_client,
            parent_page_id="parent-page-id",
            week_start_date=week_start
        )
        
        # Verify it returned the existing page ID from second page
        self.assertEqual(result, "existing-weekly-page")
        
        # Verify it didn't try to create a new page
        mock_client.pages.create.assert_not_called()
        
        # Verify it made two calls to list (pagination)
        self.assertEqual(mock_client.blocks.children.list.call_count, 2)
        
        # Verify second call used the cursor
        second_call = mock_client.blocks.children.list.call_args_list[1]
        self.assertEqual(second_call[1]["start_cursor"], "cursor-123")


if __name__ == "__main__":
    unittest.main()