"""
Test script for Notion integration.

This script tests the Notion integration by checking the connection
and verifying that we can access the specified page.
"""

import pytest
from sources.notion.notion_publisher import test_notion_connection, get_notion_client
from core.config import NOTION_API_TOKEN, NOTION_PAGE_ID
from core.utils.logging import get_logger

logger = get_logger(__name__)

@pytest.mark.integration
@pytest.mark.slow
def test_notion_integration():
    """Test the Notion integration."""
    print("\n=== Testing Notion Integration ===\n")
    
    # Check if Notion API token is set
    if not NOTION_API_TOKEN:
        logger.error("NOTION_API_TOKEN not set in environment variables")
        return False
    
    logger.info(f"Using Notion API token: {NOTION_API_TOKEN[:5]}...{NOTION_API_TOKEN[-5:]}")
    
    # Check if Notion page ID is set
    if not NOTION_PAGE_ID:
        logger.error("NOTION_PAGE_ID not set in environment variables")
        return False
    
    logger.info(f"Using Notion page ID: {NOTION_PAGE_ID}")
    
    # Test Notion connection
    logger.info("Testing Notion connection...")
    if test_notion_connection():
        logger.info("Notion connection test passed!")
        return True
    else:
        logger.error("Notion connection test failed!")
        return False

if __name__ == "__main__":
    test_notion_integration()
