#!/usr/bin/env python3
"""
Unit tests for URL linking functionality in block_generator.
"""

import unittest
from sources.notion.block_generator import create_paragraph_block


class TestURLLinking(unittest.TestCase):
    """Test URL detection and linking in paragraph blocks."""
    
    def test_no_urls(self):
        """Text without URLs returns simple rich text."""
        result = create_paragraph_block("This is plain text with no URLs")
        
        self.assertEqual(result["type"], "paragraph")
        self.assertEqual(len(result["paragraph"]["rich_text"]), 1)
        self.assertEqual(result["paragraph"]["rich_text"][0]["text"]["content"], 
                        "This is plain text with no URLs")
        self.assertNotIn("link", result["paragraph"]["rich_text"][0]["text"])
    
    def test_empty_text(self):
        """Empty text returns empty rich text array."""
        result = create_paragraph_block("")
        
        self.assertEqual(result["type"], "paragraph")
        self.assertEqual(result["paragraph"]["rich_text"], [])
    
    def test_single_url(self):
        """Text with single URL makes it clickable."""
        result = create_paragraph_block("Check out https://example.com for more info")
        
        rich_text = result["paragraph"]["rich_text"]
        self.assertEqual(len(rich_text), 3)
        
        # Before URL
        self.assertEqual(rich_text[0]["text"]["content"], "Check out ")
        self.assertNotIn("link", rich_text[0]["text"])
        
        # URL itself
        self.assertEqual(rich_text[1]["text"]["content"], "https://example.com")
        self.assertEqual(rich_text[1]["text"]["link"]["url"], "https://example.com")
        
        # After URL
        self.assertEqual(rich_text[2]["text"]["content"], " for more info")
        self.assertNotIn("link", rich_text[2]["text"])
    
    def test_multiple_urls(self):
        """Text with multiple URLs makes all clickable."""
        result = create_paragraph_block("Visit https://github.com and https://google.com today")
        
        rich_text = result["paragraph"]["rich_text"]
        self.assertEqual(len(rich_text), 5)
        
        # Check both URLs are linkified
        self.assertEqual(rich_text[1]["text"]["content"], "https://github.com")
        self.assertEqual(rich_text[1]["text"]["link"]["url"], "https://github.com")
        
        self.assertEqual(rich_text[3]["text"]["content"], "https://google.com")
        self.assertEqual(rich_text[3]["text"]["link"]["url"], "https://google.com")
    
    def test_url_without_protocol(self):
        """URLs without protocol get https:// prefix in link."""
        result = create_paragraph_block("Visit example.com for details")
        
        rich_text = result["paragraph"]["rich_text"]
        # URLExtract should find this
        url_found = False
        for segment in rich_text:
            if "link" in segment.get("text", {}):
                self.assertEqual(segment["text"]["content"], "example.com")
                self.assertEqual(segment["text"]["link"]["url"], "https://example.com")
                url_found = True
        
        self.assertTrue(url_found, "URL without protocol should be detected")
    
    def test_tweet_pattern(self):
        """Common tweet ending pattern with t.co link."""
        text = "Wake up and smell the 🥞 https://t.co/3XaAacvX2I"
        result = create_paragraph_block(text)
        
        rich_text = result["paragraph"]["rich_text"]
        # Last segment should be the URL
        self.assertEqual(rich_text[-1]["text"]["content"], "https://t.co/3XaAacvX2I")
        self.assertEqual(rich_text[-1]["text"]["link"]["url"], "https://t.co/3XaAacvX2I")
    
    def test_italic_formatting_preserved(self):
        """Italic formatting is preserved across all segments."""
        result = create_paragraph_block("Check https://example.com out", italic=True)
        
        rich_text = result["paragraph"]["rich_text"]
        # All segments should have italic annotation
        for segment in rich_text:
            self.assertTrue(segment["annotations"].get("italic", False))
    
    def test_color_formatting_preserved(self):
        """Color formatting is preserved across all segments."""
        result = create_paragraph_block("Visit https://example.com today", color="gray")
        
        rich_text = result["paragraph"]["rich_text"]
        # All segments should have gray color
        for segment in rich_text:
            self.assertEqual(segment["annotations"].get("color"), "gray")
    
    def test_combined_formatting(self):
        """Both italic and color formatting preserved."""
        result = create_paragraph_block("See https://example.com now", italic=True, color="red")
        
        rich_text = result["paragraph"]["rich_text"]
        for segment in rich_text:
            self.assertTrue(segment["annotations"].get("italic", False))
            self.assertEqual(segment["annotations"].get("color"), "red")
    
    def test_url_at_start(self):
        """URL at the beginning of text."""
        result = create_paragraph_block("https://example.com is a great site")
        
        rich_text = result["paragraph"]["rich_text"]
        # First segment should be the URL
        self.assertEqual(rich_text[0]["text"]["content"], "https://example.com")
        self.assertEqual(rich_text[0]["text"]["link"]["url"], "https://example.com")
    
    def test_url_at_end(self):
        """URL at the end of text."""
        result = create_paragraph_block("Check out this site: https://example.com")
        
        rich_text = result["paragraph"]["rich_text"]
        # Last segment should be the URL
        self.assertEqual(rich_text[-1]["text"]["content"], "https://example.com")
        self.assertEqual(rich_text[-1]["text"]["link"]["url"], "https://example.com")
    
    def test_metadata_line_pattern(self):
        """Common metadata line pattern with URL."""
        text = "Published by: @username • Jul 02, 2025 • https://t.me/@channel/123"
        result = create_paragraph_block(text, italic=True)
        
        rich_text = result["paragraph"]["rich_text"]
        # Should have URL linkified and all segments italic
        url_found = False
        for segment in rich_text:
            self.assertTrue(segment["annotations"].get("italic", False))
            if "link" in segment.get("text", {}):
                self.assertEqual(segment["text"]["content"], "https://t.me/@channel/123")
                url_found = True
        
        self.assertTrue(url_found, "Telegram URL should be detected")


if __name__ == "__main__":
    unittest.main()