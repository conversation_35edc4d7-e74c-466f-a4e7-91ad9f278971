#!/usr/bin/env python3
"""
Test suite for daily mode functionality in main.py.
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock, call
import argparse

from scripts.main import extract_config_for_source, parse_args


class TestDailyMode(unittest.TestCase):
    """Test the daily mode functionality."""
    
    def test_parse_args_daily_mode(self):
        """Test that daily mode arguments are parsed correctly."""
        # Test with daily mode enabled
        with patch('sys.argv', ['main.py', '--daily-mode']):
            args = parse_args()
            self.assertTrue(args.daily_mode)
            self.assertEqual(args.daily_collection_hours, 24)  # Default
        
        # Test with custom collection hours
        with patch('sys.argv', ['main.py', '--daily-mode', '--daily-collection-hours', '12']):
            args = parse_args()
            self.assertTrue(args.daily_mode)
            self.assertEqual(args.daily_collection_hours, 12)
        
        # Test without daily mode (default)
        with patch('sys.argv', ['main.py']):
            args = parse_args()
            self.assertFalse(args.daily_mode)
    
    def test_extract_config_daily_mode_tweet_days(self):
        """Test that tweet collection days are adjusted in daily mode."""
        # Mock args for daily mode
        args = Mock()
        args.daily_mode = True
        args.tweet_collection_days = 7  # Default value
        args.max_tweet_results = 100
        args.max_tweet_attempts = 5
        args.tweet_attempt_delay = 0
        
        # Extract config for X/Twitter
        config = extract_config_for_source("X/Twitter List", args)
        
        # Should be 1 day in daily mode when using default
        self.assertEqual(config['days'], 1)
        
        # Test with custom days (should not be overridden)
        args.tweet_collection_days = 3
        config = extract_config_for_source("X/Twitter List", args)
        self.assertEqual(config['days'], 3)
        
        # Test weekly mode
        args.daily_mode = False
        args.tweet_collection_days = 7
        config = extract_config_for_source("X/Twitter List", args)
        self.assertEqual(config['days'], 7)
    
    @patch('scripts.main.start_run')
    @patch('scripts.main.complete_run')
    @patch('scripts.main.init_daily_runs')
    def test_daily_run_tracking(self, mock_init_runs, mock_complete, mock_start):
        """Test that daily runs are tracked properly."""
        # Mock return values
        mock_start.return_value = 123  # Run ID
        
        # Import main function
        from scripts.main import main
        
        # Mock all dependencies
        with patch('sys.argv', ['main.py', '--daily-mode', '--skip-google-news', 
                               '--skip-telegram', '--skip-form', '--skip-tweet-collection',
                               '--skip-notion']):
            with patch('scripts.main.init_db'):
                with patch('scripts.main.init_publication_state'):
                    main()
        
        # Verify daily run was started
        mock_init_runs.assert_called_once()
        mock_start.assert_called_once_with('daily')
        
        # Verify run was completed
        mock_complete.assert_called_once()
        call_args = mock_complete.call_args[0]
        self.assertEqual(call_args[0], 123)  # Run ID
        
    @patch('scripts.main.is_new_week')
    @patch('scripts.main.get_current_weekly_page_id')
    @patch('scripts.main.create_weekly_notion_page')
    @patch('scripts.main.update_weekly_page_id')
    @patch('scripts.main.get_notion_client')
    @patch('scripts.main.publish_to_notion')
    @patch('scripts.main.get_news_items_since')
    def test_week_boundary_detection(self, mock_get_items, mock_publish, mock_get_client,
                                   mock_update_page, mock_create_page, mock_get_page_id, 
                                   mock_is_new_week):
        """Test week boundary detection and new page creation."""
        # Setup mocks
        mock_is_new_week.return_value = True
        mock_client = Mock()
        mock_get_client.return_value = mock_client
        mock_create_page.return_value = "new-page-123"
        mock_get_items.return_value = []
        
        # Import main function
        from scripts.main import main
        
        # Run with necessary patches
        with patch('sys.argv', ['main.py', '--daily-mode', '--skip-google-news',
                               '--skip-telegram', '--skip-form', '--skip-tweet-collection']):
            with patch('scripts.main.init_db'):
                with patch('scripts.main.init_publication_state'):
                    with patch('scripts.main.init_daily_runs'):
                        with patch('scripts.main.start_run', return_value=1):
                            with patch('scripts.main.NOTION_API_TOKEN', 'test-token'):
                                with patch('scripts.main.NOTION_PAGE_ID', 'parent-123'):
                                    main()
        
        # Verify new week was detected and page created
        mock_is_new_week.assert_called()
        mock_create_page.assert_called_once()
        mock_update_page.assert_called_once_with("new-page-123")
    
    @patch('scripts.main.is_new_week')
    @patch('scripts.main.get_current_weekly_page_id')
    @patch('scripts.main.publish_to_notion')
    @patch('scripts.main.get_news_items_since')
    def test_daily_append_mode(self, mock_get_items, mock_publish, mock_get_page_id, 
                              mock_is_new_week):
        """Test that daily mode uses append mode for publishing."""
        # Setup mocks
        mock_is_new_week.return_value = False
        mock_get_page_id.return_value = "current-page-123"
        mock_get_items.return_value = [Mock()]  # One item
        mock_publish.return_value = "current-page-123"
        
        # Import main function
        from scripts.main import main
        
        # Run with necessary patches
        with patch('sys.argv', ['main.py', '--daily-mode', '--skip-google-news',
                               '--skip-telegram', '--skip-form', '--skip-tweet-collection',
                               '--daily-collection-hours', '12']):
            with patch('scripts.main.init_db'):
                with patch('scripts.main.init_publication_state'):
                    with patch('scripts.main.init_daily_runs'):
                        with patch('scripts.main.start_run', return_value=1):
                            with patch('scripts.main.NOTION_API_TOKEN', 'test-token'):
                                with patch('scripts.main.NOTION_PAGE_ID', 'parent-123'):
                                    with patch('scripts.main.update_publication_timestamp'):
                                        main()
        
        # Verify append mode was used
        mock_publish.assert_called_once()
        call_args = mock_publish.call_args
        self.assertTrue(call_args[1]['append_mode'])
        self.assertEqual(call_args[1]['target_page_id'], "current-page-123")
        
        # Verify time window
        mock_get_items.assert_called_once()
        cutoff = mock_get_items.call_args[0][0]
        # Should be approximately 12 hours ago
        time_diff = datetime.now() - cutoff
        self.assertAlmostEqual(time_diff.total_seconds(), 12 * 3600, delta=60)


if __name__ == '__main__':
    unittest.main()