#!/usr/bin/env python3
"""
Test script for X List integration with AI analysis.
This script fetches tweets from a specified X List, analyzes them with <PERSON>,
and displays the results.
"""

from datetime import datetime

from sources.x.collector import XCollector
from core.storage import init_db, save_tweet_items, update_tweet_relevance
from core.ai_relevance_judge import evaluate_item_relevance
from core.enums import RelevanceStatus
from core.config import SAMPLE_SIZE
from core.utils.logging import get_logger

# Note: This test script specifically needs file logging
logger = get_logger(__name__, add_file_handler=True)

def main():
    """Test the X List integration with AI analysis."""
    logger.info("Starting X List AI analysis test")

    # Initialize the database
    init_db()

    # Set a small limit for testing purposes
    test_limit = 10  # Only fetch 10 tweets for testing

    # Create a collector instance
    collector = XCollector()

    # Fetch tweets from X List
    logger.info(f"Fetching a sample of {test_limit} tweets for testing")
    tweets, _, _, _ = collector.fetch_x_list_tweets(max_results=test_limit)

    if not tweets:
        logger.warning("No tweets were fetched from X List. Exiting.")
        return

    logger.info(f"Successfully fetched {len(tweets)} tweets from X List")

    # Save tweets to database
    saved_count = save_tweet_items(tweets)
    logger.info(f"Saved {saved_count} new tweets to database")

    # Process a sample with LLM for relevance using the new function
    sample_size = min(SAMPLE_SIZE if SAMPLE_SIZE else len(tweets), len(tweets))
    logger.info(f"Analyzing relevance for {sample_size} tweets")
    
    # Create the sample
    tweet_sample = tweets[:sample_size]
    
    # Evaluate sample
    evaluation_results = []
    for tweet in tweet_sample:
        try:
            # Use the new evaluation function
            status = evaluate_item_relevance(tweet)
            evaluation_results.append((tweet, status))
            logger.info(f"Evaluated Tweet {tweet.tweet_id}: Status={status.value if status else 'None'}")
            # Update relevance in database immediately
            if status:
                update_tweet_relevance(tweet.tweet_id, status)
            else:
                logger.warning(f"Skipping DB update for tweet {tweet.tweet_id} due to None status.")
        except Exception as e:
            logger.error(f"Error evaluating tweet {tweet.tweet_id}: {e}", exc_info=True)
            # Optionally append a placeholder or skip if evaluation fails
            evaluation_results.append((tweet, None)) # Mark as None if evaluation failed

    # Display results
    print("\n=== X List AI Analysis Results ===")
    print(f"Fetched {len(tweets)} tweets from X List")
    print(f"Saved {saved_count} new tweets to database")
    print(f"Analyzed {len(evaluation_results)} tweets for relevance") # Use new results list

    print("\nRelevance Analysis Results:")
    # Iterate through new results format
    for tweet, status in evaluation_results:
        # Display result
        if status:
            status_str = status.value.replace("_", " ").title()
        elif status is None and tweet.relevance is not None:
            # If eval failed but item had relevance set internally by evaluate_item_relevance before error
            status_str = f"ERROR ({tweet.relevance.value})"
        else:
            status_str = "ERROR (Unknown)"
            
        print(f"\n[{status_str}] Tweet from @{tweet.author_username or tweet.author_id}")
        print(f"Text: {tweet.text[:150]}..." if len(tweet.text) > 150 else f"Text: {tweet.text}")
        print(f"Created: {tweet.created_at.strftime('%Y-%m-%d %H:%M')}")
        print(f"Hashtags: {', '.join(tweet.hashtags) if tweet.hashtags else 'None'}")
        print(f"Engagement: {tweet.like_count} likes, {tweet.retweet_count} retweets")
        if tweet.urls:
            print(f"URLs: {', '.join(str(url) for url in tweet.urls[:3])}" +
                  ("..." if len(tweet.urls) > 3 else ""))

    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
