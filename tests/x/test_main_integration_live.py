#!/usr/bin/env python3
"""
Live test script for the X integration with the main application.
This script tests the X collection functionality with the real X API.
"""

import pytest
import os
from datetime import datetime, timedelta

# Import the XCollector class
from sources.x.collector import XCollector
from core.models import TweetItem, NewsItem
from core.utils.logging import get_logger

logger = get_logger(__name__)

@pytest.mark.integration
@pytest.mark.slow
def test_x_integration_live():
    """Test the X integration with the real X API."""
    logger.info("Starting live test of X integration")

    try:
        # Create a collector instance
        collector = XCollector()

        # First, check if there's an active collection and complete it if needed
        active_collection = collector.state_manager.get_collection()
        if active_collection:
            logger.info(f"Found active collection: {active_collection.get('collection_id')}")
            logger.info("Completing the active collection before starting a new one")
            collector.state_manager.complete_collection(
                collection_id=active_collection.get('collection_id'),
                notes="Completed by test script"
            )

        # Now use the collect_weekly_tweets method which handles time windows properly
        logger.info("Collecting tweets for the past day (small test)")
        tweets, collection_id, is_complete = collector.collect_weekly_tweets(
            max_results=10,  # Small limit for testing
            save_to_db=True,
            days=1,  # Just test the past day to minimize API calls
            resume_if_exists=False  # Start fresh for this test
        )

        # collection_id and is_complete are returned by collect_weekly_tweets

        # Log the results
        logger.info(f"Collected {len(tweets)} tweets")
        logger.info(f"Collection ID: {collection_id}")
        logger.info(f"Collection complete: {is_complete}")

        # Display the tweets
        if tweets:
            logger.info("Sample of collected tweets:")
            for i, tweet in enumerate(tweets[:3], 1):  # Show up to 3 tweets
                logger.info(f"[{i}] @{tweet.author_username}: {tweet.text[:100]}...")

            # Convert relevant tweets to NewsItems
            # For testing, we'll consider all tweets as relevant
            logger.info("Converting tweets to news items")
            news_items = [collector.tweet_processor.tweet_to_news_item(t) for t in tweets]
            logger.info(f"Converted {len(news_items)} tweets to news items")

            # Display the news items
            if news_items:
                logger.info("Sample of news items:")
                for i, item in enumerate(news_items[:3], 1):  # Show up to 3 news items
                    logger.info(f"[{i}] {item.title}")
                    logger.info(f"    Link: {item.link}")
                    logger.info(f"    Publisher: {item.publisher}")
        else:
            logger.warning("No tweets collected")

        logger.info("Live test completed successfully")
        return True

    except Exception as e:
        logger.error(f"Live test failed: {e}")
        return False

if __name__ == '__main__':
    success = test_x_integration_live()
    if success:
        print("\n✅ Live test passed successfully")
    else:
        print("\n❌ Live test failed")
