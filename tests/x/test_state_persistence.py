"""
Test for state persistence in XCollector.
"""

import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Import the XCollector class
from sources.x.collector import XCollector
from core.utils.logging import get_logger

logger = get_logger(__name__)


class TestStatePersistence(unittest.TestCase):
    """Test state persistence in XCollector."""

    @patch('sources.x.collector.XApiClient')
    @patch('sources.x.collector.XStateManager')
    @patch('sources.x.collector.XTweetProcessor')
    @patch('sources.x.collector.save_tweet_items')
    def test_state_persistence(self, mock_save_tweets, mock_tweet_processor_class, mock_state_manager_class, mock_api_client_class):
        """Test that state is saved after each page."""
        # Set up mocks
        mock_api_client = MagicMock()
        mock_state_manager = MagicMock()
        mock_tweet_processor = MagicMock()

        mock_api_client_class.return_value = mock_api_client
        mock_state_manager_class.return_value = mock_state_manager
        mock_tweet_processor_class.return_value = mock_tweet_processor

        # Mock the start_collection method to return a collection ID
        mock_state_manager.start_collection.return_value = "test_collection_id"

        # Set up the tweet processor to return some processed tweets
        mock_tweet_processor.process_api_response.return_value = [
            MagicMock(tweet_id="1"),
            MagicMock(tweet_id="2")
        ]

        # Mock the get_all_list_tweets method to capture and call the page_callback
        def mock_get_all_list_tweets(**kwargs):
            # Extract the page_callback
            page_callback = kwargs.get('page_callback')

            # Call the callback twice to simulate two pages
            if page_callback:
                # First page
                page_callback(
                    metadata={"response_meta": {"newest_id": "1", "oldest_id": "1"}},
                    tweets=[{"id": "1", "text": "Tweet 1"}],
                    page_count=1,
                    next_token="next_token_1"
                )

                # Second page
                page_callback(
                    metadata={"response_meta": {"newest_id": "2", "oldest_id": "1"}},
                    tweets=[{"id": "2", "text": "Tweet 2"}],
                    page_count=2,
                    next_token=None
                )

            # Return mock data
            return (
                # Raw tweets
                [
                    {"id": "1", "text": "Tweet 1"},
                    {"id": "2", "text": "Tweet 2"}
                ],
                # Metadata
                {
                    "newest_id": "2",
                    "oldest_id": "1",
                    "next_token": None,
                    "total_tweets": 2,
                    "pages": [{"page": 1}, {"page": 2}]
                }
            )

        mock_api_client.get_all_list_tweets.side_effect = mock_get_all_list_tweets

        # Create the collector and call fetch_x_list_tweets
        collector = XCollector()
        start_time = datetime.now() - timedelta(days=1)
        end_time = datetime.now()

        tweets, newest_id, next_token, _ = collector.fetch_x_list_tweets(
            list_id="test_list",
            start_time=start_time,
            end_time=end_time,
            max_results=10
        )

        # Verify that start_collection was called
        mock_state_manager.start_collection.assert_called_once()

        # Verify that get_all_list_tweets was called with the page_callback
        call_args = mock_api_client.get_all_list_tweets.call_args[1]
        self.assertIn('page_callback', call_args)
        self.assertIsNotNone(call_args['page_callback'])

        # Verify that update_collection was called at least twice
        # Once for each page callback and once for the final update
        self.assertGreaterEqual(mock_state_manager.update_collection.call_count, 2)

        # Verify that complete_collection was called
        mock_state_manager.complete_collection.assert_called_once()


if __name__ == '__main__':
    unittest.main()
