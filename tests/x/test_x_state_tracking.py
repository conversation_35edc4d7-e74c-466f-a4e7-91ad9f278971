#!/usr/bin/env python3
"""
Test script for X collection state tracking.
This script tests the state tracking functions for X tweet collection.
"""

import sys
from datetime import datetime, timedelta
from core.storage.db_init_pg import init_db
from core.storage.x_collection_pg import (
    initialize_collection_state, 
    update_collection_state, 
    get_collection_state,
    get_active_collection,
    clear_collection_state
)
from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_state_initialization():
    """Test initializing a new collection state."""
    logger.info("Testing collection state initialization...")
    
    # Define a time window for collection
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    # Initialize a new collection state
    collection_id = initialize_collection_state(start_time, end_time)
    
    if collection_id:
        logger.info(f"Successfully initialized collection state with ID: {collection_id}")
        return collection_id
    else:
        logger.error("Failed to initialize collection state")
        return None

def test_state_update(collection_id):
    """Test updating an existing collection state."""
    logger.info(f"Testing collection state update for ID: {collection_id}")
    
    # Update various fields
    success = update_collection_state(
        collection_id,
        oldest_tweet_id="1234567890",
        newest_tweet_id="9876543210",
        pagination_token="abc123xyz",
        requests_made=5,
        tweets_collected=100,
        last_request_time=datetime.now().isoformat(),
        notes="Test update"
    )
    
    if success:
        logger.info("Successfully updated collection state")
    else:
        logger.error("Failed to update collection state")
    
    return success

def test_state_retrieval(collection_id):
    """Test retrieving collection state."""
    logger.info(f"Testing collection state retrieval for ID: {collection_id}")
    
    # Get the state by ID
    state = get_collection_state(collection_id)
    
    if state:
        logger.info(f"Successfully retrieved collection state: {state}")
        
        # Verify updated fields
        logger.info(f"Oldest tweet ID: {state.get('oldest_tweet_id')}")
        logger.info(f"Newest tweet ID: {state.get('newest_tweet_id')}")
        logger.info(f"Pagination token: {state.get('pagination_token')}")
        logger.info(f"Requests made: {state.get('requests_made')}")
        logger.info(f"Tweets collected: {state.get('tweets_collected')}")
        logger.info(f"Status: {state.get('status')}")
        
        return True
    else:
        logger.error("Failed to retrieve collection state")
        return False

def test_active_collection():
    """Test getting the active collection."""
    logger.info("Testing active collection retrieval...")
    
    # Get the active collection
    active = get_active_collection()
    
    if active:
        logger.info(f"Successfully retrieved active collection: {active.get('collection_id')}")
        return True
    else:
        logger.info("No active collection found")
        return False

def test_state_status_update(collection_id):
    """Test updating the status of a collection state."""
    logger.info(f"Testing collection state status update for ID: {collection_id}")
    
    # Update status to 'complete'
    success = update_collection_state(
        collection_id,
        status="complete"
    )
    
    if success:
        logger.info("Successfully updated collection status to 'complete'")
    else:
        logger.error("Failed to update collection status")
    
    return success

def test_state_clearing(collection_id):
    """Test clearing a collection state."""
    logger.info(f"Testing collection state clearing for ID: {collection_id}")
    
    # Clear the state
    success = clear_collection_state(collection_id)
    
    if success:
        logger.info("Successfully cleared collection state")
    else:
        logger.error("Failed to clear collection state")
    
    return success

def main():
    """Run the state tracking tests."""
    logger.info("Starting X collection state tracking tests")
    
    # Initialize the database
    init_db()
    
    # Test state initialization
    collection_id = test_state_initialization()
    if not collection_id:
        logger.error("State initialization test failed. Exiting.")
        return
    
    # Test state update
    if not test_state_update(collection_id):
        logger.error("State update test failed. Continuing...")
    
    # Test state retrieval
    if not test_state_retrieval(collection_id):
        logger.error("State retrieval test failed. Continuing...")
    
    # Test active collection retrieval
    test_active_collection()
    
    # Test state status update
    if not test_state_status_update(collection_id):
        logger.error("State status update test failed. Continuing...")
    
    # Test active collection after status change
    if test_active_collection():
        logger.error("Active collection test failed: found active collection after status change to 'complete'")
    else:
        logger.info("Active collection test passed: no active collection found after status change")
    
    # Test state clearing
    if not test_state_clearing(collection_id):
        logger.error("State clearing test failed. Continuing...")
    
    # Verify state was cleared
    if get_collection_state(collection_id):
        logger.error("State clearing verification failed: state still exists after clearing")
    else:
        logger.info("State clearing verification passed: state no longer exists")
    
    logger.info("X collection state tracking tests completed")

if __name__ == "__main__":
    main()
