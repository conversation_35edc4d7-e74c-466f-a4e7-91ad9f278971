#!/usr/bin/env python3
"""
Test script for the X integration with the main application.
This script tests the X collection functionality in the main script.
"""

import os
from datetime import datetime
import unittest
from unittest.mock import patch, MagicMock

# Import the XCollector class
from sources.x.collector import XCollector
from core.models import TweetItem, NewsItem
from core.enums import RelevanceStatus
from core.utils.logging import get_logger

logger = get_logger(__name__)


class TestMainIntegration(unittest.TestCase):
    """Test the X integration with the main application."""

    @patch.object(XCollector, 'collect_weekly_tweets')
    def test_x_integration(self, mock_collect):
        """Test the X integration in the main script."""
        # Set up mocks
        mock_tweets = [
            TweetItem(
                tweet_id='1',
                text='Test tweet 1',
                author_id='user1',
                author_username='user1',
                author_name='User 1',
                created_at=datetime.now(),
                relevance=RelevanceStatus.RELEVANT  # This tweet should be included in the newsletter
            ),
            TweetItem(
                tweet_id='2',
                text='Test tweet 2',
                author_id='user2',
                author_username='user2',
                author_name='User 2',
                created_at=datetime.now(),
                relevance=RelevanceStatus.NOT_RELEVANT  # This tweet should NOT be included in the newsletter
            ),
            TweetItem(
                tweet_id='3',
                text='Test tweet 3',
                author_id='user3',
                author_username='user3',
                author_name='User 3',
                created_at=datetime.now(),
                relevance=RelevanceStatus.RELEVANT  # This tweet should be included in the newsletter
            )
        ]
        mock_collect.return_value = (mock_tweets, 'test_collection_123', True)  # tweets, collection_id, is_complete
        
        # We'll test just the X integration part of the main script
        try:
            # Create a collector instance
            collector = XCollector()
            
            # Call the collect_weekly_tweets method
            tweets, _, is_complete = collector.collect_weekly_tweets(
                max_results=100,
                save_to_db=False,
                days=7,
                resume_if_exists=True
            )
            
            # Verify the results
            self.assertEqual(len(tweets), 3)
            self.assertTrue(is_complete)
            
            # Convert relevant tweets to NewsItems
            relevant_tweets = [t for t in tweets if t.relevance == RelevanceStatus.RELEVANT]
            self.assertEqual(len(relevant_tweets), 2)  # Only 2 tweets are relevant
            
            # Convert to news items
            news_items = [collector.tweet_processor.tweet_to_news_item(t) for t in relevant_tweets]
            self.assertEqual(len(news_items), 2)
            
            # Verify the news items
            for item in news_items:
                self.assertIsInstance(item, NewsItem)
                self.assertTrue(item.title)
                self.assertTrue(item.link)
                self.assertTrue(item.summary)
                self.assertTrue(item.publisher)
            
            logger.info("X integration test passed successfully")
            
        except Exception as e:
            self.fail(f"X integration test failed: {e}")


if __name__ == '__main__':
    unittest.main()
