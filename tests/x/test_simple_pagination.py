"""
Simple test for pagination in XApiClient.

This script makes a basic request to the X API to get a pagination token.
"""

from datetime import datetime, timedelta

# Import the XApiClient class
from sources.x.api_client import XApiClient
from core.config import X_LIST_ID
from core.utils.logging import get_logger

logger = get_logger(__name__)


def test_simple_pagination():
    """Make a simple request to get a pagination token."""
    logger.info("Starting simple pagination test")
    
    # Create API client
    api_client = XApiClient()
    
    # Make a simple request with minimal parameters
    logger.info("Making request with minimal parameters")
    tweets, metadata = api_client.get_all_list_tweets(
        list_id=X_LIST_ID,
        max_results=5,  # Small number to ensure pagination
        max_pages=1     # Only get the first page
    )
    
    logger.info(f"Request fetched {len(tweets)} tweets")
    
    # Get the pagination token
    next_token = metadata.get('next_token')
    logger.info(f"Pagination token: {next_token}")
    
    if next_token:
        logger.info("Successfully got a pagination token")
        return True
    else:
        logger.warning("No pagination token returned")
        return False


if __name__ == "__main__":
    success = test_simple_pagination()
    if success:
        logger.info("Test completed successfully")
    else:
        logger.warning("Test completed but no pagination token was returned")
        # Don't exit with error code since this might be expected in some cases
