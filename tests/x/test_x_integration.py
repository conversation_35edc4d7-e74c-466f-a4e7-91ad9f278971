#!/usr/bin/env python3
"""
Test script for X integration.
This script tests the modular X integration implementation.
"""

from datetime import datetime, timedelta

from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_x_list_scraper():
    """Test the X List Scraper implementation using XCollector directly."""
    logger.info("Testing X List Scraper implementation using XCollector...")

    try:
        from sources.x.collector import XCollector

        # Create a collector instance
        collector = XCollector()

        # Fetch tweets with a small limit for testing
        tweets, newest_id, _, _ = collector.fetch_x_list_tweets(max_results=5)

        logger.info(f"XCollector fetched {len(tweets)} tweets")
        logger.info(f"Newest tweet ID: {newest_id}")

        # Convert to news items
        news_items = [collector.tweet_processor.tweet_to_news_item(tweet) for tweet in tweets]
        logger.info(f"Converted {len(news_items)} tweets to news items")

        # Return the results for comparison
        return tweets, news_items

    except Exception as e:
        logger.error(f"Error testing XCollector: {e}")
        return [], []

def test_collector_directly():
    """Test the X Collector directly."""
    logger.info("Testing X Collector directly...")

    try:
        from sources.x.collector import XCollector

        collector = XCollector()

        # Fetch tweets with a small limit for testing
        tweets, newest_id, _, _ = collector.fetch_x_list_tweets(max_results=5)

        logger.info(f"Collector fetched {len(tweets)} tweets")
        logger.info(f"Newest tweet ID: {newest_id}")

        # Convert to news items
        news_items = collector.convert_tweets_to_news(tweets)
        logger.info(f"Converted {len(news_items)} tweets to news items")

        # Return the results for comparison
        return tweets, news_items

    except Exception as e:
        logger.error(f"Error testing collector directly: {e}")
        return [], []

def test_time_based_collection():
    """Test time-based collection with the new implementation."""
    logger.info("Testing time-based collection...")

    try:
        from sources.x.collector import XCollector

        collector = XCollector()

        # Calculate time window for the past day
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)

        logger.info(f"Fetching tweets from {start_time.isoformat()} to {end_time.isoformat()}")

        # Fetch tweets with time window
        tweets, newest_id, _, _ = collector.fetch_x_list_tweets(
            start_time=start_time,
            end_time=end_time,
            max_results=5
        )

        logger.info(f"Time-based collection fetched {len(tweets)} tweets")
        logger.info(f"Newest tweet ID: {newest_id}")

        # Check that tweets are within the time window
        in_window = 0
        for tweet in tweets:
            if start_time <= tweet.created_at <= end_time:
                in_window += 1

        logger.info(f"{in_window} out of {len(tweets)} tweets are within the time window")

        return tweets

    except Exception as e:
        logger.error(f"Error testing time-based collection: {e}")
        return []

def compare_results(scraper_tweets, collector_tweets):
    """Compare results from two different XCollector usage patterns."""
    logger.info("Comparing results from two different XCollector usage patterns...")

    # Check if we have results to compare
    if not scraper_tweets or not collector_tweets:
        logger.warning("Not enough data to compare results")
        return False

    # Compare tweet counts
    logger.info(f"First collection: {len(scraper_tweets)} tweets, Second collection: {len(collector_tweets)} tweets")

    # Compare tweet IDs
    first_ids = set(tweet.tweet_id for tweet in scraper_tweets)
    second_ids = set(tweet.tweet_id for tweet in collector_tweets)

    common_ids = first_ids.intersection(second_ids)
    only_in_first = first_ids - second_ids
    only_in_second = second_ids - first_ids

    logger.info(f"Common tweet IDs: {len(common_ids)}")
    logger.info(f"Tweet IDs only in first collection: {len(only_in_first)}")
    logger.info(f"Tweet IDs only in second collection: {len(only_in_second)}")

    # Check if the collections are similar enough
    # Note: We expect some differences due to the timing of API calls
    similarity = len(common_ids) / max(len(first_ids), len(second_ids)) if max(len(first_ids), len(second_ids)) > 0 else 0
    logger.info(f"Similarity: {similarity:.2%}")

    return similarity >= 0.5  # Consider similar if at least 50% overlap

def main():
    """Run the integration tests."""
    logger.info("Starting X integration tests")

    # Test XCollector with first approach
    first_tweets, first_news = test_x_list_scraper()

    # Test XCollector with second approach
    second_tweets, second_news = test_collector_directly()

    # Test time-based collection
    time_based_tweets = test_time_based_collection()

    # Compare results from different approaches
    collection_comparison = compare_results(first_tweets, second_tweets)

    # Print summary
    print("\n=== Test Summary ===")
    print(f"First collection: {len(first_tweets)} tweets, {len(first_news)} news items")
    print(f"Second collection: {len(second_tweets)} tweets, {len(second_news)} news items")
    print(f"Time-based collection: {len(time_based_tweets)} tweets")
    print(f"Collection comparison: {'PASS' if collection_comparison else 'FAIL'}")

    # Overall result
    if collection_comparison:
        print("\nINTEGRATION TEST: PASS")
        print("The XCollector works consistently across different usage patterns.")
    else:
        print("\nINTEGRATION TEST: FAIL")
        print("The XCollector shows inconsistent results across different usage patterns.")

    logger.info("X integration tests completed")

if __name__ == "__main__":
    main()
