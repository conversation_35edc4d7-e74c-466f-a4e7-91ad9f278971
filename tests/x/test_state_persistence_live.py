"""
Live test for state persistence in XCollector.

This script tests the state persistence functionality with the real X API.
It performs a small collection, verifies state is saved after each page,
and tests resuming from the saved state.
"""

import pytest
import sys
import time
from datetime import datetime, timedelta

# Import the XCollector and XStateManager classes
from sources.x.collector import XCollector
from sources.x.state_manager import XStateManager
from core.config import X_LIST_ID
from core.utils.logging import get_logger

logger = get_logger(__name__)


@pytest.mark.integration
@pytest.mark.slow
def test_state_persistence():
    """Test state persistence with the real X API."""
    logger.info("Starting state persistence test with real X API")

    # Create collector and state manager
    collector = XCollector()
    state_manager = XStateManager()

    # Calculate time window for a small recent period (last 7 days)
    # X API doesn't allow future dates, so we need to use past dates
    end_time = datetime.now() - timedelta(days=1)  # Yesterday
    start_time = end_time - timedelta(days=7)  # 7 days before yesterday

    logger.info(f"Time window: {start_time.isoformat()} to {end_time.isoformat()}")

    # Step 1: Start a new collection with a small max_results to ensure pagination
    logger.info("Step 1: Starting initial collection with small max_results")
    tweets, newest_id, next_token, _ = collector.fetch_x_list_tweets(
        list_id=X_LIST_ID,
        start_time=start_time,
        end_time=end_time,
        max_results=10,  # Small number to ensure we don't hit rate limits
        save_to_db=False  # Don't save to DB since we're just testing
    )

    logger.info(f"Initial collection fetched {len(tweets)} tweets")
    logger.info(f"Newest tweet ID: {newest_id}")
    logger.info(f"Next pagination token: {next_token}")

    # Get the collection ID from the state manager
    collection_id = state_manager.active_collection_id

    # If we don't have a collection ID (e.g., database table doesn't exist),
    # we can still test the pagination token functionality directly
    if not collection_id:
        logger.warning("No active collection found - database table may not exist")
        logger.warning("Skipping state persistence tests, but will still test pagination token")
        collection_id = None
    else:
        logger.info(f"Collection ID: {collection_id}")

        # Step 2: Check if state was saved
        collection_state = state_manager.get_collection(collection_id)
        if collection_state:
            logger.info("Collection state:")
            for key, value in collection_state.items():
                logger.info(f"  {key}: {value}")
        else:
            logger.warning("No collection state found")

    # Step 3: If there's a next token, test direct resumption with the token
    if next_token:
        logger.info("Step 3: Testing direct resumption with pagination token")

        # Wait a moment to make the timestamps distinct
        time.sleep(2)

        # Test direct resumption with the pagination token
        logger.info(f"Resuming directly with token: {next_token}")
        direct_tweets, direct_newest_id, direct_next_token, _ = collector.fetch_x_list_tweets(
            list_id=X_LIST_ID,
            start_time=start_time,
            end_time=end_time,
            max_results=10,
            pagination_token=next_token,
            save_to_db=False  # Don't save to DB since we're just testing
        )

        logger.info(f"Direct resumption fetched {len(direct_tweets)} tweets")

        # Step 4: Test the resume_collection method (only if we have a collection ID)
        if collection_id:
            logger.info("Step 4: Testing resume_collection method")

            # Wait a moment to make the timestamps distinct
            time.sleep(2)

            # Resume collection using the resume_collection method
            resumed_tweets, is_complete = collector.resume_collection(
                collection_id=collection_id,
                max_results=10,  # Small number to ensure we don't hit rate limits
                save_to_db=False  # Don't save to DB since we're just testing
            )

            logger.info(f"Resumed collection fetched {len(resumed_tweets)} additional tweets")
            logger.info(f"Collection complete: {is_complete}")

            # Check updated state
            updated_state = state_manager.get_collection(collection_id)
            if updated_state:
                logger.info("Updated collection state:")
                for key, value in updated_state.items():
                    logger.info(f"  {key}: {value}")

                # Verify that the state was updated
                if 'updated_at' in updated_state and collection_state and 'updated_at' in collection_state:
                    if updated_state.get('updated_at') != collection_state.get('updated_at'):
                        logger.info("State was successfully updated during resume")
                    else:
                        logger.warning("State was not updated during resume")
            else:
                logger.warning("No updated collection state found")
        else:
            logger.warning("Skipping resume_collection test since we don't have a collection ID")
    else:
        logger.info("No next token available, skipping resume test")

    logger.info("State persistence test completed")
    return True


if __name__ == "__main__":
    success = test_state_persistence()
    if success:
        logger.info("Test completed successfully")
    else:
        logger.error("Test failed")
        sys.exit(1)
