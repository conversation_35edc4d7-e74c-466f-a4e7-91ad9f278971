"""
Test for resuming collection in XCollector.
"""

import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Import the XCollector class
from sources.x.collector import XCollector
from core.utils.logging import get_logger

logger = get_logger(__name__)


class TestResumeCollection(unittest.TestCase):
    """Test resuming collection in XCollector."""

    @patch('sources.x.collector.XApiClient')
    @patch('sources.x.collector.XStateManager')
    @patch('sources.x.collector.XTweetProcessor')
    @patch('sources.x.collector.save_tweet_items')
    def test_resume_with_token(self, mock_save_tweets, mock_tweet_processor_class,
                              mock_state_manager_class, mock_api_client_class):
        """Test resuming collection with a valid pagination token."""
        # Set up mocks
        mock_api_client = MagicMock()
        mock_state_manager = MagicMock()
        mock_tweet_processor = MagicMock()

        mock_api_client_class.return_value = mock_api_client
        mock_state_manager_class.return_value = mock_state_manager
        mock_tweet_processor_class.return_value = mock_tweet_processor

        # Mock the get_resume_parameters method to return test parameters
        mock_state_manager.get_resume_parameters.return_value = {
            'collection_id': 'test_collection_id',
            'start_time': datetime.now() - timedelta(days=7),
            'end_time': datetime.now(),
            'pagination_token': 'test_pagination_token',
            'newest_tweet_id': '1234567890',
            'oldest_tweet_id': '0987654321'
        }

        # Mock the fetch_x_list_tweets method to return test data
        mock_api_client.get_all_list_tweets.return_value = (
            # Raw tweets
            [{'id': '1', 'text': 'Tweet 1'}, {'id': '2', 'text': 'Tweet 2'}],
            # Metadata
            {'newest_id': '2', 'oldest_id': '1', 'next_token': None}
        )

        # Set up the tweet processor to return processed tweets
        mock_tweet_processor.process_api_response.return_value = [
            MagicMock(tweet_id='1'),
            MagicMock(tweet_id='2')
        ]

        # Create the collector and call resume_collection
        collector = XCollector()
        tweets, is_complete = collector.resume_collection(
            collection_id='test_collection_id',
            max_results=10
        )

        # Verify that get_resume_parameters was called
        mock_state_manager.get_resume_parameters.assert_called_once_with('test_collection_id')

        # Verify that fetch_x_list_tweets was called with the pagination token
        call_args = mock_api_client.get_all_list_tweets.call_args[1]
        self.assertEqual(call_args['initial_pagination_token'], 'test_pagination_token')

        # Verify that complete_collection was called (since next_token is None)
        # Note: It might be called more than once due to the implementation
        self.assertTrue(mock_state_manager.complete_collection.called)
        # Verify it was called with the right collection_id
        _, kwargs = mock_state_manager.complete_collection.call_args_list[-1]
        self.assertEqual(kwargs['collection_id'], 'test_collection_id')

        # Verify the return values
        self.assertEqual(len(tweets), 2)
        self.assertTrue(is_complete)

    @patch('sources.x.collector.XApiClient')
    @patch('sources.x.collector.XStateManager')
    @patch('sources.x.collector.XTweetProcessor')
    @patch('sources.x.collector.save_tweet_items')
    def test_resume_without_token(self, mock_save_tweets, mock_tweet_processor_class,
                                 mock_state_manager_class, mock_api_client_class):
        """Test resuming collection without a pagination token."""
        # Set up mocks
        mock_api_client = MagicMock()
        mock_state_manager = MagicMock()
        mock_tweet_processor = MagicMock()

        mock_api_client_class.return_value = mock_api_client
        mock_state_manager_class.return_value = mock_state_manager
        mock_tweet_processor_class.return_value = mock_tweet_processor

        # Mock the get_resume_parameters method to return test parameters without a token
        mock_state_manager.get_resume_parameters.return_value = {
            'collection_id': 'test_collection_id',
            'start_time': datetime.now() - timedelta(days=7),
            'end_time': datetime.now(),
            'pagination_token': None,
            'newest_tweet_id': '1234567890',
            'oldest_tweet_id': '0987654321'
        }

        # Mock the fetch_x_list_tweets method to return test data
        mock_api_client.get_all_list_tweets.return_value = (
            # Raw tweets
            [{'id': '1', 'text': 'Tweet 1'}, {'id': '2', 'text': 'Tweet 2'}],
            # Metadata
            {'newest_id': '2', 'oldest_id': '1', 'next_token': None}
        )

        # Set up the tweet processor to return processed tweets
        mock_tweet_processor.process_api_response.return_value = [
            MagicMock(tweet_id='1'),
            MagicMock(tweet_id='2')
        ]

        # Create the collector and call resume_collection
        collector = XCollector()
        tweets, is_complete = collector.resume_collection(
            collection_id='test_collection_id',
            max_results=10
        )

        # Verify that get_resume_parameters was called
        mock_state_manager.get_resume_parameters.assert_called_once_with('test_collection_id')

        # Verify that fetch_x_list_tweets was called without a pagination token
        # The initial_pagination_token parameter will be None, which is fine
        call_args = mock_api_client.get_all_list_tweets.call_args[1]
        self.assertIsNone(call_args.get('initial_pagination_token'))

        # Verify the return values
        self.assertEqual(len(tweets), 2)
        self.assertTrue(is_complete)


if __name__ == '__main__':
    unittest.main()
