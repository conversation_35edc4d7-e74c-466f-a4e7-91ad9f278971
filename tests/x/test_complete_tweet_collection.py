#!/usr/bin/env python3
"""
Test script for the complete tweet collection function.

This script tests the new collect_all_tweets_complete function in the XCollector class.
"""

import os
from datetime import datetime

from sources.x.collector import XCollector
from core.storage import init_db
from core.config import X_LIST_ID
from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_complete_tweet_collection():
    """Test the complete tweet collection function."""
    print("\n=== Testing Complete Tweet Collection ===\n")
    
    # Initialize the database
    init_db()
    
    # Create collector
    collector = XCollector()
    
    # Run the complete collection process with limited parameters for testing
    tweets, collection_id, is_complete, metrics = collector.collect_all_tweets_complete(
        max_results_per_run=10,  # Small value for testing
        days=3,                  # Look back only 3 days for testing
        max_attempts=2,          # Only make 2 attempts for testing
        delay_between_attempts=5 # Short delay for testing
    )
    
    # Print results
    print("\n=== Collection Results ===")
    print(f"Total tweets collected: {len(tweets)}")
    print(f"Collection ID: {collection_id}")
    print(f"Collection complete: {'Yes' if is_complete else 'No'}")
    print(f"Attempts made: {metrics['attempts']}")
    print(f"Start time: {metrics['start_time']}")
    print(f"End time: {metrics['end_time']}")
    
    # Print sample of tweets
    if tweets:
        print("\n=== Sample Tweets ===")
        for i, tweet in enumerate(tweets[:3]):  # Show first 3 tweets
            print(f"\nTweet {i+1}:")
            print(f"ID: {tweet.tweet_id}")
            print(f"Author: @{tweet.author_username}")
            print(f"Date: {tweet.created_at}")
            print(f"Text: {tweet.text[:100]}...")
    
    # Verify the function works correctly
    assert collection_id, "Collection ID should not be empty"
    assert metrics["attempts"] > 0, "Should have made at least one attempt"
    assert metrics["start_time"], "Start time should be recorded"
    assert metrics["end_time"], "End time should be recorded"
    
    print("\n=== Test Completed Successfully ===")
    return True

if __name__ == "__main__":
    test_complete_tweet_collection()
