#!/usr/bin/env python3
"""
Test script for the X List collector component.
This script fetches tweets from a specified X List using the enhanced TweetItem model.
"""

from datetime import datetime

from sources.x.collector import XCollector
from core.utils.logging import get_logger

# This test script needs file logging 
logger = get_logger(__name__, add_file_handler=True)

def main():
    """Test the X List collector functionality."""
    logger.info("Starting X List collector test")

    # Set a small limit for testing purposes
    test_limit = 5  # Only fetch 5 tweets for testing

    # Fetch news items from X List with a small limit
    logger.info(f"Fetching a small sample of {test_limit} tweets for testing")

    # Create a collector instance
    collector = XCollector()

    # Use the collector to fetch tweets
    logger.info(f"Attempting to fetch {test_limit} tweets from X List")
    tweets, newest_id, _, _ = collector.fetch_x_list_tweets(max_results=test_limit)

    if tweets:
        logger.info(f"Successfully fetched {len(tweets)} tweets from X List")
        if newest_id:
            logger.info(f"Newest tweet ID: {newest_id}")
    else:
        logger.warning("No tweets were fetched from X List")

    # Display results
    if tweets:
        print(f"\n=== Fetched {len(tweets)} Tweets from X List ===")
        for i, tweet in enumerate(tweets[:10], 1):  # Print first 10
            print(f"\n[{i}] Tweet ID: {tweet.tweet_id}")
            print(f"Text: {tweet.text}")

            # Display user information if available
            if tweet.author_username:
                print(f"Author: @{tweet.author_username} ({tweet.author_name})")
            else:
                print(f"Author ID: {tweet.author_id}")

            print(f"Published: {tweet.created_at.strftime('%Y-%m-%d %H:%M')}")

            # Display tweet type
            tweet_type = []
            if tweet.is_retweet: tweet_type.append("Retweet")
            if tweet.is_quote: tweet_type.append("Quote")
            if tweet.is_reply: tweet_type.append("Reply")
            if tweet_type:
                print(f"Type: {', '.join(tweet_type)}")

            # Display commentary status
            print(f"Has Commentary: {'Yes' if tweet.has_commentary else 'No'}")

            # Display related tweet IDs if any
            if tweet.in_reply_to_tweet_id:
                print(f"In reply to tweet: {tweet.in_reply_to_tweet_id}")
            if tweet.in_reply_to_user_id:
                print(f"In reply to user: {tweet.in_reply_to_user_id}")
            if tweet.quoted_tweet_id:
                print(f"Quotes tweet: {tweet.quoted_tweet_id}")
            if tweet.retweeted_tweet_id:
                print(f"Retweets tweet: {tweet.retweeted_tweet_id}")

            # Display metrics
            print(f"Metrics: {tweet.like_count} likes, {tweet.retweet_count} retweets, {tweet.reply_count} replies, {tweet.quote_count} quotes")
            if tweet.impression_count:
                print(f"Impressions: {tweet.impression_count}")
            if tweet.bookmark_count:
                print(f"Bookmarks: {tweet.bookmark_count}")

            # Display URLs if any
            if tweet.urls:
                print(f"URLs: {', '.join(str(url) for url in tweet.urls)}")

            # Display hashtags and mentions if any
            if tweet.hashtags:
                print(f"Hashtags: {', '.join('#' + tag for tag in tweet.hashtags)}")
            if tweet.mentions:
                print(f"Mentions: {', '.join('@' + mention for mention in tweet.mentions)}")
    else:
        print("No tweets fetched from X List.")

    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
