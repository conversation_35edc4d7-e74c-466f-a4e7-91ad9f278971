#!/usr/bin/env python3
"""
Test script for time-based tweet collection.
This script tests the date-to-ID mapping and time window validation.
"""

from datetime import datetime, timedelta
from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_date_to_id_mapping():
    """Test the date-to-ID mapping functionality."""
    logger.info("Testing date-to-ID mapping...")

    try:
        from sources.x.api_client import XApiClient

        # Test dates
        test_dates = [
            datetime.now(),
            datetime.now() - timedelta(days=1),
            datetime.now() - timedelta(days=7),
            datetime.now() - timedelta(days=30)
        ]

        for dt in test_dates:
            # Convert date to ID
            tweet_id = XApiClient.date_to_tweet_id(dt)

            # Convert ID back to date
            converted_dt = XApiClient.tweet_id_to_date(tweet_id)

            # Calculate difference in seconds
            diff_seconds = abs((dt - converted_dt).total_seconds())

            logger.info(f"Date: {dt.isoformat()}")
            logger.info(f"Tweet ID: {tweet_id}")
            logger.info(f"Converted back to date: {converted_dt.isoformat()}")
            logger.info(f"Difference: {diff_seconds:.2f} seconds")
            logger.info("-" * 50)

        return True

    except Exception as e:
        logger.error(f"Error testing date-to-ID mapping: {e}")
        return False

def test_time_window_validation():
    """Test the time window validation functionality."""
    logger.info("Testing time window validation...")

    try:
        from sources.x.api_client import XApiClient

        # Test cases
        now = datetime.now()
        test_cases = [
            # Valid cases
            {"start_time": now - timedelta(days=1), "end_time": now, "expected": True, "name": "Past 24 hours"},
            {"start_time": now - timedelta(days=7), "end_time": now, "expected": True, "name": "Past week"},
            {"start_time": None, "end_time": now, "expected": True, "name": "Until now"},
            {"start_time": now - timedelta(days=1), "end_time": None, "expected": True, "name": "Since yesterday"},
            {"start_time": None, "end_time": None, "expected": True, "name": "No time window"},

            # Invalid cases
            {"start_time": now + timedelta(days=1), "end_time": now + timedelta(days=2), "expected": False, "name": "Future dates"},
            {"start_time": now, "end_time": now - timedelta(days=1), "expected": False, "name": "End before start"},
            {"start_time": now - timedelta(days=10), "end_time": now + timedelta(days=1), "expected": False, "name": "End in future"},
            {"start_time": now - timedelta(days=60), "end_time": now, "expected": False, "name": "Start too old"},
            {"start_time": now - timedelta(days=6), "end_time": now - timedelta(days=2), "expected": True, "name": "Past window"}
        ]

        results = []
        for case in test_cases:
            is_valid, message = XApiClient.validate_time_window(case["start_time"], case["end_time"])

            result = {
                "name": case["name"],
                "expected": case["expected"],
                "actual": is_valid,
                "message": message,
                "passed": is_valid == case["expected"]
            }

            results.append(result)

            logger.info(f"Test case: {case['name']}")
            logger.info(f"Start time: {case['start_time']}")
            logger.info(f"End time: {case['end_time']}")
            logger.info(f"Expected: {case['expected']}")
            logger.info(f"Actual: {is_valid}")
            logger.info(f"Message: {message}")
            logger.info(f"Result: {'PASS' if result['passed'] else 'FAIL'}")
            logger.info("-" * 50)

        # Calculate overall result
        passed = all(result["passed"] for result in results)
        logger.info(f"Overall result: {'PASS' if passed else 'FAIL'}")

        return passed, results

    except Exception as e:
        logger.error(f"Error testing time window validation: {e}")
        return False, []

def test_tweet_in_time_window():
    """Test the is_tweet_in_time_window functionality."""
    logger.info("Testing is_tweet_in_time_window...")

    try:
        from sources.x.api_client import XApiClient

        # Test cases
        now = datetime.now()
        yesterday = now - timedelta(days=1)
        last_week = now - timedelta(days=7)

        test_cases = [
            # Tweet created now
            {"tweet_time": now, "start_time": yesterday, "end_time": now + timedelta(hours=1), "expected": True, "name": "Tweet now, window includes now"},
            {"tweet_time": now, "start_time": now + timedelta(hours=1), "end_time": now + timedelta(hours=2), "expected": False, "name": "Tweet now, window in future"},
            {"tweet_time": now, "start_time": last_week, "end_time": yesterday, "expected": False, "name": "Tweet now, window in past"},

            # Tweet created yesterday
            {"tweet_time": yesterday, "start_time": last_week, "end_time": now, "expected": True, "name": "Tweet yesterday, window includes yesterday"},
            {"tweet_time": yesterday, "start_time": None, "end_time": now, "expected": True, "name": "Tweet yesterday, no start time"},
            {"tweet_time": yesterday, "start_time": yesterday, "end_time": None, "expected": True, "name": "Tweet yesterday, no end time"},

            # No time window
            {"tweet_time": now, "start_time": None, "end_time": None, "expected": True, "name": "No time window"}
        ]

        results = []
        for case in test_cases:
            is_in_window = XApiClient.is_tweet_in_time_window(case["tweet_time"], case["start_time"], case["end_time"])

            result = {
                "name": case["name"],
                "expected": case["expected"],
                "actual": is_in_window,
                "passed": is_in_window == case["expected"]
            }

            results.append(result)

            logger.info(f"Test case: {case['name']}")
            logger.info(f"Tweet time: {case['tweet_time']}")
            logger.info(f"Start time: {case['start_time']}")
            logger.info(f"End time: {case['end_time']}")
            logger.info(f"Expected: {case['expected']}")
            logger.info(f"Actual: {is_in_window}")
            logger.info(f"Result: {'PASS' if result['passed'] else 'FAIL'}")
            logger.info("-" * 50)

        # Calculate overall result
        passed = all(result["passed"] for result in results)
        logger.info(f"Overall result: {'PASS' if passed else 'FAIL'}")

        return passed, results

    except Exception as e:
        logger.error(f"Error testing is_tweet_in_time_window: {e}")
        return False, []

def test_time_based_collection():
    """Test the time-based collection functionality with the API."""
    logger.info("Testing time-based collection with API...")

    try:
        from sources.x.collector import XCollector

        # Create collector
        collector = XCollector()

        # Define a time window for the past day
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)

        logger.info(f"Fetching tweets from {start_time.isoformat()} to {end_time.isoformat()}")

        # Fetch tweets with time window
        tweets, newest_id, next_token, _ = collector.fetch_x_list_tweets(
            start_time=start_time,
            end_time=end_time,
            max_results=10
        )

        logger.info(f"Fetched {len(tweets)} tweets")

        # Check that all tweets are within the time window
        in_window_count = 0
        for tweet in tweets:
            if start_time <= tweet.created_at <= end_time:
                in_window_count += 1
            else:
                logger.warning(f"Tweet {tweet.tweet_id} is outside the time window: {tweet.created_at.isoformat()}")

        logger.info(f"{in_window_count} out of {len(tweets)} tweets are within the time window")

        # Calculate result
        passed = in_window_count == len(tweets)
        logger.info(f"Result: {'PASS' if passed else 'FAIL'}")

        return passed, tweets

    except Exception as e:
        logger.error(f"Error testing time-based collection: {e}")
        return False, []

def main():
    """Run the time-based collection tests."""
    logger.info("Starting time-based collection tests")

    # Test date-to-ID mapping
    mapping_result = test_date_to_id_mapping()

    # Test time window validation
    validation_result, validation_details = test_time_window_validation()

    # Test is_tweet_in_time_window
    window_result, window_details = test_tweet_in_time_window()

    # Test time-based collection with API
    collection_result, collection_tweets = test_time_based_collection()

    # Print summary
    print("\n=== Test Summary ===")
    print(f"Date-to-ID mapping: {'PASS' if mapping_result else 'FAIL'}")
    print(f"Time window validation: {'PASS' if validation_result else 'FAIL'}")
    print(f"Tweet in time window: {'PASS' if window_result else 'FAIL'}")
    print(f"Time-based collection: {'PASS' if collection_result else 'FAIL'}")

    # Overall result
    overall_result = all([mapping_result, validation_result, window_result, collection_result])
    print(f"\nOverall result: {'PASS' if overall_result else 'FAIL'}")

    logger.info("Time-based collection tests completed")

if __name__ == "__main__":
    main()
