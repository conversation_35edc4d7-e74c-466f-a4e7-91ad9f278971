"""
Test for resuming pagination in XApiClient.

This script tests resuming pagination using an initial pagination token.
"""

from datetime import datetime, timedelta

# Import the XApiClient class
from sources.x.api_client import XApiClient
from core.config import X_LIST_ID
from core.utils.logging import get_logger

logger = get_logger(__name__)


def test_resume_pagination():
    """Test resuming pagination with an initial token."""
    logger.info("Starting resume pagination test")
    
    # Create API client
    api_client = XApiClient()
    
    # Step 1: Make an initial request to get a pagination token
    logger.info("Step 1: Making initial request to get a pagination token")
    tweets1, metadata1 = api_client.get_all_list_tweets(
        list_id=X_LIST_ID,
        max_results=5,  # Small number to ensure pagination
        max_pages=1     # Only get the first page
    )
    
    logger.info(f"Initial request fetched {len(tweets1)} tweets")
    
    # Get the pagination token
    next_token = metadata1.get('next_token')
    logger.info(f"Pagination token: {next_token}")
    
    if not next_token:
        logger.warning("No pagination token returned, cannot test resumption")
        return False
    
    # Step 2: Make a second request using the pagination token
    logger.info("Step 2: Making second request with pagination token")
    tweets2, metadata2 = api_client.get_all_list_tweets(
        list_id=X_LIST_ID,
        max_results=5,
        max_pages=1,
        initial_pagination_token=next_token
    )
    
    logger.info(f"Resumed request fetched {len(tweets2)} tweets")
    
    # Verify that the tweets from the second request are different from the first
    if len(tweets1) > 0 and len(tweets2) > 0:
        first_ids = set(tweet.get('id') for tweet in tweets1)
        second_ids = set(tweet.get('id') for tweet in tweets2)
        
        # Check if there's no overlap between the two sets of tweets
        if not first_ids.intersection(second_ids):
            logger.info("Success: Resumed request returned different tweets")
            return True
        else:
            logger.warning("Warning: Some tweets appear in both requests")
            return False
    else:
        logger.warning("Not enough tweets to compare")
        return False


if __name__ == "__main__":
    success = test_resume_pagination()
    if success:
        logger.info("Test completed successfully")
    else:
        logger.warning("Test completed with issues")
        # Don't exit with error code since this might be expected in some cases
