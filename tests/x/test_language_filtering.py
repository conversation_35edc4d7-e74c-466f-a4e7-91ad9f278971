#!/usr/bin/env python3
"""
Test script for English language filtering in X integration.
This script tests that tweets are correctly filtered by language.
"""

import sys
from datetime import datetime
from core.utils.logging import get_logger

logger = get_logger(__name__)

def test_api_language_filter():
    """Test that the API language filter is working."""
    logger.info("Testing API language filter...")

    try:
        from sources.x.api_client import XApiClient

        # Create API client
        client = XApiClient()

        # Fetch tweets with language filter
        list_id = "1906777783583584471"  # Use the configured list ID
        tweets, metadata, _ = client.get_list_tweets(list_id=list_id, max_results=10)

        logger.info(f"Fetched {len(tweets)} tweets with language filter")

        # Check if the language field was included in the tweet fields
        params = metadata.get('params', {})
        tweet_fields = params.get('tweet.fields', '')
        if 'lang' in tweet_fields:
            logger.info("Language field was included in the request")
        else:
            logger.warning("Language field was NOT included in the request")

        # Return the tweets for further analysis
        return tweets, metadata

    except Exception as e:
        logger.error(f"Error testing API language filter: {e}")
        return [], {}

def test_processor_language_detection():
    """Test the processor's language detection."""
    logger.info("Testing processor language detection...")

    try:
        from sources.x.tweet_processor import XTweetProcessor

        # Create processor
        processor = XTweetProcessor(english_only=True)

        # Test tweets in different languages
        test_tweets = [
            # English tweet
            {
                'id': '1',
                'text': 'This is a test tweet in English about blockchain and crypto.',
                'created_at': '2023-01-01T12:00:00Z',
                'author_id': 'user1',
                'lang': 'en'
            },
            # Non-English tweet with language tag
            {
                'id': '2',
                'text': 'Esto es un tweet de prueba en español sobre blockchain y crypto.',
                'created_at': '2023-01-01T12:00:00Z',
                'author_id': 'user2',
                'lang': 'es'
            },
            # English tweet without language tag
            {
                'id': '3',
                'text': 'Another English tweet about tokenized assets and RWA.',
                'created_at': '2023-01-01T12:00:00Z',
                'author_id': 'user3'
            },
            # Non-English tweet without language tag
            {
                'id': '4',
                'text': 'Ceci est un tweet en français à propos de la blockchain.',
                'created_at': '2023-01-01T12:00:00Z',
                'author_id': 'user4'
            }
        ]

        # Process the test tweets
        processed_tweets = []
        for tweet_data in test_tweets:
            try:
                tweet = processor.process_tweet(tweet_data)
                processed_tweets.append(tweet)
                # Check language directly from the tweet data
                lang = tweet_data.get('lang')
                is_english = lang == 'en' if lang else processor._detect_language(tweet_data.get('text', ''))
                logger.info(f"Tweet {tweet.tweet_id}: lang={lang}, is_english={is_english}")
            except Exception as e:
                logger.error(f"Error processing test tweet {tweet_data['id']}: {e}")

        # Filter tweets using the processor
        filtered_tweets = processor.process_tweets(test_tweets)
        logger.info(f"After filtering, {len(filtered_tweets)} out of {len(test_tweets)} tweets remain")

        return processed_tweets, filtered_tweets

    except Exception as e:
        logger.error(f"Error testing processor language detection: {e}")
        return [], []

def test_end_to_end():
    """Test the end-to-end language filtering."""
    logger.info("Testing end-to-end language filtering...")

    try:
        from sources.x.collector import XCollector

        # Create collector
        collector = XCollector()

        # Fetch tweets
        tweets, _, _, _ = collector.fetch_x_list_tweets(max_results=10)

        logger.info(f"Fetched {len(tweets)} tweets")

        # All tweets should be in English due to our filtering
        logger.info(f"Fetched {len(tweets)} tweets, all should be in English")

        # Check if any tweets have notes indicating they're not in English
        non_english_notes = sum(1 for tweet in tweets if tweet.notes and "Not in English" in tweet.notes)
        if non_english_notes > 0:
            logger.warning(f"{non_english_notes} tweets have notes indicating they might not be in English")
        else:
            logger.info("No tweets have notes indicating language issues")

        return tweets

    except Exception as e:
        logger.error(f"Error testing end-to-end language filtering: {e}")
        return []

def main():
    """Run the language filtering tests."""
    logger.info("Starting language filtering tests")

    # Test API language filter
    api_tweets, _ = test_api_language_filter()

    # Test processor language detection
    processed_tweets, filtered_tweets = test_processor_language_detection()

    # Test end-to-end
    end_to_end_tweets = test_end_to_end()

    # Print summary
    print("\n=== Test Summary ===")
    print(f"API language field: {len(api_tweets)} tweets fetched with lang field included")
    print(f"Processor language detection: {len(filtered_tweets)} out of {len(processed_tweets)} tweets passed the English filter")
    print(f"End-to-end: {len(end_to_end_tweets)} tweets fetched through the complete pipeline")

    logger.info("Language filtering tests completed")

if __name__ == "__main__":
    main()
