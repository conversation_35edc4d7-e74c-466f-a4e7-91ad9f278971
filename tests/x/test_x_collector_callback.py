"""Tests for the XCollector's page callback functionality."""

import unittest
from unittest.mock import Mock, patch
from datetime import datetime, timezone

from sources.x.collector import XCollector


class TestXCollectorCallback(unittest.TestCase):
    """Test cases for the XCollector's _create_page_callback method."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.collector = XCollector()
        self.mock_state_manager = Mock()
        self.collector.state_manager = self.mock_state_manager
    
    def test_create_page_callback_returns_none_when_no_collection_id(self):
        """Test that _create_page_callback returns None when collection_id is None."""
        callback = self.collector._create_page_callback(None)
        self.assertIsNone(callback)
    
    def test_create_page_callback_returns_function_when_collection_id_provided(self):
        """Test that _create_page_callback returns a function when collection_id is provided."""
        callback = self.collector._create_page_callback("test-collection-123")
        self.assertIsNotNone(callback)
        self.assertTrue(callable(callback))
    
    @patch('sources.x.collector.now_utc')
    def test_callback_updates_state_manager_correctly(self, mock_now_utc):
        """Test that the callback correctly updates the state manager."""
        # Mock the current time
        mock_time = datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
        mock_now_utc.return_value = mock_time
        
        # Create callback
        collection_id = "test-collection-456"
        callback = self.collector._create_page_callback(collection_id)
        
        # Prepare test data
        metadata = {
            'response_meta': {
                'newest_id': 'tweet-newest-123',
                'oldest_id': 'tweet-oldest-789'
            }
        }
        tweets = ['tweet1', 'tweet2', 'tweet3']  # 3 tweets
        page_count = 2
        next_token = 'next-page-token-xyz'
        
        # Call the callback
        callback(metadata, tweets, page_count, next_token)
        
        # Verify state manager was called correctly
        self.mock_state_manager.update_collection.assert_called_once_with(
            collection_id=collection_id,
            newest_tweet_id='tweet-newest-123',
            oldest_tweet_id='tweet-oldest-789',
            pagination_token='next-page-token-xyz',
            tweets_collected=3,
            requests_made=2,
            last_request_time=mock_time.isoformat(),
            notes='Page 2 fetched'
        )
    
    @patch('sources.x.collector.now_utc')
    def test_callback_handles_empty_tweets_list(self, mock_now_utc):
        """Test that the callback handles an empty tweets list correctly."""
        mock_time = datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
        mock_now_utc.return_value = mock_time
        
        collection_id = "test-collection-789"
        callback = self.collector._create_page_callback(collection_id)
        
        metadata = {'response_meta': {}}
        tweets = []  # Empty list
        page_count = 1
        next_token = None
        
        callback(metadata, tweets, page_count, next_token)
        
        # Verify tweets_collected is 0 for empty list
        call_args = self.mock_state_manager.update_collection.call_args[1]
        self.assertEqual(call_args['tweets_collected'], 0)
    
    @patch('sources.x.collector.now_utc')
    def test_callback_handles_none_tweets(self, mock_now_utc):
        """Test that the callback handles None tweets correctly."""
        mock_time = datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
        mock_now_utc.return_value = mock_time
        
        collection_id = "test-collection-999"
        callback = self.collector._create_page_callback(collection_id)
        
        metadata = {'response_meta': {}}
        tweets = None  # None instead of list
        page_count = 1
        next_token = None
        
        callback(metadata, tweets, page_count, next_token)
        
        # Verify tweets_collected is 0 for None
        call_args = self.mock_state_manager.update_collection.call_args[1]
        self.assertEqual(call_args['tweets_collected'], 0)
    
    @patch('sources.x.collector.now_utc')
    @patch('sources.x.collector.logger')
    def test_callback_logs_update(self, mock_logger, mock_now_utc):
        """Test that the callback logs the update."""
        mock_time = datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
        mock_now_utc.return_value = mock_time
        
        collection_id = "test-collection-log"
        callback = self.collector._create_page_callback(collection_id)
        
        callback({}, [], 5, None)
        
        # Verify logging was called
        mock_logger.info.assert_called_with("Updated collection state after page 5")


if __name__ == '__main__':
    unittest.main()