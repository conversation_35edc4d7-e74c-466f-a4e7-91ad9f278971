"""
Test for rate limit handling in XApiClient.
"""

import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Import the XApiClient class
from sources.x.api_client import XApiClient
from core.utils.logging import get_logger

logger = get_logger(__name__)


class TestRateLimitHandling(unittest.TestCase):
    """Test rate limit handling in XApiClient."""

    def setUp(self):
        """Set up test fixtures."""
        self.client = XApiClient(bearer_token='test_token')

    def test_rate_limit_initialization(self):
        """Test that rate limits are properly initialized."""
        self.assertIsNone(self.client.rate_limits['limit'])
        self.assertIsNone(self.client.rate_limits['remaining'])
        self.assertIsNone(self.client.rate_limits['reset'])
        self.assertIsNone(self.client.rate_limits['last_updated'])

    @patch('requests.Response')
    def test_extract_rate_limit_headers(self, mock_response):
        """Test extracting rate limit headers from response."""
        # Set up mock response with rate limit headers
        mock_response.headers = {
            'x-rate-limit-limit': '300',
            'x-rate-limit-remaining': '299',
            'x-rate-limit-reset': str(int((datetime.now() + timedelta(minutes=15)).timestamp()))
        }
        mock_response.status_code = 200

        # Extract rate limit headers
        rate_limit_info = self.client.extract_rate_limit_headers(mock_response)

        # Verify rate limit info was extracted correctly
        self.assertEqual(rate_limit_info['limit'], '300')
        self.assertEqual(rate_limit_info['remaining'], '299')
        self.assertIsNotNone(rate_limit_info['reset'])

        # Verify internal rate limits were updated
        self.assertEqual(self.client.rate_limits['limit'], 300)
        self.assertEqual(self.client.rate_limits['remaining'], 299)
        self.assertIsNotNone(self.client.rate_limits['reset'])
        self.assertIsNotNone(self.client.rate_limits['last_updated'])

    @patch('requests.get')
    def test_get_list_tweets_updates_rate_limits(self, mock_get):
        """Test that get_list_tweets updates rate limits."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {
            'x-rate-limit-limit': '300',
            'x-rate-limit-remaining': '299',
            'x-rate-limit-reset': str(int((datetime.now() + timedelta(minutes=15)).timestamp()))
        }
        mock_response.json.return_value = {
            'data': [{'id': '1', 'text': 'Test tweet'}],
            'meta': {'result_count': 1}
        }
        mock_get.return_value = mock_response

        # Call get_list_tweets
        tweets, metadata, next_token = self.client.get_list_tweets(list_id='test_list')

        # Verify rate limits were updated
        self.assertEqual(self.client.rate_limits['limit'], 300)
        self.assertEqual(self.client.rate_limits['remaining'], 299)
        self.assertIsNotNone(self.client.rate_limits['reset'])
        self.assertIsNotNone(self.client.rate_limits['last_updated'])

        # Verify rate limit info was included in metadata
        self.assertEqual(metadata['rate_limit']['limit'], '300')
        self.assertEqual(metadata['rate_limit']['remaining'], '299')
        self.assertIsNotNone(metadata['rate_limit']['reset'])

    @patch('requests.get')
    def test_rate_limit_exceeded(self, mock_get):
        """Test handling of rate limit exceeded response."""
        # Set up mock response for rate limit exceeded
        mock_response = MagicMock()
        mock_response.status_code = 429  # Rate limit exceeded
        mock_response.headers = {
            'x-rate-limit-limit': '300',
            'x-rate-limit-remaining': '0',
            'x-rate-limit-reset': str(int((datetime.now() + timedelta(minutes=15)).timestamp()))
        }
        mock_get.return_value = mock_response

        # Call get_list_tweets
        tweets, metadata, next_token = self.client.get_list_tweets(list_id='test_list')

        # Verify empty results were returned
        self.assertEqual(tweets, [])
        self.assertIsNone(next_token)

        # Verify rate limits were updated
        self.assertEqual(self.client.rate_limits['limit'], 300)
        self.assertEqual(self.client.rate_limits['remaining'], 0)
        self.assertIsNotNone(self.client.rate_limits['reset'])

        # Verify rate limit info was included in metadata
        self.assertEqual(metadata['status_code'], 429)
        self.assertEqual(metadata['rate_limit']['limit'], '300')
        self.assertEqual(metadata['rate_limit']['remaining'], '0')


if __name__ == '__main__':
    unittest.main()
