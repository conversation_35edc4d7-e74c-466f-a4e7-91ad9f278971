#!/usr/bin/env python3
"""
Test week boundary transitions and daily-to-weekly rollover functionality.

This test simulates a complete week of daily collections and tests
the transition to a new week.
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, MagicMock

from core.storage.publication_state_pg import (
    update_publication_timestamp,
    is_new_week,
    get_current_weekly_page_id,
    update_weekly_page_id
)
from core.storage.daily_runs_pg import start_run, complete_run, get_recent_runs
from core.storage.news_items_pg import save_news_items, get_news_items_since
from core.models import NewsItem
from core.enums import RelevanceStatus
from core.week_boundary_utils import get_current_week_start, get_week_date_range


class TestWeekTransition(unittest.TestCase):
    """Test week boundary transitions and rollover functionality."""
    
    def setUp(self):
        """Set up each test with mocked database connections."""
        # Mock the database connection pool
        self.mock_pool = MagicMock()
        self.mock_conn = MagicMock()
        self.mock_cursor = MagicMock()
        
        self.mock_pool.getconn.return_value = self.mock_conn
        self.mock_conn.cursor.return_value = self.mock_cursor
        self.mock_conn.__enter__ = Mock(return_value=self.mock_conn)
        self.mock_conn.__exit__ = Mock(return_value=None)
        self.mock_cursor.__enter__ = Mock(return_value=self.mock_cursor)
        self.mock_cursor.__exit__ = Mock(return_value=None)
        
        # Set up default mock return values
        self.mock_cursor.fetchone.return_value = None
        self.mock_cursor.fetchall.return_value = []
        self.mock_cursor.rowcount = 0
        
        # Track saved items for testing
        self.saved_items = []
        
        # Patch get_connection_pool in all relevant modules
        self.patches = []
        modules_to_patch = [
            'core.storage.publication_state_pg',
            'core.storage.daily_runs_pg',
            'core.storage.news_items_pg'
        ]
        
        for module in modules_to_patch:
            patcher = patch(f'{module}.get_connection_pool', return_value=self.mock_pool)
            patcher.start()
            self.patches.append(patcher)
    
    def tearDown(self):
        """Clean up patches."""
        for patcher in self.patches:
            patcher.stop()
    
    def test_simulated_week_progression(self):
        """Simulate a full week of daily collections and test rollover."""
        print("\n=== Testing Simulated Week Progression ===")
        
        # Mock save_news_items to track saved items
        saved_items_count = 0
        def mock_save_news_items(items):
            nonlocal saved_items_count
            self.saved_items.extend(items)
            saved_items_count = len(items)
            return saved_items_count
        
        # Mock start_run and complete_run
        run_id_counter = 0
        completed_runs = []
        def mock_start_run(run_type):
            nonlocal run_id_counter
            run_id_counter += 1
            return run_id_counter
        
        def mock_complete_run(run_id, items_fetched, items_published):
            completed_runs.append({
                'run_id': run_id,
                'run_type': 'daily',
                'items_fetched': items_fetched,
                'items_published': items_published
            })
        
        # Mock get_recent_runs to return our completed runs
        def mock_get_recent_runs(limit):
            return completed_runs[-limit:]
        
        # Mock get_news_items_since to return filtered saved items
        def mock_get_news_items_since(since_date):
            return [item for item in self.saved_items if item.published_at >= since_date]
        
        # Apply mocks
        with patch('core.storage.news_items_pg.save_news_items', side_effect=mock_save_news_items), \
             patch('core.storage.daily_runs_pg.start_run', side_effect=mock_start_run), \
             patch('core.storage.daily_runs_pg.complete_run', side_effect=mock_complete_run), \
             patch('core.storage.daily_runs_pg.get_recent_runs', side_effect=mock_get_recent_runs), \
             patch('core.storage.news_items_pg.get_news_items_since', side_effect=mock_get_news_items_since), \
             patch('core.storage.publication_state_pg.update_publication_timestamp'):
            
            # Simulate 7 days of daily collections
            base_date = datetime.now() - timedelta(days=7)
            
            for day in range(7):
                current_date = base_date + timedelta(days=day)
                print(f"\n--- Day {day + 1} ({current_date.strftime('%Y-%m-%d')}) ---")
                
                # Create daily news items
                daily_items = [
                    NewsItem(
                        title=f"Day {day + 1} News Item 1",
                        link=f"https://example.com/day{day+1}_item1",
                        published_at=current_date + timedelta(hours=9),
                        summary=f"Summary for day {day + 1} item 1",
                        publisher="Test Publisher",
                        source="test_daily",
                        relevance=RelevanceStatus.RELEVANT
                    ),
                    NewsItem(
                        title=f"Day {day + 1} News Item 2", 
                        link=f"https://example.com/day{day+1}_item2",
                        published_at=current_date + timedelta(hours=15),
                        summary=f"Summary for day {day + 1} item 2",
                        publisher="Test Publisher",
                        source="test_daily",
                        relevance=RelevanceStatus.NOT_RELEVANT if day % 2 == 0 else RelevanceStatus.RELEVANT
                    )
                ]
                
                # Save items and track run
                saved_count = save_news_items(daily_items)
                
                run_id = start_run('daily')
                complete_run(run_id, items_fetched=len(daily_items), items_published=saved_count)
                
                # Update publication timestamp
                update_publication_timestamp()
                
                print(f"Completed day {day + 1}: {saved_count} items saved")
            
            # Check total items across the week
            week_start = get_current_week_start()
            all_items = get_news_items_since(week_start - timedelta(days=7))
            print(f"\nTotal items collected over simulated week: {len(all_items)}")
            
            # Check run history
            recent_runs = get_recent_runs(10)
            daily_runs = [r for r in recent_runs if r['run_type'] == 'daily']
            self.assertEqual(len(daily_runs), 7)
            print(f"Logged {len(daily_runs)} daily runs")
            
            # Verify data distribution
            relevant_count = len([item for item in all_items if item.relevance == RelevanceStatus.RELEVANT])
            not_relevant_count = len([item for item in all_items if item.relevance == RelevanceStatus.NOT_RELEVANT])
            print(f"Relevance distribution: {relevant_count} relevant, {not_relevant_count} not relevant")
    
    def test_week_boundary_detection_logic(self):
        """Test the week boundary detection logic in detail."""
        print("\n=== Testing Week Boundary Detection Logic ===")
        
        # Test with different days of the week
        test_dates = [
            datetime(2025, 6, 1, 23, 59, 59),  # Saturday night
            datetime(2025, 6, 2, 0, 0, 1),     # Sunday midnight+1sec (new week)
            datetime(2025, 6, 8, 12, 0, 0),    # Next Sunday noon  
            datetime(2025, 6, 9, 0, 0, 1),     # Monday midnight+1sec (new week)
        ]
        
        for test_date in test_dates:
            with patch('core.week_boundary_utils.datetime') as mock_datetime:
                mock_datetime.now.return_value = test_date
                mock_datetime.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)
                
                week_start = get_current_week_start()
                date_range = get_week_date_range(week_start)
                
                print(f"Test date: {test_date.strftime('%A %Y-%m-%d %H:%M:%S')}")
                print(f"  Week start: {week_start.strftime('%Y-%m-%d')}")
                print(f"  Date range: {date_range}")
                
                # Sunday should be start of week
                self.assertEqual(week_start.weekday(), 6)  # Sunday = 6 in Python
    
    def test_new_week_page_creation_workflow(self):
        """Test the complete workflow of creating a new weekly page."""
        print("\n=== Testing New Week Page Creation Workflow ===")
        
        # Mock the publication state
        mock_state = {
            'weekly_page_id': "existing-week-page-123",
            'week_start': (get_current_week_start() - timedelta(days=7)).isoformat()
        }
        
        def mock_get_current_weekly_page_id():
            return mock_state['weekly_page_id']
        
        def mock_update_weekly_page_id(page_id, week_start):
            mock_state['weekly_page_id'] = page_id
            mock_state['week_start'] = week_start.isoformat()
        
        def mock_is_new_week():
            stored_week_start = datetime.fromisoformat(mock_state['week_start'])
            current_week_start = get_current_week_start()
            return stored_week_start < current_week_start
        
        with patch('core.storage.publication_state_pg.get_current_weekly_page_id', side_effect=mock_get_current_weekly_page_id), \
             patch('core.storage.publication_state_pg.update_weekly_page_id', side_effect=mock_update_weekly_page_id), \
             patch('core.storage.publication_state_pg.is_new_week', side_effect=mock_is_new_week):
            
            # Start with existing weekly page
            existing_page_id = "existing-week-page-123"
            current_page = get_current_weekly_page_id()
            self.assertEqual(current_page, existing_page_id)
            print(f"Starting with existing page: {current_page}")
            
            # Check if it detects new week (should be True since we set past week start)
            is_new = is_new_week()
            print(f"New week detected: {is_new}")
            self.assertTrue(is_new)
            
            # Simulate creating new page  
            new_page_id = "new-week-page-456"
            current_week_start = get_current_week_start()
            update_weekly_page_id(new_page_id, current_week_start)
            
            updated_page = get_current_weekly_page_id()
            self.assertEqual(updated_page, new_page_id)
            print(f"Created new weekly page: {updated_page}")
            
            # Now check again - should not be new week
            is_new_again = is_new_week()
            self.assertFalse(is_new_again)
            print(f"Subsequent check shows same week: {not is_new_again}")
    
    def test_daily_collection_hours_filtering(self):
        """Test filtering items by daily collection hours."""
        print("\n=== Testing Daily Collection Hours Filtering ===")
        
        now = datetime.now()
        
        # Create items at different time intervals
        test_items = [
            # Within 24 hours
            NewsItem(
                title="Recent Item 1",
                link="https://example.com/recent1",
                published_at=now - timedelta(hours=2),
                summary="Recent summary 1", 
                publisher="Test Publisher",
                source="test_recent"
            ),
            # Within 24 hours 
            NewsItem(
                title="Recent Item 2",
                link="https://example.com/recent2",
                published_at=now - timedelta(hours=12),
                summary="Recent summary 2",
                publisher="Test Publisher", 
                source="test_recent"
            ),
            # Older than 24 hours
            NewsItem(
                title="Old Item",
                link="https://example.com/old",
                published_at=now - timedelta(hours=30),
                summary="Old summary",
                publisher="Test Publisher",
                source="test_old"
            )
        ]
        
        # Mock save_news_items and get_news_items_since
        saved_items = []
        
        def mock_save_news_items(items):
            saved_items.extend(items)
            return len(items)
        
        def mock_get_news_items_since(since_date):
            # Simulate fetched_at timestamp being current time
            return [item for item in saved_items if hasattr(item, 'fetched_at') or True]
        
        with patch('core.storage.news_items_pg.save_news_items', side_effect=mock_save_news_items), \
             patch('core.storage.news_items_pg.get_news_items_since', side_effect=mock_get_news_items_since):
            
            save_news_items(test_items)
            
            # Test different collection windows
            for hours in [6, 12, 24, 48]:
                cutoff = now - timedelta(hours=hours)
                items = get_news_items_since(cutoff)
                
                # Note: get_news_items_since filters by fetched_at, not published_at
                # So we expect all items since they were all fetched recently
                print(f"Last {hours} hours: {len(items)} items found")
            
            print("Daily collection hours filtering tested")


if __name__ == '__main__':
    unittest.main(verbosity=2)