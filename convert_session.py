#!/usr/bin/env python3
"""
Convert existing Telegram session file to base64 string for GitHub Actions.

SECURITY NOTE: This script outputs the session string to console only.
Never save session strings to files!
"""

import os
import base64
from telethon.sync import TelegramClient
from telethon.sessions import StringSession
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def convert_existing_session():
    # Get credentials from .env
    api_id_str = os.getenv('TELEGRAM_API_ID')
    api_hash = os.getenv('TELEGRAM_API_HASH')
    
    if not api_id_str or not api_hash:
        print("Error: TELEGRAM_API_ID and TELEGRAM_API_HASH must be set in .env file")
        return
    
    try:
        api_id = int(api_id_str)
    except ValueError:
        print(f"Error: TELEGRAM_API_ID must be a number, got: {api_id_str}")
        return
    
    print(f"Using API ID: {api_id}")
    print("Loading existing session...\n")
    
    session_file = 'tac_news_session.session'
    if not os.path.exists(session_file):
        print(f"Error: Session file '{session_file}' not found!")
        print("Please run generate_telegram_session.py first to create a session.")
        return
    
    try:
        # Load the existing session file
        with TelegramClient('tac_news_session', api_id, api_hash) as client:
            # Export to string session
            string_session = StringSession.save(client.session)
            
            # Encode it in base64 for GitHub secrets
            encoded = base64.b64encode(string_session.encode()).decode()
            
            print("\n" + "="*80)
            print("✅ SESSION CONVERTED SUCCESSFULLY!")
            print("="*80)
            
            print("\n📋 Your session string (for GitHub Secrets):")
            print("-" * 80)
            print(encoded)
            print("-" * 80)
            
            print("\n📝 Add this to GitHub Secrets:")
            print("1. Go to your GitHub repository")
            print("2. Click Settings → Secrets and variables → Actions")
            print("3. Click 'New repository secret'")
            print("4. Name: TELEGRAM_SESSION_STRING")
            print("5. Value: Paste the string above")
            print("6. Click 'Add secret'")
            
            print("\n⚠️  SECURITY WARNING:")
            print("- This session string gives FULL access to your Telegram account")
            print("- NEVER commit it to your repository")
            print("- NEVER share it publicly")
            print("- Revoke it immediately if compromised")
            
            print("="*80)
            
    except Exception as e:
        print(f"\n❌ Error converting session: {e}")

if __name__ == "__main__":
    convert_existing_session()