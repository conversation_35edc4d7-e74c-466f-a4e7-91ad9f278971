import argparse
from datetime import datetime
from datetime import timedelta
import threading
import faulthandler

# Import core modules
from core.config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SAMPLE_SIZE, GOOGLE_FORM_SHEET_ID, NOTION_API_TOKEN, NOTION_PAGE_ID
from core.utils.logging import get_logger
from core.timezone_config import now_utc
from core.storage import init_db
from core.storage.news_items_pg import save_news_items, update_item_relevance, get_news_items_since
from core.storage.publication_state_pg import init_publication_state, update_publication_timestamp, get_current_weekly_page_id, is_new_week, update_weekly_page_id
from core.storage.daily_runs_pg import init_daily_runs, start_run, complete_run
from core.week_boundary_utils import get_current_week_start
from core.enums import RelevanceStatus
from core.interfaces.llm_preparable_interface import LLMPreparable
from core.models import NewsItem, TweetItem
from core.ai_relevance_judge import evaluate_item_relevance
from core.storage.news_items_pg import update_item_relevance as update_news_item_relevance_status
from core.storage.tweet_items_pg import save_tweet_items, update_tweet_relevance as update_tweet_item_relevance_status
from sources.notion.notion_publisher import publish_to_notion
from sources.notion.subpage_creator import create_weekly_notion_page, get_notion_client
from core.email_alerts import send_time_sensitive_email
from core.deduplication import mark_duplicates
from core.time_sensitive_service import get_time_sensitive_items_for_period

# Import source modules
from sources import GoogleNewsDataSource, TelegramDataSource, GoogleFormDataSource, XCollector

# Configure logging with file handler for main script
logger = get_logger(__name__, add_file_handler=True)

def extract_config_for_source(source_name: str, args) -> dict:
    """
    Extract configuration parameters for a specific data source.

    Args:
        source_name: Name of the data source
        args: Command-line arguments

    Returns:
        Dictionary with source-specific configuration parameters
    """
    config = {}

    if source_name == "Google News RSS":
        config = {
            'keywords': KEYWORDS,
            'items_per_keyword': args.google_news_items
        }
    elif source_name == "Telegram":
        config = {
            'limit': args.telegram_limit
        }
    elif source_name == "Google Form Submissions":
        # No specific config needed
        pass
    elif source_name == "X/Twitter List":
        config = {
            'max_results_per_run': args.max_tweet_results,
            'days': args.tweet_collection_days,
            'max_attempts': args.max_tweet_attempts,
            'delay_between_attempts': args.tweet_attempt_delay
        }

    return config

def parse_args():
    """
    Parse command-line arguments for the TAC Daily News Aggregation System.

    Returns:
        argparse.Namespace: The parsed command-line arguments with the following attributes:
            - skip_tweet_collection (bool): Whether to skip tweet collection entirely
            - max_tweet_results (int): Maximum number of results per tweet collection attempt
            - tweet_collection_days (int): Number of days to look back for tweets
            - max_tweet_attempts (int): Maximum number of tweet collection attempts
            - tweet_attempt_delay (int): Delay between tweet collection attempts in seconds
            - skip_google_news (bool): Whether to skip Google News RSS fetching
            - skip_telegram (bool): Whether to skip Telegram channel scraping
            - skip_form (bool): Whether to skip Google Form submissions
            - skip_notion (bool): Whether to skip publishing to Notion
            - skip_relevance_evaluation (bool): Whether to skip the AI relevance evaluation step
            - daily_collection_hours (int): Number of hours to look back for daily collection
    """
    parser = argparse.ArgumentParser(description="TAC Daily News Aggregation System")

    # Tweet collection arguments
    parser.add_argument("--skip-tweet-collection", action="store_true",
                        help="Skip tweet collection entirely")
    parser.add_argument("--max-tweet-results", type=int, default=100,
                        help="Maximum number of results per tweet collection attempt")
    parser.add_argument("--tweet-collection-days", type=int, default=1,
                        help="Number of days to look back for tweets")
    parser.add_argument("--max-tweet-attempts", type=int, default=5,
                        help="Maximum number of tweet collection attempts")
    parser.add_argument("--tweet-attempt-delay", type=int, default=0,
                        help="Delay between tweet collection attempts in seconds")

    # Other arguments
    parser.add_argument("--skip-google-news", action="store_true",
                        help="Skip Google News RSS fetching")
    parser.add_argument("--google-news-items", type=int, default=10,
                        help="Maximum number of items to fetch per Google News keyword")
    parser.add_argument("--skip-telegram", action="store_true",
                        help="Skip Telegram channel scraping")
    parser.add_argument("--telegram-limit", type=int, default=None,
                        help="Override the number of messages to fetch per Telegram channel")
    parser.add_argument("--skip-form", action="store_true",
                        help="Skip Google Form submissions")
    parser.add_argument("--skip-notion", action="store_true",
                        help="Skip publishing to Notion")
    parser.add_argument("--sample-size", type=int, default=None,
                        help="Override the number of items to check with LLM for relevance")
    parser.add_argument("--skip-relevance-evaluation", action="store_true",
                        help="Skip the AI relevance evaluation step (useful for testing downstream steps)")
    
    # Collection time arguments
    parser.add_argument("--daily-collection-hours", type=int, default=24,
                        help="Number of hours to look back for daily collection (default: 24)")
    
    # Email notification arguments
    parser.add_argument("--enable-email-alerts", action="store_true",
                        help="Enable email notifications for time-sensitive content")
    parser.add_argument("--email-dry-run", action="store_true",
                        help="Check for time-sensitive content but don't send emails")

    return parser.parse_args()

def main():
    """
    Main function to run the TAC Daily News Aggregation System.

    This function orchestrates the entire daily collection process:
    1. Initializes the database and publication state
    2. Sets up data sources based on configuration and command-line arguments
    3. Fetches items from all enabled data sources using a standardized interface
    4. Stores all collected items in the database
    5. Performs relevance checks on items that don't have relevance scores yet
    6. Appends items to the current week's Notion page (creates new page on Thursday transitions)
    7. Displays results and logs operations

    The behavior can be customized using command-line arguments parsed by parse_args().
    """
    # Enable faulthandler to get Python stack trace on segfault
    faulthandler.enable()
    logger.info("Faulthandler enabled for segfault debugging")
    
    # Parse command-line arguments
    args = parse_args()

    # Set run mode
    logger.info("Starting TAC Daily News Aggregation System")
    run_type = 'daily'

    # Initialize the database and publication state
    init_db()
    init_publication_state()
    
    # Initialize daily runs tracking
    init_daily_runs()
    run_id = start_run(run_type)
    logger.info(f"Started daily run with ID: {run_id}")

    # Initialize empty list for all news items
    all_fetched_items = []

    # Initialize empty list for data sources
    data_sources = []

    # Initialize source-specific item lists for reporting
    source_items = {}

    # Add Google News data source if not skipped
    if not args.skip_google_news:
        google_news_source = GoogleNewsDataSource()
        data_sources.append(google_news_source)

    # Add Telegram data source if not skipped
    if not args.skip_telegram:
        telegram_source = TelegramDataSource()
        data_sources.append(telegram_source)

    # Add Google Form data source if configured and not skipped
    if GOOGLE_FORM_SHEET_ID and not args.skip_form:
        form_source = GoogleFormDataSource()
        data_sources.append(form_source)
    elif not GOOGLE_FORM_SHEET_ID:
        logger.warning("Google Form Sheet ID not configured, skipping form submissions")

    # Add X/Twitter data source if not skipped
    if not args.skip_tweet_collection:
        try:
            collector = XCollector()
            data_sources.append(collector)
        except Exception as e:
            logger.error(f"Error initializing X collector: {e}")

    # Fetch items from all enabled data sources
    for source in data_sources:
        source_name = source.get_source_name()
        logger.info(f"Fetching items from {source_name}...")

        try:
            # Extract source-specific configuration
            config_params = extract_config_for_source(source_name, args)

            # Fetch items from the source
            items = source.fetch_items(config_params)

            # Store items for reporting
            source_items[source_name] = items

            # Add to the combined list
            all_fetched_items.extend(items)

            logger.info(f"Fetched {len(items)} items from {source_name}")
            logger.info(f"Completed processing {source_name}")
        except Exception as e:
            logger.error(f"Error fetching from {source_name}: {e}")
            logger.info(f"Continuing without {source_name} integration")
    
    logger.info("All data sources processed, checking cleanup status")
    
    # Check if we have any *newly* fetched news items
    if not all_fetched_items:
        logger.warning("No *new* items fetched from sources in this run. Proceeding to publish existing items.")
        total_saved_count = 0
    else:
        # Run deduplication before saving to database
        logger.info("Running deduplication on fetched items...")
        all_fetched_items, duplicates_marked = mark_duplicates(all_fetched_items)
        logger.info(f"Deduplication complete: {duplicates_marked} duplicates marked")
        
        # Save items to database ONLY if new items were fetched
        news_items_to_save = [item for item in all_fetched_items if isinstance(item, NewsItem)]
        tweet_items_to_save = [item for item in all_fetched_items if isinstance(item, TweetItem)]
        
        saved_news_count = 0
        if news_items_to_save:
            saved_news_count = save_news_items(news_items_to_save)
            logger.info(f"Saved {saved_news_count} new news items to database")

        saved_tweet_count = 0
        if tweet_items_to_save:
            saved_tweet_count = save_tweet_items(tweet_items_to_save)
            logger.info(f"Saved {saved_tweet_count} new tweet items to database")
        
        total_saved_count = saved_news_count + saved_tweet_count
        logger.info(f"Total {total_saved_count} new items saved to database")

    # Unified Relevance Evaluation (Conditional)
    evaluated_count = 0 # Initialize count
    if not args.skip_relevance_evaluation:
        logger.info("Querying database for items needing relevance evaluation...")
        
        # Get unevaluated items from database using watermark-based approach
        from core.storage.news_items_pg import get_unevaluated_news_items_since
        from core.storage.tweet_items_pg import get_unevaluated_tweet_items_since
        from core.storage.daily_runs_pg import get_last_successful_run_timestamp
        
        # Get the timestamp of the last successful run
        processing_watermark = get_last_successful_run_timestamp()
        logger.info(f"Processing items fetched since: {processing_watermark.isoformat()}")
        
        # Get items fetched since the watermark
        unevaluated_news = get_unevaluated_news_items_since(processing_watermark)
        unevaluated_tweets = get_unevaluated_tweet_items_since(processing_watermark)
        items_to_evaluate = unevaluated_news + unevaluated_tweets
        
        logger.info(f"Found {len(items_to_evaluate)} items to evaluate since {processing_watermark.isoformat()} ({len(unevaluated_news)} news, {len(unevaluated_tweets)} tweets)")
        
        for item in items_to_evaluate:
            if isinstance(item, LLMPreparable) and item.relevance is None:
                try:
                    item_title_for_log = "Unknown item"
                    if hasattr(item, 'get_llm_input'):
                        try:
                            item_title_for_log, _ = item.get_llm_input()
                        except Exception: # pylint: disable=broad-except
                            pass # Keep default "Unknown item" if get_llm_input fails

                    logger.debug(f"Evaluating item: {item_title_for_log}")
                    
                    # evaluate_item_relevance updates item.relevance directly in memory
                    # and returns the status, which might be useful for immediate logging if needed.
                    new_relevance_status = evaluate_item_relevance(item) 
                    
                    # Persist the change to the database
                    if item.relevance is not None: # Ensure relevance was set
                        if isinstance(item, NewsItem):
                            if hasattr(item, 'link') and item.link is not None:
                                update_news_item_relevance_status(str(item.link), item.relevance)
                                # Also update timeliness if it was evaluated
                                if hasattr(item, 'is_time_sensitive'):
                                    from core.storage.news_items_pg import update_item_timeliness
                                    update_item_timeliness(str(item.link), item.is_time_sensitive, item.timeliness_reason)
                            else:
                                logger.error(f"NewsItem missing link, cannot update relevance: {item_title_for_log}")
                        elif isinstance(item, TweetItem):
                            if hasattr(item, 'tweet_id') and item.tweet_id is not None:
                                update_tweet_item_relevance_status(item.tweet_id, item.relevance)
                                # Also update timeliness if it was evaluated
                                if hasattr(item, 'is_time_sensitive'):
                                    from core.storage.tweet_items_pg import update_tweet_timeliness
                                    update_tweet_timeliness(item.tweet_id, item.is_time_sensitive, item.timeliness_reason)
                            else:
                                logger.error(f"TweetItem missing tweet_id, cannot update relevance for item ID (if available): {getattr(item, 'tweet_id', 'N/A')}")
                        else:
                            logger.warning(f"Item of type {type(item)} is LLMPreparable but not NewsItem or TweetItem. Relevance not persisted to DB via main loop for: {item_title_for_log}")
                        evaluated_count += 1
                        logger.debug(f"Relevance for '{item_title_for_log}' set to {item.relevance.value if item.relevance else 'None'} and persisted.")
                    else:
                        logger.warning(f"Relevance for '{item_title_for_log}' was not set by evaluate_item_relevance, skipping DB update.")
                    
                except Exception as e: # pylint: disable=broad-except
                    item_identifier = "Unknown item"
                    if hasattr(item, 'get_llm_input'):
                        try:
                            item_identifier, _ = item.get_llm_input()
                        except: # pylint: disable=bare-except
                            pass
                    elif hasattr(item, 'link'): # Fallback for NewsItem
                        item_identifier = str(getattr(item, 'link', 'N/A'))
                    elif hasattr(item, 'tweet_id'): # Fallback for TweetItem
                        item_identifier = str(getattr(item, 'tweet_id', 'N/A'))
                    logger.error(f"Error evaluating or updating relevance for item ({item_identifier}): {e}", exc_info=True)
        logger.info(f"Unified relevance evaluation complete. Processed {evaluated_count} items for relevance.")
    else:
        logger.info("Skipping relevance evaluation as requested by --skip-relevance-evaluation flag.")
        # evaluated_count remains 0, which will be reflected in the final summary

    # Check for time-sensitive items using centralized service
    time_sensitive_count = 0
    time_sensitive_data = None
    
    if args.enable_email_alerts or args.email_dry_run:
        logger.info("Checking for time-sensitive content using centralized service...")
        
        # Get time-sensitive items from the single source of truth
        time_sensitive_data = get_time_sensitive_items_for_period(
            hours=args.daily_collection_hours,
            limit_for_email=True
        )
        
        time_sensitive_count = time_sensitive_data['total_count']
        
        if time_sensitive_count > 0:
            logger.info(f"Found {time_sensitive_count} time-sensitive items")
            if args.email_dry_run:
                logger.info("Email dry-run mode - notifications not sent")
                # Show first 5 items for dry run
                for item, reason in time_sensitive_data['all_items'][:5]:
                    item_title = getattr(item, 'title', getattr(item, 'text', 'Unknown')[:50])
                    logger.info(f"  - {item_title}: {reason}")
                if time_sensitive_count > 5:
                    logger.info(f"  ... and {time_sensitive_count - 5} more")
        else:
            logger.info("No time-sensitive items found")

    # Publish to Notion if configured and not skipped
    notion_page_id = None
    if NOTION_API_TOKEN and NOTION_PAGE_ID and not args.skip_notion:
        logger.info("Publishing news items to Notion")

        # Handle daily publishing - check for week boundary and handle accordingly
        try:
            current_weekly_page = get_current_weekly_page_id()
            
            if is_new_week() or not current_weekly_page:
                # Create a new weekly page if it's a new week OR if we don't have a page for this week
                if is_new_week():
                    logger.info("New week detected, creating new Notion page")
                else:
                    logger.info("No weekly page exists for current week, creating one")
                
                notion_client = get_notion_client()
                new_page_id = create_weekly_notion_page(notion_client, NOTION_PAGE_ID)
                if new_page_id:
                    update_weekly_page_id(new_page_id)
                    target_page_id = new_page_id
                    logger.info(f"Created new weekly page: {new_page_id}")
                else:
                    logger.error("Failed to create new weekly page, using parent page")
                    target_page_id = NOTION_PAGE_ID
            else:
                # Continue with current week's page
                target_page_id = current_weekly_page
                logger.info(f"Using existing weekly page: {target_page_id}")
        except Exception as e:
            logger.error(f"Error in week boundary detection: {e}")
            target_page_id = NOTION_PAGE_ID
        
        # Get items from last N hours for daily update
        from datetime import timezone
        cutoff = datetime.now(timezone.utc) - timedelta(hours=args.daily_collection_hours)
        logger.info(f"Fetching items from last {args.daily_collection_hours} hours for daily update")
        
        # Get ALL items from the last N days (regardless of relevance)
        from core.storage.news_items_pg import get_recent_news_items
        from core.storage.tweet_items_pg import get_recent_tweets
        
        # Get items from the last 7 days - we'll filter by publication date below
        all_news = get_recent_news_items(days=7, relevance=None)  # Get ALL news items
        all_tweets = get_recent_tweets(days=7)  # Get ALL tweets
        
        # Filter by actual publication date (not fetched_at) - but keep ALL items regardless of relevance
        items_for_notion = []
        
        # Filter news items by published_at
        for item in all_news:
            if hasattr(item, 'published_at') and item.published_at:
                # Ensure published_at is timezone-aware for comparison
                pub_time = item.published_at
                if pub_time.tzinfo is None:
                    pub_time = pub_time.replace(tzinfo=timezone.utc)
                if pub_time > cutoff:
                    items_for_notion.append(item)
        
        # Filter tweet items by created_at (which is their publication time)
        for tweet in all_tweets:
            if hasattr(tweet, 'created_at') and tweet.created_at:
                # Ensure created_at is timezone-aware for comparison
                created_time = tweet.created_at
                if created_time.tzinfo is None:
                    created_time = created_time.replace(tzinfo=timezone.utc)
                if created_time > cutoff:
                    items_for_notion.append(tweet)
        
        logger.info(f"Found {len(items_for_notion)} items published in last {args.daily_collection_hours} hours (all relevance levels)")
        
        if items_for_notion:
            logger.info(f"Found {len(items_for_notion)} items to append to daily page")
            # Use append mode for daily updates
            result = publish_to_notion(items_for_notion, since_timestamp=cutoff, 
                               append_mode=True, target_page_id=target_page_id,
                               time_sensitive_items=time_sensitive_data['all_items'] if time_sensitive_data else None)
            if result[0]:  # Check if page_id is not None
                logger.info(f"Successfully appended {len(items_for_notion)} items to daily page")
                notion_page_id = result[0]
                time_sensitive_block_id = result[1]
                # Update publication timestamp
                update_publication_timestamp()
                logger.info(f"Updated publication timestamp to {now_utc().isoformat()}")
            else:
                logger.error("Failed to append items to daily page")
        else:
            logger.info("No new items to append to daily page")
                
    else:
        if args.skip_notion:
            logger.info("Skipping Notion publishing as requested")
        else:
            logger.info("Notion publishing not configured, skipping")
    
    # Send email notifications AFTER Notion publishing (so we have the page ID)
    if (args.enable_email_alerts or args.email_dry_run) and time_sensitive_count > 0:
        # Determine which page ID to use for the email
        email_page_id = None
        email_block_id = None
        logger.info(f"DEBUG: target_page_id exists: {'target_page_id' in locals()}")
        if 'target_page_id' in locals():
            logger.info(f"DEBUG: target_page_id value: {target_page_id}")
            email_page_id = target_page_id
        elif 'notion_page_id' in locals() and notion_page_id:
            logger.info(f"DEBUG: Using notion_page_id: {notion_page_id}")
            email_page_id = notion_page_id
        
        # Get the time-sensitive block ID if available
        logger.info(f"DEBUG: time_sensitive_block_id exists: {'time_sensitive_block_id' in locals()}")
        if 'time_sensitive_block_id' in locals():
            logger.info(f"DEBUG: time_sensitive_block_id value: {time_sensitive_block_id}")
            email_block_id = time_sensitive_block_id
        
        if args.enable_email_alerts and not args.email_dry_run:
            logger.info(f"Sending email notification for {time_sensitive_count} time-sensitive items")
            logger.info(f"Email page ID: {email_page_id}")
            logger.info(f"Email block ID: {email_block_id}")
            
            # Sanity check - ensure we're not passing block ID as page ID
            if email_page_id and email_block_id and email_page_id.startswith(email_block_id[:8]):
                logger.error(f"WARNING: email_page_id ({email_page_id}) looks like it might be a block ID!")
                logger.error(f"This would cause incorrect Notion URLs in emails")
            
            # Use the new simplified email function
            send_time_sensitive_email(
                email_items=time_sensitive_data['email_items'],
                total_count=time_sensitive_data['total_count'],
                excluded_count=time_sensitive_data['excluded_count'],
                weekly_subpage_id=email_page_id,
                time_sensitive_block_id=email_block_id
            )
        else:
            logger.info("Email dry-run mode - notifications not sent")

    # Print results
    print("\n=== TAC Daily News Aggregation Results ===")
    print(f"Fetched {len(all_fetched_items)} unique news items:")

    # Print items from each source
    for source_name, items in source_items.items():
        print(f"  - {len(items)} from {source_name}")

    print(f"Saved {total_saved_count} new items to database")
    if NOTION_API_TOKEN and NOTION_PAGE_ID and not args.skip_notion:
        # MODIFIED: Use items_for_notion variable if it exists
        if 'items_for_notion' in locals() and items_for_notion:
            print(f"Published {len(items_for_notion)} news items to Notion (organized by relevance)")
            # Publication time range no longer shown (was for weekly mode only)
        else:
            print("No items published to Notion")
    print("\nRelevance Check Summary:")
    print(f"  - {evaluated_count} items processed for relevance.")
    if args.enable_email_alerts or args.email_dry_run:
        print(f"  - {time_sensitive_count} time-sensitive items detected.")
        if time_sensitive_count > 0 and args.email_dry_run:
            print("  - Email notifications not sent (dry-run mode)")

    print("\n=== Process Complete ===")
    
    # Complete daily run tracking
    if run_id:
        try:
            items_count = len(items_for_notion) if 'items_for_notion' in locals() and items_for_notion else 0
            complete_run(run_id, items_fetched=len(all_fetched_items), 
                        items_published=items_count, notion_page_id=notion_page_id)
            logger.info(f"Completed daily run {run_id}")
        except Exception as e:
            logger.error(f"Error completing daily run {run_id}: {e}")
    
    # Add diagnostic logging for shutdown
    logger.info("Main function completed, beginning shutdown sequence")
    logger.info(f"Active threads: {threading.active_count()}")
    for thread in threading.enumerate():
        logger.info(f"Thread: {thread.name} (daemon: {thread.daemon}, alive: {thread.is_alive()})")
    
    # Check for any specific Telegram-related threads
    telegram_threads = [t for t in threading.enumerate() if 'telegram' in t.name.lower() or 'telethon' in t.name.lower()]
    if telegram_threads:
        logger.info(f"Found {len(telegram_threads)} Telegram-related threads still active")
        for t in telegram_threads:
            logger.info(f"  Telegram thread: {t.name}")
    
    # Log garbage collection info
    import gc
    logger.info(f"Garbage collector stats: {gc.get_count()}")
    logger.info(f"Garbage objects: {len(gc.garbage)}")
    
    logger.info("Shutdown logging complete")

if __name__ == "__main__":
    main()