#!/usr/bin/env python3
"""
Test script for the X Collector's news fetching functionality.
This script fetches tweets from a specified X List and converts them to news items.
"""

from datetime import datetime

from sources.x.collector import XCollector
from core.config import X_LIST_ID, X_TWEET_LIMIT
from core.utils.logging import get_logger

# This test script needs file logging
logger = get_logger(__name__, add_file_handler=True)

if __name__ == "__main__":
    logger.info("Running XCollector direct test for fetch_x_list_news...")
    collector = XCollector()
    
    # Call the equivalent method on XCollector.
    # XCollector.fetch_x_list_news() already exists and uses a 7-day lookback.
    news_items = collector.fetch_x_list_news(list_id=X_LIST_ID, max_results=X_TWEET_LIMIT)

    if news_items:
        print(f"\n--- Fetched {len(news_items)} News Items directly via XCollector ---")
        for item in news_items[:5]:  # Print first 5
            print(f"Title: {item.title}")
            print(f"Publisher: {item.publisher}")
            print(f"Published: {item.published_at}")
            print(f"Link: {item.link}")
            print(f"Summary: {item.summary[:100]}...")
            print("-" * 50)
    else:
        print("No new items fetched directly via XCollector.")
    
    logger.info("XCollector direct test run finished.")
