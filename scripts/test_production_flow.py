#!/usr/bin/env python3
"""
Test the production flow without making actual changes.
This validates that the system will work correctly when the GitHub Action runs.
"""
import os
import sys
from datetime import datetime, timezone

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.utils.logging import get_logger
from core.storage.publication_state_pg import (
    get_current_weekly_page_id, 
    is_new_week,
    init_publication_state
)
from core.config import NOTION_PAGE_ID
from sources.notion.subpage_creator import get_notion_client

logger = get_logger(__name__)

def test_production_flow():
    """Test what will happen in production without making changes."""
    logger.info("=== Testing Production Flow ===")
    
    # 1. Check database state
    logger.info("\n1. Checking database state...")
    init_publication_state()
    
    current_page_id = get_current_weekly_page_id()
    logger.info(f"   Current weekly page ID in DB: {current_page_id}")
    
    is_new = is_new_week()
    logger.info(f"   Is new week: {is_new}")
    
    # 2. Check Notion configuration
    logger.info("\n2. Checking Notion configuration...")
    notion_token = os.getenv("NOTION_API_TOKEN")
    notion_page_id = os.getenv("NOTION_PAGE_ID") or NOTION_PAGE_ID
    
    if not notion_token:
        logger.error("   ❌ NOTION_API_TOKEN not set")
        return False
    else:
        logger.info("   ✅ NOTION_API_TOKEN is configured")
    
    if not notion_page_id:
        logger.error("   ❌ NOTION_PAGE_ID not set")
        return False
    else:
        logger.info(f"   ✅ NOTION_PAGE_ID: {notion_page_id}")
    
    # 3. Test Notion connection
    logger.info("\n3. Testing Notion connection...")
    try:
        notion_client = get_notion_client()
        page = notion_client.pages.retrieve(notion_page_id)
        logger.info(f"   ✅ Successfully connected to parent page")
        logger.info(f"   Page URL: {page.get('url', 'N/A')}")
    except Exception as e:
        logger.error(f"   ❌ Failed to connect to Notion: {e}")
        return False
    
    # 4. Simulate what will happen
    logger.info("\n4. Simulating production behavior...")
    
    if is_new:
        logger.info("   → System will create a new weekly page")
        logger.info(f"   → New page will be created under: {notion_page_id}")
        logger.info("   → Weekly page ID will be stored in database")
    else:
        if current_page_id:
            logger.info(f"   → System will use existing weekly page: {current_page_id}")
            # Validate the weekly page exists
            try:
                weekly_page = notion_client.pages.retrieve(current_page_id)
                logger.info(f"   ✅ Weekly page exists and is accessible")
                logger.info(f"   Page URL: {weekly_page.get('url', 'N/A')}")
            except Exception as e:
                logger.error(f"   ❌ Weekly page not accessible: {e}")
                logger.info("   → System will create a new weekly page")
        else:
            logger.info(f"   → No weekly page ID stored")
            logger.info("   → System will create or find the weekly page for the current week")
    
    logger.info("\n5. Summary:")
    logger.info("   ✅ Database connection working")
    logger.info("   ✅ Notion connection working")
    logger.info("   ✅ Configuration appears correct")
    logger.info("\n   When the GitHub Action runs:")
    logger.info("   1. It will collect news from all sources")
    logger.info("   2. Evaluate relevance with Claude AI")
    logger.info("   3. Publish to the appropriate Notion page")
    logger.info("   4. Send email alerts for time-sensitive items")
    
    return True

def main():
    """Run the production flow test."""
    success = test_production_flow()
    
    if success:
        logger.info("\n✅ Production flow test PASSED")
        logger.info("The system should work correctly when the GitHub Action runs.")
    else:
        logger.error("\n❌ Production flow test FAILED")
        logger.error("Please fix the issues before running in production.")
        sys.exit(1)

if __name__ == "__main__":
    main()