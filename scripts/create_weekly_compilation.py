#!/usr/bin/env python3
"""
Weekly compilation script for TAC Weekly News Aggregation System.

Advanced approach: Parse weekly page structure, extract items with metadata,
deduplicate categories, and reorganize chronologically.
"""

import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, cast
from notion_client import Client
import re

from sources.notion.notion_publisher import get_notion_client
from sources.notion.notion_utils import format_notion_uuid, format_datetime_pst
from sources.notion import block_generator as bg
from core.week_boundary_utils import get_week_date_range, get_current_week_start
from core.storage.publication_state_pg import get_current_weekly_page_id

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def fetch_blocks_paginated(client: Client, block_id: str) -> List[Dict]:
    """Fetch all blocks from a Notion page or toggle with pagination."""
    all_blocks = []
    start_cursor = None
    
    while True:
        response = cast(Dict, client.blocks.children.list(
            block_id=block_id,
            start_cursor=start_cursor
        ))
        
        all_blocks.extend(response.get('results', []))
        
        if not response.get('has_more', False):
            break
        start_cursor = response.get('next_cursor')
    
    return all_blocks


def get_block_text(block: Dict) -> str:
    """Extract text content from a block."""
    block_type = block.get('type')
    if not block_type:
        return ""
    
    rich_text = block.get(block_type, {}).get('rich_text', [])
    return ''.join(
        text_obj.get('text', {}).get('content', '')
        for text_obj in rich_text
        if text_obj.get('type') == 'text'
    )


def parse_date_from_header(header_text: str) -> Optional[datetime]:
    """Parse date from daily header like '📅 Thursday, January 02, 2025'."""
    date_match = re.search(r'(?:📅\s*)?\w+,\s*(\w+\s+\d+,\s+\d{4})', header_text)
    if date_match:
        try:
            return datetime.strptime(date_match.group(1), '%B %d, %Y')
        except ValueError:
            pass
    return None


# Category mapping for cleaner detection
CATEGORY_PATTERNS = {
    'time_sensitive': lambda text: '🚨 Time-Sensitive Items' in text,
    'relevant': lambda text: '✅' in text and 'Relevant' in text,
    'needs_review': lambda text: '🔍' in text and 'Review' in text,
    'not_relevant': lambda text: '❌' in text and 'Not Relevant' in text,
}

CATEGORY_TITLES = {
    'time_sensitive': '🚨 Time-Sensitive Items',
    'relevant': '✅ Relevant',
    'needs_review': '🔍 Needs Review',
    'not_relevant': '❌ Not Relevant'
}


def parse_weekly_structure(blocks: List[Dict]) -> Tuple[Dict[str, List[Dict]], List[Tuple[datetime, Dict]]]:
    """Parse the weekly page structure and extract items with metadata."""
    items_by_category = {cat: [] for cat in CATEGORY_PATTERNS}
    all_items_with_dates = []
    
    current_date = None
    current_category = None
    
    for block in blocks:
        block_type = block.get('type')
        
        if block_type == 'heading_2':
            # Check for daily header
            header_text = get_block_text(block)
            parsed_date = parse_date_from_header(header_text)
            if parsed_date:
                current_date = parsed_date
                logger.debug(f"Found daily section: {header_text}")
        
        elif block_type == 'heading_3':
            # Check for category header
            header_text = get_block_text(block)
            current_category = next(
                (cat for cat, pattern in CATEGORY_PATTERNS.items() if pattern(header_text)),
                None
            )
        
        elif block_type == 'toggle' and current_category and current_date:
            # Check for duplicates
            toggle_text = get_block_text(block)
            if not any(get_block_text(existing) == toggle_text 
                      for existing in items_by_category[current_category]):
                items_by_category[current_category].append(block)
                all_items_with_dates.append((current_date, block))
    
    return items_by_category, all_items_with_dates


def find_page_by_title(client: Client, parent_id: str, title: str) -> Optional[str]:
    """Find a child page by title under a parent page."""
    start_cursor = None
    
    while True:
        results = cast(Dict, client.blocks.children.list(
            block_id=parent_id,
            start_cursor=start_cursor
        ))
        
        for block in results.get("results", []):
            if (block.get("type") == "child_page" and 
                block.get("child_page", {}).get("title", "") == title):
                return block.get("id")
        
        if not results.get("has_more", False):
            break
        start_cursor = results.get("next_cursor")
    
    return None


def create_compilation_page(client: Client, 
                          source_page_id: str,
                          parent_page_id: str,
                          week_start: Optional[datetime] = None) -> bool:
    """Create compilation by parsing, deduplicating, and reorganizing content."""
    try:
        # Format page IDs
        source_page_id = format_notion_uuid(source_page_id)
        parent_page_id = format_notion_uuid(parent_page_id)
        
        # Get week range for title
        week_start = week_start or get_current_week_start()
        week_range_str = get_week_date_range(week_start)
        
        logger.info(f"Creating compilation for week: {week_range_str}")
        
        # Create a new subpage
        page_title = f"TAC Weekly Compilation - {week_range_str}"
        new_page = cast(Dict, client.pages.create(
            parent={"page_id": parent_page_id},
            properties={
                "title": [{
                    "type": "text",
                    "text": {"content": page_title}
                }]
            }
        ))
        
        compilation_page_id = new_page["id"]
        logger.info(f"Created new compilation subpage: {compilation_page_id}")
        
        # Fetch and parse source content
        source_blocks = fetch_blocks_paginated(client, source_page_id)
        items_by_category, all_items_with_dates = parse_weekly_structure(source_blocks)
        
        # Log statistics
        for category, items in items_by_category.items():
            logger.info(f"Found {len(items)} items in {category} category")
        
        # Build compilation blocks
        compilation_blocks = [
            bg.create_heading_block(f"TAC Weekly Compilation - {week_range_str}", level=1),
            bg.create_paragraph_block(f"Compiled on {format_datetime_pst()}", italic=True, color="gray"),
            bg.create_divider_block()
        ]
        
        # Add categories in order
        for category_key, category_title in CATEGORY_TITLES.items():
            items = items_by_category.get(category_key, [])
            if not items:
                continue
                
            compilation_blocks.append(bg.create_heading_block(f"{category_title} ({len(items)})", level=3))
            
            # Sort items chronologically
            category_items_with_dates = [
                (date, item) for date, item in all_items_with_dates 
                if item in items
            ]
            sorted_items = [item for date, item in sorted(category_items_with_dates, key=lambda x: x[0])]
            
            # Fetch children for toggles
            for item_block in sorted_items:
                if item_block.get('type') == 'toggle' and item_block.get('id'):
                    children = fetch_blocks_paginated(client, item_block['id'])
                    if 'toggle' in item_block:
                        item_block['toggle']['children'] = children
            
            compilation_blocks.extend(sorted_items)
            compilation_blocks.append(bg.create_paragraph_block(""))  # Spacing
        
        # Upload blocks in chunks
        logger.info(f"Adding {len(compilation_blocks)} blocks to compilation subpage")
        for i in range(0, len(compilation_blocks), 100):
            client.blocks.children.append(
                block_id=compilation_page_id,
                children=compilation_blocks[i:i + 100]
            )
        
        logger.info("Weekly compilation created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error creating weekly compilation: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Create weekly news compilation in Notion')
    parser.add_argument('parent_page_id', help='Parent page ID where compilation subpage will be created')
    parser.add_argument('--source-page-id', help='Override source weekly page ID')
    parser.add_argument('--week-offset', type=int, default=0,
                       help='Weeks to go back from current week (0=last week, 1=two weeks ago, etc.)')
    
    args = parser.parse_args()
    
    # Get client
    client = get_notion_client()
    if not client:
        logger.error("Failed to create Notion client")
        return 1
    
    # Get source page ID
    source_page_id = args.source_page_id
    if not source_page_id:
        # Calculate target week
        current_week = get_current_week_start()
        target_week_start = current_week - timedelta(weeks=(1 + args.week_offset))
        
        # Get parent page ID from config
        from core.config import NOTION_PAGE_ID
        from sources.notion.notion_utils import format_notion_uuid
        
        if not NOTION_PAGE_ID:
            logger.error("NOTION_PAGE_ID not set in environment variables")
            return 1
            
        parent_id = format_notion_uuid(NOTION_PAGE_ID)
        
        # Search for the page
        target_date_range = get_week_date_range(target_week_start)
        target_title = f"TAC Weekly Newsletter - {target_date_range}"
        
        logger.info(f"Searching for weekly page with title: {target_title}")
        source_page_id = find_page_by_title(client, parent_id, target_title)
        
        if not source_page_id:
            logger.error(f"Could not find weekly page with title '{target_title}'")
            return 1
            
        logger.info(f"Found weekly page for {target_date_range}. Page ID: {source_page_id}")
    
    # Calculate week start for compilation title
    current_week = get_current_week_start()
    week_start = current_week - timedelta(weeks=(1 if args.week_offset == 0 else args.week_offset))
    logger.info(f"Using week starting {week_start.strftime('%Y-%m-%d')} for compilation")
    
    # Create compilation
    success = create_compilation_page(client, source_page_id, args.parent_page_id, week_start)
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())