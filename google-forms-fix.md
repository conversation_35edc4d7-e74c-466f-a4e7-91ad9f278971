# Google Forms Authentication Fix

## Problem
The Google Forms data collection was failing in GitHub Actions with the error:
```
ERROR - Google credentials file not found: credentials.json
```

## Root Cause
The code was only looking for a credentials file (`credentials.json`), but GitHub Actions provides credentials as a JSON string in the `GOOGLE_SERVICE_ACCOUNT_JSON` environment variable.

## Solution
Modified `sources/google_form/google_forms.py` to handle both scenarios:

1. **GitHub Actions**: First checks for `GOOGLE_SERVICE_ACCOUNT_JSON` environment variable
2. **Local Development**: Falls back to `credentials.json` file if env var not found

## Code Changes
Updated the `get_sheets_service()` function to:
```python
# First, check if credentials are provided as JSON string in environment variable
service_account_json = os.getenv("GOOGLE_SERVICE_ACCOUNT_JSON")

if service_account_json:
    # Parse the JSON string from environment variable
    import json
    service_account_info = json.loads(service_account_json)
    credentials = service_account.Credentials.from_service_account_info(
        service_account_info,
        scopes=['https://www.googleapis.com/auth/spreadsheets.readonly']
    )
elif os.path.exists(GOOGLE_CREDENTIALS_FILE):
    # Fall back to file-based credentials
    credentials = service_account.Credentials.from_service_account_file(
        GOOGLE_CREDENTIALS_FILE,
        scopes=['https://www.googleapis.com/auth/spreadsheets.readonly']
    )
```

## Setup Instructions
- **Local Development**: Save credentials as `credentials.json` in project root
- **GitHub Actions**: Add the entire JSON content as `GOOGLE_SERVICE_ACCOUNT_JSON` secret

This fix ensures Google Forms data collection works in both environments without code changes.