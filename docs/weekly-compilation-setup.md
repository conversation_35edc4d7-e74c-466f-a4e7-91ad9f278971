# Weekly Compilation Setup

This document explains how to set up the automated weekly compilation feature.

## Overview

The weekly compilation creates a reorganized view of the entire week's content, grouped by category (time-sensitive, relevant, needs review, not relevant) with items sorted chronologically within each category.

## GitHub Secrets Required

Add this secret to your GitHub repository (Settings → Secrets and variables → Actions):

### New Secret Needed:
1. `NOTION_COMPILATION_PARENT_ID` - The parent page under which compilations are created
   - This stays constant - it's where all compilation subpages will be created
   - Example: `22cf98cc0d5080d39c80e88a0668da18`

### Existing Secrets Used:
- `NOTION_API_TOKEN` - Already set up for daily collection
- `ANTHROPIC_API_KEY` - Already set up
- `SUPABASE_DB_URL` - Already set up

## How It Works

The script automatically:
1. **Gets the current weekly page ID from the database** - No manual updates needed!
2. Fetches all content from the current weekly page
3. Parses the structure to extract individual items
4. Reorganizes by category (removing duplicate headers)
5. Sorts items chronologically within each category
6. Creates a new subpage under the compilation parent
7. Copies all toggle content (including nested blocks)

## Schedule

The compilation runs automatically:
- **When**: Every Thursday at 11:00 UTC (2 hours after daily collection)
- **Why Thursday**: Aligns with your weekly cycle (Thursday to Thursday)
- **Manual trigger**: Can also be run manually from GitHub Actions tab

## Manual Usage

```bash
# For current week (automatically gets page ID from database)
python scripts/create_weekly_compilation.py PARENT_PAGE_ID

# For historical weeks (requires manual page ID)
python scripts/create_weekly_compilation.py PARENT_PAGE_ID --source-page-id SOURCE_PAGE_ID --week-offset 1

# Examples:
# Current week
python scripts/create_weekly_compilation.py 22cf98cc0d5080d39c80e88a0668da18

# Last week with manual page ID
python scripts/create_weekly_compilation.py 22cf98cc0d5080d39c80e88a0668da18 \
  --source-page-id 225f98cc0d5081a989f2ee2d84abdef1 \
  --week-offset 1
```

## Verification

After each compilation:
```bash
python scripts/verify_compilation.py SOURCE_PAGE_ID COMPILATION_PAGE_ID
```

This ensures all items were successfully copied.

## No Maintenance Required! 🎉

Since the script automatically gets the current weekly page ID from the database, there's no weekly maintenance needed. The system will always compile the correct week's content.