# Supabase Setup Guide

## 1. Create Your Supabase Project

1. Go to https://supabase.com and click "Start your project"
2. Sign in with GitHub (easiest) or create an account
3. Click "New project"
4. Fill in:
   - Project name: `tac-news-aggregator`
   - Database Password: [Generate a strong one and SAVE IT]
   - Region: Choose closest to you
   - Pricing Plan: Free tier is fine
5. Click "Create new project" (takes ~2 minutes to provision)

## 2. Get Your Connection Details

Once your project is ready:

1. Go to Settings (gear icon) → Database
2. Find "Connection string" section
3. Copy the "URI" connection string - it looks like:
   ```
   postgresql://postgres.[your-project-ref]:[your-password]@aws-0-[region].pooler.supabase.com:6543/postgres
   ```

## 3. Save Connection String

Create a `.env` file in your project root:
```bash
# Add this line to your .env
SUPABASE_DB_URL="postgresql://postgres.[your-project-ref]:[your-password]@aws-0-[region].pooler.supabase.com:6543/postgres"
```

## 4. Quick Test

Test your connection:
```bash
# Install psql if you don't have it
brew install postgresql  # macOS

# Test connection
psql "$SUPABASE_DB_URL" -c "SELECT version();"
```

You should see PostgreSQL version info if it worked!