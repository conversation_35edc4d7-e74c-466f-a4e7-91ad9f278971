# Storage Refactoring Plan

This document outlines the step-by-step plan to refactor the monolithic `core/storage.py` file into a more modular structure with separate files for different responsibilities.

**Note: We are working in a branch, so we don't need to maintain backward compatibility during the refactoring process. We can focus on moving functions to their new locations and then update imports once everything is in place.**

## Phase 1: Setup Directory Structure

- [x] Create `core/storage/` directory
- [x] Create empty `__init__.py` file in the new directory

## Phase 2: Extract Database Initialization

- [x] Create `core/storage/db_init.py`
- [x] Move `init_db()` function to this file
- [x] Test database initialization still works
  - [x] Run `python -c "from core.storage.db_init import init_db; init_db()"`
  - [x] Verify database file is created correctly
- [x] Remove `init_db()` function from original `storage.py` file

## Phase 3: Extract News Item Functions

- [x] Create `core/storage/news_items.py`
- [x] Move the following functions to this file:
  - [x] `save_news_items(items)`
  - [x] `update_item_relevance(link, relevance)`
  - [x] `get_recent_items(limit, source)`
  - [x] `get_recent_news_items(days, relevance)`
- [x] Test news item functions still work
  - [x] Run tests for news item storage and retrieval
  - [x] Verify all functions operate correctly
- [x] Remove the following functions from original `storage.py` file:
  - [x] `save_news_items(items)`
  - [x] `update_item_relevance(link, relevance)`
  - [x] `get_recent_items(limit, source)`
  - [x] `get_recent_news_items(days, relevance)`

## Phase 4: Extract Tweet Item Functions

- [x] Create `core/storage/tweet_items.py`
- [x] Move the following functions to this file:
  - [x] `save_tweet_items(items)`
  - [x] `get_recent_tweets(limit, source, days)`
  - [x] `update_tweet_relevance(tweet_id, relevance)`
  - [x] `update_tweet_newsletter_status(tweet_id, included)`
- [x] Test tweet item functions still work
  - [x] Run tests for tweet storage and retrieval
  - [x] Verify all functions operate correctly
- [x] Remove the following functions from original `storage.py` file:
  - [x] `save_tweet_items(items)`
  - [x] `get_recent_tweets(limit, source, days)`
  - [x] `update_tweet_relevance(tweet_id, relevance)`
  - [x] `update_tweet_newsletter_status(tweet_id, included)`

## Phase 5: Extract X Collection State Management

- [x] Create `core/storage/x_collection.py`
- [x] Move the following functions to this file:
  - [x] `initialize_collection_state(start_time, end_time)`
  - [x] `update_collection_state(collection_id, **kwargs)`
  - [x] `get_collection_state(collection_id, status)`
  - [x] `get_active_collection()`
  - [x] `clear_collection_state(collection_id)`
- [x] Test X collection state management still works
  - [x] Run tests for X collection state management
  - [x] Verify all functions operate correctly
- [x] Remove the following functions from original `storage.py` file:
  - [x] `initialize_collection_state(start_time, end_time)`
  - [x] `update_collection_state(collection_id, **kwargs)`
  - [x] `get_collection_state(collection_id, status)`
  - [x] `get_active_collection()`
  - [x] `clear_collection_state(collection_id)`

## Phase 6: Update `__init__.py` to Re-export Functions

- [x] Update `core/storage/__init__.py` to import and re-export all functions from the new modules
- [x] Test imports from the new package
  - [x] Verify `from core.storage import X` works for all functions

## Phase 7: Remove Original Storage File

- [x] Delete the original `core/storage.py` file after all functions have been moved
- [x] Update any remaining imports in the codebase to use the new module structure

## Phase 8: Run Integration Tests

- [x] Run all existing tests that use storage functions
- [x] Verify no regressions in functionality
- [x] Run the full application to ensure end-to-end functionality

## Phase 9: Clean Up and Documentation

- [x] Add docstrings to all new files explaining their purpose
- [x] Update any documentation that references the storage module
- [x] Remove any redundant code or imports

## Phase 10: Final Verification

- [x] Run a full test suite to ensure everything works correctly
- [x] Verify all ballot boxes in this plan are checked
- [x] Document any issues encountered and their resolutions
