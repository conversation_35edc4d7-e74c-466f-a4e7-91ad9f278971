# Daily Incremental Collection Implementation Plan

## Overview
Transform the TAC Weekly News Aggregation System from a weekly batch process to a daily incremental collection system that maintains weekly rollups in Notion.

## Implementation Plan

[x] Phase 1: Database Schema Extensions
    [x] Step 1.1: Extend publication_state table
        [x] Substep 1.1.1: Add current_weekly_page_id column to track active Notion page
        [x] Substep 1.1.2: Add week_start_date column to track when current week began
        [x] Substep 1.1.3: Create migration script for existing data
    [x] Step 1.2: Add daily run tracking
        [x] Substep 1.2.1: Create daily_runs table to log each execution
        [x] Substep 1.2.2: Add indexes for efficient querying
    [x] Test for Step 1.2: Unit test verifying schema changes and data migration

[x] Phase 2: Core State Management Updates
    [x] Step 2.1: Enhance publication state logic
        [x] Substep 2.1.1: Add get_current_weekly_page_id() function
        [x] Substep 2.1.2: Add is_new_week() function to detect Sunday midnight cutoff
        [x] Substep 2.1.3: Add update_weekly_page_id() function
    [x] Step 2.2: Implement week boundary detection
        [x] Substep 2.2.1: Create week_boundary_utils.py module
        [x] Substep 2.2.2: Add get_current_week_start() function
        [x] Substep 2.2.3: Add get_next_week_cutoff() function
    [x] Test for Step 2.2: Unit tests for week boundary calculations

[x] Phase 3: Notion Integration Modifications
    [x] Step 3.1: Add append mode to Notion publisher
        [x] Substep 3.1.1: Create append_to_existing_page() function
        [x] Substep 3.1.2: Add daily section headers with timestamps
        [x] Substep 3.1.3: Modify publish_to_notion() to support append mode
    [x] Step 3.2: Implement weekly page creation
        [x] Substep 3.2.1: Create create_weekly_notion_page() function
        [x] Substep 3.2.2: Add week date range to page titles
        [x] Substep 3.2.3: Add initial page structure for the week
    [x] Test for Step 3.2: Unit tests for Notion page creation and append functionality

[x] Phase 4: Main Workflow Adaptation
    [x] Step 4.1: Add daily mode to main.py
        [x] Substep 4.1.1: Add --daily-mode command line argument
        [x] Substep 4.1.2: Modify default time windows when in daily mode
        [x] Substep 4.1.3: Add week boundary checking logic
    [x] Step 4.2: Update collection windows
        [x] Substep 4.2.1: Change default tweet collection to 1 day in daily mode
        [x] Substep 4.2.2: Adjust other source time windows accordingly
        [x] Substep 4.2.3: Add overlap protection to prevent duplicate collection
    [x] Test for Step 4.2: Integration test running daily collection

[ ] Phase 5: Scheduler Updates
    [ ] Step 5.1: Create daily scheduler
        [ ] Substep 5.1.1: Copy schedule_weekly.py to schedule_daily.py
        [ ] Substep 5.1.2: Change schedule to run daily at configurable time
        [ ] Substep 5.1.3: Add --daily-mode flag to main.py invocation
    [ ] Step 5.2: Add scheduler configuration
        [ ] Substep 5.2.1: Add DAILY_RUN_TIME to config
        [ ] Substep 5.2.2: Add timezone handling
        [ ] Substep 5.2.3: Add missed run detection
    [ ] Test for Step 5.2: Manual test of daily scheduler execution

[ ] Phase 6: Data Deduplication & Cleanup
    [ ] Step 6.1: Implement item deduplication
        [ ] Substep 6.1.1: Add last_included_in_page column to track items
        [ ] Substep 6.1.2: Modify collection to skip already-published items
        [ ] Substep 6.1.3: Add cleanup for items older than 2 weeks
    [ ] Step 6.2: Add daily run cleanup
        [ ] Substep 6.2.1: Create cleanup_old_runs() function
        [ ] Substep 6.2.2: Add configurable retention period
        [ ] Substep 6.2.3: Schedule cleanup to run after each daily collection
    [ ] Test for Step 6.2: Unit tests for deduplication and cleanup logic

[ ] Phase 7: Configuration & Documentation
    [ ] Step 7.1: Update configuration
        [ ] Substep 7.1.1: Add COLLECTION_MODE to config (daily/weekly)
        [ ] Substep 7.1.2: Add WEEK_CUTOFF_DAY and WEEK_CUTOFF_TIME
        [ ] Substep 7.1.3: Update all time-related defaults
    [ ] Step 7.2: Update documentation
        [ ] Substep 7.2.1: Update README.md with daily mode instructions
        [ ] Substep 7.2.2: Create DAILY_MODE.md with detailed documentation
        [ ] Substep 7.2.3: Update command-line help text
    [ ] Test for Step 7.2: Manual review of documentation accuracy

[ ] Phase 8: Testing & Rollout
    [ ] Step 8.1: Comprehensive testing
        [ ] Substep 8.1.1: Run full week simulation in test environment
        [ ] Substep 8.1.2: Test week boundary transitions
        [ ] Substep 8.1.3: Test recovery from missed runs
    [ ] Step 8.2: Gradual rollout
        [ ] Substep 8.2.1: Run daily and weekly in parallel for one week
        [ ] Substep 8.2.2: Compare outputs for consistency
        [ ] Substep 8.2.3: Switch production to daily mode
    [ ] Test for Step 8.2: End-to-end validation of daily collection with weekly rollups

## Success Criteria
- Daily collections run automatically at configured time
- Items are incrementally added to current week's Notion page
- New Notion page created automatically at Sunday midnight
- No duplicate items in daily updates
- Existing weekly functionality remains available via flag
- All tests pass and documentation is updated

## Rollback Plan
If issues arise:
1. Stop daily scheduler
2. Revert to weekly scheduler
3. Database changes are backward compatible
4. No data loss as weekly mode remains functional