# Thursday-Wednesday Week with Pacific Time - Simple Implementation Plan

## Overview
Change week boundary from Sunday-Saturday to Thursday-Wednesday and add Pacific Time support.

---

[x] Phase 1: Add Pacific Time Support
    [x] Step 1.1: Add pytz to requirements.txt
        [x] Substep 1.1.1: Add line `pytz==2024.1` to requirements.txt
        [x] Substep 1.1.2: Run `pip install pytz` to verify it works
    [x] Test for Step 1.1: Import pytz and create Pacific timezone object

[x] Phase 2: Update Week Calculation Logic
    [x] Step 2.1: Update core/week_boundary_utils.py
        [x] Substep 2.1.1: Add imports: `import pytz` and `PACIFIC_TZ = pytz.timezone('US/Pacific')`
        [x] Substep 2.1.2: In get_current_week_start(), change calculation from Sunday to Thursday
        [x] Substep 2.1.3: Add `.replace(tzinfo=PACIFIC_TZ)` to make datetime timezone-aware
    [x] Test for Step 2.1: Run a simple test to verify Thursday is returned as week start
    
    [x] Step 2.2: Update core/storage/publication_state.py
        [x] Substep 2.2.1: Add same timezone imports
        [x] Substep 2.2.2: Update is_new_week() to use Thursday calculation
        [x] Substep 2.2.3: Update update_weekly_page_id() to use Thursday calculation
    [x] Test for Step 2.2: Test that week detection works for Thursday boundaries

[x] Phase 3: Update Tests
    [x] Step 3.1: Fix test_week_boundary.py
        [x] Substep 3.1.1: Change assertions from weekday 6 to weekday 3
        [x] Substep 3.1.2: Update any Sunday references to Thursday
    [x] Test for Step 3.1: Run the test file to ensure it passes

[x] Phase 4: Final Verification
    [x] Step 4.1: Run Integration Test
        [x] Substep 4.1.1: Run main.py with --skip-notion to verify no errors
        [x] Substep 4.1.2: Check logs to confirm Thursday week boundaries are used
    [x] Test for Step 4.1: Verify the system runs without errors

---

That's it. We're just changing the weekday calculation and adding timezone awareness. No need for complex migrations or rollback procedures.