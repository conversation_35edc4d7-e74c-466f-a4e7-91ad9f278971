## PRD: Unified Content Relevance Pipeline and Model Abstraction

**1. Feature:**
Standardize LLM relevance evaluation across all content types and enhance relevance data representation.

**2. Problem Statement:**
*   `core/ai_relevance_judge.py` contains multiple, somewhat redundant functions for checking relevance (e.g., `evaluate_content_item`, `check_relevance`, `check_tweet_relevance`).
*   The `evaluate_content_item` function uses `hasattr` for duck-typing to extract title/content, which can be less explicit and potentially fragile.
*   The LLM prompt (`relevance_evaluation_user_prompt.txt`) requests "Yes/No/Maybe" categories, but the system currently only stores a boolean `relevance` in the database and models. This loses the nuance of "Maybe," which is valuable for items requiring manual review.
*   Relevance evaluation for tweets happens within the X/Twitter `DataSource`'s `fetch_items` (as per the previous PR<PERSON>'s suggestion), while relevance for other `NewsItem`s happens later in `scripts/main.py`. This could be more unified.

**3. Proposed Solution:**
*   Define a `RelevanceStatus` Enum (e.g., `RELEVANT`, `NOT_RELEVANT`, `NEEDS_REVIEW`) in `core/models.py` or a new `core/enums.py`.
*   Update the `relevance` field in the `news_items` and `tweet_items` database tables and corresponding Pydantic models (`NewsItem`, `TweetItem`) to use this `RelevanceStatus` Enum (stored as TEXT or INTEGER).
*   Define an `LLMPreparable` abstract base class (ABC) or interface in `core/interfaces/llm_preparable_interface.py` (new file). This interface will have a method `get_llm_input() -> Tuple[str, str]` to provide a standardized title and content body for LLM evaluation.
*   Modify `NewsItem` and `TweetItem` models to implement the `LLMPreparable` interface.
*   Consolidate relevance checking logic in `core/ai_relevance_judge.py` into a single primary function, e.g., `evaluate_item_relevance(item: LLMPreparable) -> RelevanceStatus`. This function will call the underlying LLM and parse its "Yes/No/Maybe" response into the `RelevanceStatus` enum.
*   Refactor `scripts/main.py` to have a single, unified stage where all fetched items (that implement `LLMPreparable` and don't already have a relevance status) are passed through `evaluate_item_relevance`.

**4. Goals:**
*   Simplify the AI relevance checking module (`core/ai_relevance_judge.py`) by having one clear entry point.
*   Improve type safety and explicitness for how items provide data to the LLM via the `LLMPreparable` interface.
*   Capture and utilize the full "Yes/No/Maybe" (or `RELEVANT`/`NOT_RELEVANT`/`NEEDS_REVIEW`) feedback from the LLM.
*   Create a more cohesive and unified pipeline for content evaluation in `scripts/main.py`.
*   Make the system more maintainable by localizing changes related to LLM input preparation or relevance criteria.

**5. Non-Goals:**
*   Changing the LLM prompts themselves (unless minor adjustments are needed for parsing the "Maybe" category).
*   Changing the core LLM interaction logic (e.g., the Anthropic client usage).
*   Implementing a full manual review interface in this PRD (though this change enables it better).

**6. Decomposed Steps & Testing:**

*   **Phase 1: Define New Structures (Enum, Interface, DB/Model Updates)**
    *   `[x]` **Step 1: Define `RelevanceStatus` Enum.**
        *   Create `core/enums.py` (or add to `core/models.py` if preferred for simplicity initially).
        *   Define `RelevanceStatus(Enum)`:
            ```python
            from enum import Enum

            class RelevanceStatus(Enum):
                RELEVANT = "relevant"
                NOT_RELEVANT = "not_relevant"
                NEEDS_REVIEW = "needs_review"
                # Optional: PENDING_EVALUATION = "pending" (for items not yet processed)
            ```
        *   **Test:** Ensure the enum can be imported and its members accessed.
    *   `[x]` **Step 2: Update Database Schema (`core/storage/db_init.py`).**
        *   Modify `news_items` table: change `relevance BOOLEAN` to `relevance TEXT`.
        *   Modify `tweet_items` table: change `relevance BOOLEAN` to `relevance TEXT`.
        *   **Note:** A migration strategy for existing boolean data might be needed if preserving old relevance states is critical. For simplicity in this PRD, we assume new items will use TEXT and old `0`/`1` values might need manual interpretation or a one-off conversion script (outside this PRD's direct implementation steps, but to be noted). Alternatively, if the database supports it, an `ALTER TABLE` script could try to map `0` to `'not_relevant'` and `1` to `'relevant'`.
        *   **Test:** ✅
            *   Delete the existing `news_feed.db` file (if in a dev environment).
            *   Run `init_db()` (e.g., by just running `scripts/main.py` once or a small script that calls `init_db()`).
            *   Inspect the schema of `news_items` and `tweet_items` tables using a SQLite browser to confirm the `relevance` column is `TEXT`.
    *   `[x]` **Step 3: Update Pydantic Models (`core/models.py`).**
        *   Import `RelevanceStatus` from where it was defined.
        *   In `NewsItem`: change `relevance: Optional[bool] = None` to `relevance: Optional[RelevanceStatus] = None`.
        *   In `TweetItem`: change `relevance: Optional[bool] = None` to `relevance: Optional[RelevanceStatus] = None`.
        *   **Test:** ✅
            *   Attempt to instantiate `NewsItem` and `TweetItem` with the new `relevance` type (e.g., `relevance=RelevanceStatus.RELEVANT`).
            *   Ensure Pydantic validation works as expected.
    *   `[x]` **Step 4: Update Storage Functions (`core/storage/*.py`).**
        *   In `core/storage/news_items.py`:
            *   `save_news_items`: When saving `item.relevance`, store `item.relevance.value` (the string value of the enum) if `item.relevance` is not None.
            *   `update_item_relevance`: The `relevance` parameter should now be `RelevanceStatus`. Store `relevance.value`.
            *   `get_recent_news_items`, `get_news_items_since`: When fetching, convert the string from DB back to `RelevanceStatus(row['relevance'])` if `row['relevance']` is not None. The `relevance` parameter to these functions should also accept `RelevanceStatus`.
        *   In `core/storage/tweet_items.py`:
            *   Apply similar changes to `save_tweet_items`, `update_tweet_relevance`, `get_recent_tweets` for handling `RelevanceStatus` enum for storage and retrieval.
        *   **Test:** ✅
            *   Write small test scripts to save and retrieve `NewsItem` and `TweetItem` objects with different `RelevanceStatus` values. Verify the data is stored and retrieved correctly as enum members.
            *   Test existing functions like `get_news_items_since(..., relevance=RelevanceStatus.RELEVANT)`.
    *   `[x]` **Step 5: Define `LLMPreparable` Interface.**
        *   Create `core/interfaces/llm_preparable_interface.py`.
        *   Define an ABC `LLMPreparable`:
            ```python
            from abc import ABC, abstractmethod
            from typing import Tuple

            class LLMPreparable(ABC):
                @abstractmethod
                def get_llm_input(self) -> Tuple[str, str]:
                    """Returns (title, content_body) for LLM evaluation."""
                    pass
            ```
        *   **Test:** ✅ Ensure the interface can be imported.
    *   `[x]` **Step 6: Implement `LLMPreparable` in `NewsItem` and `TweetItem`.**
        *   In `core/models.py`:
            *   Modify `NewsItem` to inherit from `LLMPreparable`. Implement `get_llm_input(self) -> Tuple[str, str]`: return `(self.title, self.summary)`.
            *   Modify `TweetItem` to inherit from `LLMPreparable`. Implement `get_llm_input(self) -> Tuple[str, str]`:
                ```python
                author = self.author_username or self.author_id or "unknown_user"
                title = f"Tweet from @{author}"
                content_body = self.text
                return (title, content_body)
                ```
        *   **Test:** ✅
            *   Instantiate a `NewsItem` and a `TweetItem`.
            *   Call `get_llm_input()` on both and verify the returned tuple is correct.

*   **Phase 2: Refactor AI Relevance Judge**
    *   `[x]` **Step 7: Refactor `core/ai_relevance_judge.py`.**
        *   Modify `evaluate_content_relevance(title: str, content: str)`:
            *   Keep its existing logic for calling the LLM.
            *   Change its return type to `RelevanceStatus`.
            *   Update the parsing of `content_response` from the LLM. It should now map:
                *   `"CATEGORY: YES"` (or similar positive) to `RelevanceStatus.RELEVANT`.
                *   `"CATEGORY: NO"` (or similar negative) to `RelevanceStatus.NOT_RELEVANT`.
                *   `"CATEGORY: MAYBE"` (or similar) to `RelevanceStatus.NEEDS_REVIEW`.
                *   Handle cases where the LLM response might not perfectly match, potentially defaulting to `NEEDS_REVIEW` or logging an error.
        *   Create a new primary function:
            ```python
            # In core/ai_relevance_judge.py
            from core.interfaces.llm_preparable_interface import LLMPreparable
            from core.enums import RelevanceStatus # Or from core.models

            def evaluate_item_relevance(item: LLMPreparable) -> RelevanceStatus:
                if not ANTHROPIC_API_KEY:
                    logger.error("ANTHROPIC_API_KEY is not set. Skipping relevance check.")
                    return RelevanceStatus.NEEDS_REVIEW # Or handle as appropriate

                try:
                    title, content_body = item.get_llm_input()
                    # Call the (now modified) internal _evaluate_content_relevance
                    status = _evaluate_content_relevance(title, content_body) # Rename old func
                    # Update the item's relevance directly if desired, or let the caller do it.
                    # If item has a 'relevance' attribute:
                    # item.relevance = status
                    return status
                except Exception as e:
                    logger.error(f"Error evaluating item relevance: {e}")
                    return RelevanceStatus.NEEDS_REVIEW # Default on error
            ```
            (Make the old `evaluate_content_relevance` private, e.g., `_evaluate_content_relevance`)
        *   Remove `evaluate_content_item`, `check_relevance`, `check_tweet_relevance_and_language`, `check_tweet_relevance`, `process_sample`, `process_tweet_sample`. The `process_sample` logic will be moved to `main.py`.
        *   **Test:** ✅
            *   Unit test `_evaluate_content_relevance`'s parsing logic with mock LLM responses for "Yes", "No", "Maybe", and malformed responses.
            *   Test `evaluate_item_relevance` with mock `NewsItem` and `TweetItem` objects (that implement `LLMPreparable`).

*   **Phase 3: Integrate into `main.py` and Update Consumers**
    *   `[ ]` **Step 8: Unified Relevance Evaluation in `main.py`.**
        *   After `all_fetched_items` are collected from all `DataSource`s:
            *   Iterate through `all_fetched_items`.
            *   For each item, if it's an instance of `LLMPreparable` and its `relevance` attribute is currently `None` (or `PENDING_EVALUATION` if you added that state):
                *   Call `new_relevance = evaluate_item_relevance(item)`.
                *   Set `item.relevance = new_relevance`.
                *   Call the appropriate storage update function (e.g., `update_item_relevance` or `update_tweet_relevance`) to persist this new status to the database. This is important because the `DataSource` for X/Twitter might have already set relevance for tweets, so this loop would primarily handle non-X items or items missed by a source's internal evaluation.
        *   Remove the old `process_sample` call for non-tweet items.
        *   Remove the specific AI evaluation logic for tweets from the X/Twitter section in `main.py` (this logic should have moved into `XCollector.fetch_items()` if it decided to return `NewsItem`s with relevance, or this unified loop will catch raw `TweetItem`s if `XCollector` returns those). Ensure there's no double-evaluation.
        *   **Test:**
            *   Run `scripts/main.py`. Verify that both `NewsItem`s (from Google News, Forms, Telegram) and `TweetItem`s (or `NewsItem`s derived from tweets) get their `relevance` field populated with `RelevanceStatus` values.
            *   Check the database to confirm the new `TEXT` values are stored.
            *   Inspect logs for relevance evaluation messages.
    *   `[ ]` **Step 9: Update Notion Publishing Logic (`sources/notion/notion_publisher.py` and `scripts/main.py`).**
        *   Modify `format_news_items_as_blocks` in `notion_publisher.py`:
            *   The `relevance_text` should now reflect the `RelevanceStatus` enum (e.g., `f"Relevance: {item.relevance.value}"`).
        *   In `scripts/main.py`'s Notion publishing section:
            *   When calling `get_news_items_since`, the `relevance` parameter should now be `RelevanceStatus.RELEVANT`.
        *   **Test:**
            *   Run `scripts/main.py` with Notion publishing enabled.
            *   Verify the Notion page includes the correct relevance status string.
            *   Verify only items marked `RelevanceStatus.RELEVANT` are published (unless logic is changed).
    *   `[ ]` **Step 10: Update Rejected Items Scripts (`scripts/publish_rejected_items.py`, `scripts/publish_rejected_items_7days.py`).**
        *   Modify these scripts to query for items where `relevance` is `RelevanceStatus.NOT_RELEVANT.value` or `RelevanceStatus.NEEDS_REVIEW.value` (or both, depending on desired behavior for "rejected review").
        *   Update how they display relevance if they do.
        *   **Test:**
            *   Manually set some items in the DB to `not_relevant` or `needs_review`.
            *   Run these scripts and verify they pick up the correct items and publish them to Notion.

*   **Phase 4: Cleanup and Final Verification**
    *   `[ ]` **Step 11: Code Review and Full System Test.**
        *   Review all changes.
        *   Run `scripts/main.py` end-to-end with all sources enabled.
        *   Verify data flow, relevance evaluation, storage, and Notion publishing.
        *   Check log outputs.
    *   `[ ]` **Step 12: Update Documentation.**
        *   Update any developer documentation or comments that refer to the old boolean relevance or the deprecated AI judge functions.

---

This PRD aims for a structured approach to a significant refactor. The key is to update the data representation (Enum, DB, Models) first, then the interface for providing data to the LLM, then the LLM judge itself, and finally integrate these changes into the main application flow and downstream consumers.