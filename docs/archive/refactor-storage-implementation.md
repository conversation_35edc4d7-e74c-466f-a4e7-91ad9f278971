# Storage Refactoring Implementation Details

This document provides detailed implementation guidance for each file in the refactored storage module.

## Directory Structure

```
core/
├── storage/
│   ├── __init__.py          # Re-exports all public functions
│   ├── db_init.py           # Database initialization functions
│   ├── news_items.py        # News item storage and retrieval
│   ├── tweet_items.py       # Tweet item storage and retrieval
│   └── x_collection.py      # X collection state management
└── storage.py               # Slim wrapper that imports from storage/ (for backward compatibility)
```

## File Contents

### `core/storage/__init__.py`

```python
"""
Storage module for the TAC Weekly News Aggregation System.
This module provides functions for storing and retrieving data from the database.
"""

# Import all functions from submodules
from .db_init import init_db
from .news_items import (
    save_news_items,
    update_item_relevance,
    get_recent_items,
    get_recent_news_items,
)
from .tweet_items import (
    save_tweet_items,
    get_recent_tweets,
    update_tweet_relevance,
    update_tweet_newsletter_status,
)
from .x_collection import (
    initialize_collection_state,
    update_collection_state,
    get_collection_state,
    get_active_collection,
    clear_collection_state,
)

# Export all functions
__all__ = [
    'init_db',
    'save_news_items',
    'update_item_relevance',
    'get_recent_items',
    'get_recent_news_items',
    'save_tweet_items',
    'get_recent_tweets',
    'update_tweet_relevance',
    'update_tweet_newsletter_status',
    'initialize_collection_state',
    'update_collection_state',
    'get_collection_state',
    'get_active_collection',
    'clear_collection_state',
]
```

### `core/storage/db_init.py`

```python
"""
Database initialization functions for the TAC Weekly News Aggregation System.
"""

import sqlite3
import logging
from ..config import DATABASE_PATH

logger = logging.getLogger(__name__)

def init_db():
    """Initialize the database with the necessary tables."""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Create news_items table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS news_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        link TEXT UNIQUE NOT NULL,
        published_at TIMESTAMP NOT NULL,
        summary TEXT,
        publisher TEXT,
        source TEXT NOT NULL,
        fetched_at TIMESTAMP NOT NULL,
        relevance BOOLEAN
    )
    ''')

    # Create tweet_items table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS tweet_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tweet_id TEXT UNIQUE NOT NULL,
        text TEXT NOT NULL,
        author_id TEXT NOT NULL,
        author_username TEXT,
        author_name TEXT,
        created_at TIMESTAMP NOT NULL,
        fetched_at TIMESTAMP NOT NULL,

        -- Tweet type flags
        is_retweet BOOLEAN DEFAULT 0,
        is_quote BOOLEAN DEFAULT 0,
        is_reply BOOLEAN DEFAULT 0,
        has_commentary BOOLEAN DEFAULT 0,

        -- Related tweet IDs
        in_reply_to_tweet_id TEXT,
        in_reply_to_user_id TEXT,
        quoted_tweet_id TEXT,
        retweeted_tweet_id TEXT,

        -- URLs and media (stored as JSON)
        urls TEXT,  -- JSON array of URLs
        hashtags TEXT, -- JSON array of hashtags
        mentions TEXT, -- JSON array of mentions

        -- Individual engagement metrics
        like_count INTEGER DEFAULT 0,
        retweet_count INTEGER DEFAULT 0,
        reply_count INTEGER DEFAULT 0,
        quote_count INTEGER DEFAULT 0,
        impression_count INTEGER,
        bookmark_count INTEGER,

        -- Processing fields
        source TEXT NOT NULL DEFAULT 'x_list_scraper',
        relevance BOOLEAN,
        included_in_newsletter BOOLEAN DEFAULT 0,
        processed_at TIMESTAMP,
        notes TEXT
    )
    ''')

    # Create indices for better query performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_created_at ON tweet_items(created_at)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_author_id ON tweet_items(author_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_relevance ON tweet_items(relevance)')

    # Create x_collection_state table for tracking tweet collection state
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS x_collection_state (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        collection_id TEXT UNIQUE NOT NULL,  -- Unique identifier for this collection run
        start_time TIMESTAMP NOT NULL,       -- Start time of the collection window
        end_time TIMESTAMP NOT NULL,         -- End time of the collection window
        oldest_tweet_id TEXT,                -- ID of the oldest tweet collected
        newest_tweet_id TEXT,                -- ID of the newest tweet collected
        pagination_token TEXT,               -- Token for resuming pagination
        status TEXT NOT NULL,                -- Status: 'in_progress', 'complete', 'failed'
        last_request_time TIMESTAMP,         -- Timestamp of the last API request
        requests_made INTEGER DEFAULT 0,     -- Number of API requests made
        tweets_collected INTEGER DEFAULT 0,  -- Number of tweets collected
        created_at TIMESTAMP NOT NULL,       -- When this collection was started
        updated_at TIMESTAMP NOT NULL,       -- When this collection was last updated
        notes TEXT                           -- Additional notes or error information
    )
    ''')

    # Create index for x_collection_state
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_collection_status ON x_collection_state(status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_collection_time ON x_collection_state(start_time, end_time)')

    conn.commit()
    conn.close()

    logger.info(f"Database initialized at {DATABASE_PATH}")
```

### `core/storage/news_items.py`

```python
"""
News item storage and retrieval functions for the TAC Weekly News Aggregation System.
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Optional
from ..models import NewsItem
from ..config import DATABASE_PATH

logger = logging.getLogger(__name__)

def save_news_items(items: List[NewsItem]) -> int:
    """
    Save a list of news items to the database.
    Returns the number of items successfully saved.
    """
    if not items:
        return 0

    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    saved_count = 0

    for item in items:
        try:
            # Check if item with this link already exists
            cursor.execute("SELECT id FROM news_items WHERE link = ?", (str(item.link),))
            if cursor.fetchone():
                logger.debug(f"Item with link {item.link} already exists, skipping")
                continue

            # Insert the item
            cursor.execute('''
            INSERT INTO news_items
            (title, link, published_at, summary, publisher, source, fetched_at, relevance)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item.title,
                str(item.link),
                item.published_at.isoformat(),
                item.summary,
                item.publisher,
                item.source,
                item.fetched_at.isoformat(),
                item.relevance
            ))
            saved_count += 1

        except Exception as e:
            logger.error(f"Error saving item {item.title}: {e}")

    conn.commit()
    conn.close()

    logger.info(f"Saved {saved_count} new items to database")
    return saved_count


def update_item_relevance(link: str, relevance: bool) -> bool:
    """Update the relevance of a news item by its link."""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "UPDATE news_items SET relevance = ? WHERE link = ?",
            (relevance, link)
        )
        success = cursor.rowcount > 0
        conn.commit()
        return success
    except Exception as e:
        logger.error(f"Error updating relevance for {link}: {e}")
        return False
    finally:
        conn.close()


def get_recent_items(limit: int = 10, source: Optional[str] = None) -> List[NewsItem]:
    """Retrieve the most recent news items from the database."""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    query = "SELECT * FROM news_items ORDER BY fetched_at DESC LIMIT ?"
    params = [limit]

    if source:
        query = "SELECT * FROM news_items WHERE source = ? ORDER BY fetched_at DESC LIMIT ?"
        params = [source, limit]

    cursor.execute(query, params)
    rows = cursor.fetchall()
    conn.close()

    items = []
    for row in rows:
        try:
            item = NewsItem(
                title=row['title'],
                link=row['link'],
                published_at=datetime.fromisoformat(row['published_at']),
                summary=row['summary'],
                publisher=row['publisher'],
                source=row['source'],
                fetched_at=datetime.fromisoformat(row['fetched_at']),
                relevance=row['relevance']
            )
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")

    return items


def get_recent_news_items(days: int = 7, relevance: Optional[bool] = None) -> List[NewsItem]:
    """Retrieve news items from the last X days, optionally filtered by relevance."""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Calculate the date X days ago
    cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()

    if relevance is None:
        # Get all items from the last X days
        query = "SELECT * FROM news_items WHERE published_at >= ? ORDER BY published_at DESC"
        params = [cutoff_date]
    else:
        # Get items from the last X days with specific relevance
        query = "SELECT * FROM news_items WHERE published_at >= ? AND relevance = ? ORDER BY published_at DESC"
        params = [cutoff_date, relevance]

    cursor.execute(query, params)
    rows = cursor.fetchall()
    conn.close()

    items = []
    for row in rows:
        try:
            item = NewsItem(
                title=row['title'],
                link=row['link'],
                published_at=datetime.fromisoformat(row['published_at']),
                summary=row['summary'],
                publisher=row['publisher'],
                source=row['source'],
                fetched_at=datetime.fromisoformat(row['fetched_at']),
                relevance=row['relevance']
            )
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")

    return items
```

### `core/storage/tweet_items.py`

```python
"""
Tweet item storage and retrieval functions for the TAC Weekly News Aggregation System.
"""

import sqlite3
import logging
import json
from datetime import datetime, timedelta
from typing import List, Optional
from ..models import TweetItem
from ..config import DATABASE_PATH

logger = logging.getLogger(__name__)

def save_tweet_items(items: List[TweetItem]) -> int:
    """
    Save a list of tweet items to the database.
    Returns the number of items successfully saved.
    """
    if not items:
        return 0

    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    saved_count = 0

    for item in items:
        try:
            # Check if item with this tweet_id already exists
            cursor.execute("SELECT id FROM tweet_items WHERE tweet_id = ?", (item.tweet_id,))
            if cursor.fetchone():
                logger.debug(f"Tweet with ID {item.tweet_id} already exists, skipping")
                continue

            # Convert lists to JSON strings
            urls_json = json.dumps([str(url) for url in item.urls]) if item.urls else '[]'
            hashtags_json = json.dumps(item.hashtags) if item.hashtags else '[]'
            mentions_json = json.dumps(item.mentions) if item.mentions else '[]'

            # Insert the item with all fields
            cursor.execute('''
            INSERT INTO tweet_items
            (tweet_id, text, author_id, author_username, author_name, created_at, fetched_at,
             is_retweet, is_quote, is_reply, has_commentary,
             in_reply_to_tweet_id, in_reply_to_user_id, quoted_tweet_id, retweeted_tweet_id,
             urls, hashtags, mentions,
             like_count, retweet_count, reply_count, quote_count, impression_count, bookmark_count,
             source, relevance, included_in_newsletter, processed_at, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item.tweet_id,
                item.text,
                item.author_id,
                item.author_username,
                item.author_name,
                item.created_at.isoformat(),
                item.fetched_at.isoformat(),
                item.is_retweet,
                item.is_quote,
                item.is_reply,
                item.has_commentary,
                item.in_reply_to_tweet_id,
                item.in_reply_to_user_id,
                item.quoted_tweet_id,
                item.retweeted_tweet_id,
                urls_json,
                hashtags_json,
                mentions_json,
                item.like_count,
                item.retweet_count,
                item.reply_count,
                item.quote_count,
                item.impression_count,
                item.bookmark_count,
                item.source,
                item.relevance,
                item.included_in_newsletter,
                item.processed_at.isoformat() if item.processed_at else None,
                item.notes
            ))
            saved_count += 1

        except Exception as e:
            logger.error(f"Error saving tweet {getattr(item, 'tweet_id', 'unknown')}: {e}")

    conn.commit()
    conn.close()

    logger.info(f"Saved {saved_count} new tweets to database")
    return saved_count


def get_recent_tweets(limit: int = 10, source: Optional[str] = None, days: Optional[int] = None) -> List[TweetItem]:
    """
    Retrieve the most recent tweets from the database.

    Args:
        limit: Maximum number of tweets to retrieve
        source: Optional source to filter by
        days: Optional number of days to look back

    Returns:
        List of TweetItem objects
    """
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    query = "SELECT * FROM tweet_items"
    params = []
    where_clauses = []

    # Add source filter if provided
    if source:
        where_clauses.append("source = ?")
        params.append(source)

    # Add date filter if days is provided
    if days is not None:
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        where_clauses.append("created_at >= ?")
        params.append(cutoff_date)

    # Add WHERE clause if we have any filters
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)

    # Add ORDER BY and LIMIT
    query += " ORDER BY created_at DESC LIMIT ?"
    params.append(limit)

    cursor.execute(query, params)
    rows = cursor.fetchall()
    conn.close()

    tweets = []
    for row in rows:
        try:
            # Parse JSON fields
            urls = json.loads(row['urls']) if row['urls'] else []
            hashtags = json.loads(row['hashtags']) if row['hashtags'] else []
            mentions = json.loads(row['mentions']) if row['mentions'] else []

            # Create TweetItem with all fields
            tweet = TweetItem(
                tweet_id=row['tweet_id'],  # Using tweet_id consistently
                text=row['text'],
                created_at=datetime.fromisoformat(row['created_at']),
                fetched_at=datetime.fromisoformat(row['fetched_at']),

                # User information
                author_id=row['author_id'],
                author_username=row['author_username'],
                author_name=row['author_name'],

                # Tweet type flags
                is_retweet=bool(row['is_retweet']),
                is_quote=bool(row['is_quote']),
                is_reply=bool(row['is_reply']),
                has_commentary=bool(row['has_commentary']),

                # Related tweet IDs
                in_reply_to_tweet_id=row['in_reply_to_tweet_id'],
                in_reply_to_user_id=row['in_reply_to_user_id'],
                quoted_tweet_id=row['quoted_tweet_id'],
                retweeted_tweet_id=row['retweeted_tweet_id'],

                # URLs and media
                urls=urls,
                hashtags=hashtags,
                mentions=mentions,

                # Individual engagement metrics
                like_count=row['like_count'] or 0,
                retweet_count=row['retweet_count'] or 0,
                reply_count=row['reply_count'] or 0,
                quote_count=row['quote_count'] or 0,
                impression_count=row['impression_count'],
                bookmark_count=row['bookmark_count'],

                # Processing fields
                source=row['source'],
                relevance=row['relevance'],
                included_in_newsletter=bool(row['included_in_newsletter']),
                processed_at=datetime.fromisoformat(row['processed_at']) if row['processed_at'] else None,
                notes=row['notes']
            )
            tweets.append(tweet)
        except Exception as e:
            logger.error(f"Error converting row to TweetItem: {e}")

    return tweets


def update_tweet_relevance(tweet_id: str, relevance: bool) -> bool:
    """Update the relevance of a tweet by its ID."""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "UPDATE tweet_items SET relevance = ? WHERE tweet_id = ?",
            (relevance, tweet_id)
        )
        success = cursor.rowcount > 0
        conn.commit()
        return success
    except Exception as e:
        logger.error(f"Error updating relevance for tweet {tweet_id}: {e}")
        return False
    finally:
        conn.close()


def update_tweet_newsletter_status(tweet_id: str, included: bool) -> bool:
    """Update whether a tweet is included in the newsletter."""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "UPDATE tweet_items SET included_in_newsletter = ?, processed_at = ? WHERE tweet_id = ?",
            (included, datetime.now().isoformat(), tweet_id)
        )
        success = cursor.rowcount > 0
        conn.commit()
        return success
    except Exception as e:
        logger.error(f"Error updating newsletter status for tweet {tweet_id}: {e}")
        return False
    finally:
        conn.close()
```

### `core/storage/x_collection.py`

```python
"""
X collection state management functions for the TAC Weekly News Aggregation System.
"""

import sqlite3
import logging
import uuid
from datetime import datetime
from typing import Optional, Dict
from ..config import DATABASE_PATH

logger = logging.getLogger(__name__)

def initialize_collection_state(start_time: datetime, end_time: datetime) -> Optional[str]:
    """
    Initialize a new collection state entry for X tweet collection.

    Args:
        start_time: Start time of the collection window
        end_time: End time of the collection window

    Returns:
        Collection ID if successful, None otherwise
    """
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    try:
        # Generate a unique collection ID
        collection_id = str(uuid.uuid4())
        now = datetime.now().isoformat()

        # Insert new collection state
        cursor.execute('''
        INSERT INTO x_collection_state
        (collection_id, start_time, end_time, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            collection_id,
            start_time.isoformat(),
            end_time.isoformat(),
            'in_progress',
            now,
            now
        ))

        conn.commit()
        logger.info(f"Initialized collection state with ID: {collection_id}")
        return collection_id

    except Exception as e:
        logger.error(f"Error initializing collection state: {e}")
        return None
    finally:
        conn.close()


def update_collection_state(collection_id: str, **kwargs) -> bool:
    """
    Update an existing collection state entry.

    Args:
        collection_id: ID of the collection to update
        **kwargs: Fields to update (oldest_tweet_id, newest_tweet_id, pagination_token,
                  status, last_request_time, requests_made, tweets_collected, notes)

    Returns:
        True if successful, False otherwise
    """
    if not kwargs:
        logger.warning("No update parameters provided for collection state update")
        return False

    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    try:
        # Build the update query dynamically based on provided kwargs
        set_clauses = []
        params = []

        for key, value in kwargs.items():
            if key in ['oldest_tweet_id', 'newest_tweet_id', 'pagination_token', 'status',
                       'last_request_time', 'requests_made', 'tweets_collected', 'notes']:
                set_clauses.append(f"{key} = ?")
                params.append(value)

        # Always update the updated_at timestamp
        set_clauses.append("updated_at = ?")
        params.append(datetime.now().isoformat())

        # Add collection_id to params
        params.append(collection_id)

        # Execute the update
        query = f"UPDATE x_collection_state SET {', '.join(set_clauses)} WHERE collection_id = ?"
        cursor.execute(query, params)

        success = cursor.rowcount > 0
        conn.commit()

        if success:
            logger.debug(f"Updated collection state for ID: {collection_id}")
        else:
            logger.warning(f"No collection state found with ID: {collection_id}")

        return success

    except Exception as e:
        logger.error(f"Error updating collection state: {e}")
        return False
    finally:
        conn.close()


def get_collection_state(collection_id: Optional[str] = None, status: Optional[str] = None) -> Optional[Dict]:
    """
    Get collection state information.

    Args:
        collection_id: Optional ID of a specific collection to retrieve
        status: Optional status to filter by (e.g., 'in_progress')

    Returns:
        Dictionary with collection state or None if not found
    """
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # Return rows as dictionaries
    cursor = conn.cursor()

    try:
        query = "SELECT * FROM x_collection_state"
        params = []
        where_clauses = []

        if collection_id:
            where_clauses.append("collection_id = ?")
            params.append(collection_id)

        if status:
            where_clauses.append("status = ?")
            params.append(status)

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        # Order by most recently updated first
        query += " ORDER BY updated_at DESC LIMIT 1"

        cursor.execute(query, params)
        row = cursor.fetchone()

        if row:
            # Convert row to dictionary
            state = dict(row)
            return state
        else:
            return None

    except Exception as e:
        logger.error(f"Error retrieving collection state: {e}")
        return None
    finally:
        conn.close()


def get_active_collection() -> Optional[Dict]:
    """
    Get the currently active collection (status = 'in_progress').

    Returns:
        Dictionary with collection state or None if no active collection
    """
    return get_collection_state(status='in_progress')


def clear_collection_state(collection_id: str) -> bool:
    """
    Clear/delete a collection state entry.

    Args:
        collection_id: ID of the collection to delete

    Returns:
        True if successful, False otherwise
    """
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute("DELETE FROM x_collection_state WHERE collection_id = ?", (collection_id,))
        success = cursor.rowcount > 0
        conn.commit()

        if success:
            logger.info(f"Cleared collection state with ID: {collection_id}")
        else:
            logger.warning(f"No collection state found with ID: {collection_id}")

        return success

    except Exception as e:
        logger.error(f"Error clearing collection state: {e}")
        return False
    finally:
        conn.close()
```

### `core/storage.py` (Slim Wrapper)

```python
"""
Storage module for the TAC Weekly News Aggregation System.
This is a slim wrapper that re-exports all functions from the storage submodules.
"""

# Re-export all functions from the storage submodules
from core.storage.db_init import init_db
from core.storage.news_items import (
    save_news_items,
    update_item_relevance,
    get_recent_items,
    get_recent_news_items,
)
from core.storage.tweet_items import (
    save_tweet_items,
    get_recent_tweets,
    update_tweet_relevance,
    update_tweet_newsletter_status,
)
from core.storage.x_collection import (
    initialize_collection_state,
    update_collection_state,
    get_collection_state,
    get_active_collection,
    clear_collection_state,
)

# For backward compatibility, re-export all functions at the module level
__all__ = [
    'init_db',
    'save_news_items',
    'update_item_relevance',
    'get_recent_items',
    'get_recent_news_items',
    'save_tweet_items',
    'get_recent_tweets',
    'update_tweet_relevance',
    'update_tweet_newsletter_status',
    'initialize_collection_state',
    'update_collection_state',
    'get_collection_state',
    'get_active_collection',
    'clear_collection_state',
]
```

## Testing Strategy

For each phase of the refactoring:

1. Create the new file with the extracted functions
2. Run unit tests that specifically test those functions
3. Verify that the functions work correctly in isolation
4. Update the `__init__.py` file to re-export the functions
5. Run integration tests to ensure the functions work correctly when imported from the new location
6. Update the original `storage.py` file to import and re-export the functions
7. Run all tests to ensure backward compatibility

This approach ensures that each step of the refactoring is tested thoroughly before moving on to the next step.
