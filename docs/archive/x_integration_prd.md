# X Integration PRD: Tweet Collection with State Tracking

## Overview
This PRD outlines the implementation of a robust X (Twitter) integration for the TAC Weekly News Aggregation System. The system will collect tweets from a specified X List, analyze them for relevance, and include them in the weekly newsletter. The implementation is broken down into small, testable steps to ensure reliability.

## Goals
- Reliably collect all tweets from the past week despite API rate limits
- Track collection state to handle interruptions and resumptions
- Provide backfilling capability for missed collection periods
- Analyze tweets for relevance to RWA/tokenized assets
- Integrate relevant tweets into the weekly newsletter

## Initial Research and Testing
Before beginning implementation, conduct these research tests to increase confidence and refine the approach:

- [x] **R.1 API Rate Limit Verification**
  - [x] Write script to make repeated calls to List Tweets endpoint
  - [x] Log and analyze rate limit headers from responses
  - [x] Document actual limits for our authentication level
  - **Test**: Run script until rate limits are hit and verify recovery

- [x] **R.2 Time Window vs. ID-Based Pagination**
  - [x] Compare results using `since_id`/`until_id` vs. `start_time`/`end_time`
  - [x] Evaluate completeness and reliability of each approach
  - [x] Document recommended approach with justification
  - **Test**: Fetch same time period using both methods and compare results

- [ ] **R.3 Tweet ID to Timestamp Correlation**
  - [ ] Collect sample of tweet IDs with their timestamps
  - [ ] Analyze correlation between IDs and timestamps
  - [ ] Create function to estimate ID ranges from time windows
  - **Test**: Verify estimated ID ranges correctly capture time windows

- [ ] **R.4 Pagination Token Behavior**
  - [ ] Test pagination through large tweet sets
  - [ ] Determine how long pagination tokens remain valid
  - [ ] Document any limitations or edge cases
  - **Test**: Pause pagination midway and attempt to resume after delays

- [ ] **R.5 API Error Response Analysis**
  - [ ] Trigger various error conditions (rate limits, invalid params, etc.)
  - [ ] Catalog error types, response formats, and status codes
  - [ ] Document appropriate handling strategy for each error type
  - **Test**: Verify error handling approaches for common error types

- [ ] **R.6 Collection Completeness Verification**
  - [ ] Compare results from different collection methods
  - [ ] Identify potential gaps or duplicates in collection
  - [ ] Document approach for ensuring complete collection
  - **Test**: Verify all tweets in a time window are collected without gaps

## Implementation Steps

### Step 1: Enhance State Tracking
Create a robust state tracking mechanism for tweet collection.

- [x] **1.1 Create state tracking table in database**
  - [x] Add `x_collection_state` table to database schema in `storage.py`
  - [x] Include fields for collection timestamp, tweet IDs, and status
  - **Test**: Verify table is created correctly in the database

- [x] **1.2 Implement basic state functions**
  - [x] Function to initialize collection state
  - [x] Function to update state during collection
  - [x] Function to retrieve current state
  - **Test**: Create test script that initializes, updates, and retrieves state

- [x] **1.3 Add state persistence**
  - [x] Ensure state is saved to database after each update
  - [x] Add function to clear/reset state when needed
  - **Test**: Verify state persists after script termination and restart

### Step 2: Refactor X List Scraper for Modularity
Refactor the X List Scraper into a more modular structure for better maintainability and testability.

- [x] **2.1 Create X API client class**
  - [x] Implement class to handle API interactions
  - [x] Add rate limit handling and pagination
  - [x] Support both ID-based and time-based parameters
  - **Test**: Verify API client can fetch tweets with same functionality as original

- [x] **2.2 Create state tracking manager**
  - [x] Implement class to manage collection state
  - [x] Add methods for saving/loading state
  - [x] Integrate with database state tracking
  - **Test**: Verify state is correctly tracked across multiple runs

- [x] **2.3 Create tweet processor**
  - [x] Implement class to process raw tweet data
  - [x] Add filtering options (e.g., only tweets with commentary)
  - [x] Maintain all existing processing logic
  - **Test**: Verify processed tweets match original implementation

- [x] **2.4 Update high-level interface**
  - [x] Maintain backward compatibility
  - [x] Add new parameters for time-based collection
  - [x] Ensure all existing code using the module continues to work
  - **Test**: Run integration tests to verify nothing broke in refactoring

### Step 3: Implement Time-Based Collection
Modify tweet fetching to work with time windows rather than just IDs.

- [x] **3.1 Enhance fetch function parameters**
  - [x] Add start_date and end_date parameters to fetch function
  - [x] Map these to appropriate API parameters
  - [x] **Test**: Fetch tweets from last 24 hours and verify timestamps
  - **Note**: Testing revealed that the X API returns a 400 error for future dates, which needs to be handled

- [x] **3.2 Implement date-to-ID mapping**
  - [x] Function to estimate tweet ID ranges based on dates
  - [x] Function to check if a tweet falls within target time window
  - [x] **Test**: Verify tweets returned match the requested time window

- [x] **3.3 Add time window validation**
  - [x] Ensure requested time windows are valid
  - [x] Handle edge cases (e.g., future dates, very old dates)
  - [x] **Test**: Test with various time windows and verify proper handling

### Step 4: Add Pagination with State Tracking
Ensure we can paginate through all tweets in a time window, saving state as we go.

- [ ] **4.1 Enhance pagination logic**
  - [ ] Save pagination token in state
  - [ ] Resume pagination from saved state
  - **Test**: Verify pagination works across multiple requests

- [ ] **4.2 Implement incremental saving**
  - [ ] Save tweets in batches as they're collected
  - [ ] Mark which tweets have been processed
  - **Test**: Fetch large set of tweets requiring pagination and verify all are saved

- [ ] **4.3 Add interruption/resumption handling**
  - [ ] Simulate interruption during collection
  - [ ] Implement resumption from last saved state
  - **Test**: Interrupt collection process and verify it resumes correctly

### Step 5: Add Rate Limit Handling
Handle X API rate limits gracefully.

- [ ] **5.1 Implement rate limit tracking**
  - [ ] Parse rate limit headers from API responses
  - [ ] Track remaining requests and reset time
  - **Test**: Verify rate limit information is correctly extracted from responses

- [ ] **5.2 Add pause/resume logic**
  - [ ] Pause collection when approaching limits
  - [ ] Save state before pausing
  - [ ] Resume after rate limit reset
  - **Test**: Run collection that hits rate limits and verify pause/resume works

- [ ] **5.3 Add exponential backoff for errors**
  - [ ] Implement retry logic with exponential backoff
  - [ ] Handle different types of errors appropriately
  - **Test**: Simulate API errors and verify retry behavior

### Step 6: Implement Complete Weekly Collection
Put it all together for a complete weekly collection.

- [ ] **6.1 Create weekly collection function**
  - [ ] Calculate appropriate time window (last 7 days)
  - [ ] Use enhanced collection functions with state tracking
  - **Test**: Run complete weekly collection and verify results

- [ ] **6.2 Add deduplication logic**
  - [ ] Ensure no duplicate tweets if collections overlap
  - [ ] Handle edge cases with tweet deletions or API inconsistencies
  - **Test**: Run overlapping collections and verify no duplicates

- [ ] **6.3 Implement collection status reporting**
  - [ ] Add detailed logging of collection process
  - [ ] Create summary report of collection results
  - **Test**: Verify logs and reports accurately reflect collection status

### Step 7: Add Backfilling Capability
Allow catching up if we miss collection periods.

- [ ] **7.1 Implement backfill function**
  - [ ] Accept date range for backfilling
  - [ ] Use collection infrastructure with different parameters
  - **Test**: Simulate missed week and verify backfill collects missed tweets

- [ ] **7.2 Add backfill status tracking**
  - [ ] Track backfill operations separately from regular collection
  - [ ] Report on backfill completion status
  - **Test**: Verify backfill status is correctly tracked and reported

- [ ] **7.3 Implement automatic backfill detection**
  - [ ] Detect missed collection periods
  - [ ] Trigger backfill automatically when needed
  - **Test**: Simulate missed collection and verify automatic backfill

### Step 8: Enhance Tweet Analysis
Improve the AI analysis of tweets for relevance.

- [ ] **8.1 Refine tweet analysis prompt**
  - [ ] Optimize prompt for tweet-specific content
  - [ ] Include relevant metadata in analysis
  - **Test**: Analyze sample tweets and verify accuracy of relevance determination

- [ ] **8.2 Add batch analysis capability**
  - [ ] Process tweets in batches for efficiency
  - [ ] Track analysis state for resumption if interrupted
  - **Test**: Analyze large batch of tweets and verify all are processed

- [ ] **8.3 Implement relevance scoring**
  - [ ] Add nuanced relevance scoring beyond binary yes/no
  - [ ] Store scores in database for filtering
  - **Test**: Verify relevance scores are calculated and stored correctly

### Step 9: Integrate with Main Application
Integrate the enhanced tweet collection with the main application.

- [ ] **9.1 Update main.py**
  - [ ] Replace current X integration with new system
  - [ ] Ensure proper error handling and logging
  - **Test**: Run main application and verify tweet collection works

- [ ] **9.2 Add scheduling logic**
  - [ ] Ensure weekly collection runs at appropriate times
  - [ ] Handle scheduling errors gracefully
  - **Test**: Verify scheduled runs execute correctly

- [ ] **9.3 Integrate with newsletter generation**
  - [ ] Include relevant tweets in newsletter
  - [ ] Format tweets appropriately for Notion
  - **Test**: Generate newsletter with tweets and verify formatting

## Success Criteria
- System reliably collects all tweets from the past week
- Collection process handles interruptions and API rate limits gracefully
- Tweets are accurately analyzed for relevance
- Relevant tweets are properly integrated into the weekly newsletter
- System can backfill missed tweets if collection is interrupted

## Timeline
- Steps 1-2: Foundation building and refactoring (1 week)
- Steps 3-5: Enhanced collection system (1-2 weeks)
- Steps 6-7: Complete collection system with backfilling (1 week)
- Steps 8-9: Analysis and integration (1 week)

## Implementation Notes

### Rate Limit Findings
- The X API has a rate limit of approximately 25 requests per 15-minute window for the List Tweets endpoint
- Rate limit information is provided in response headers (`x-rate-limit-limit`, `x-rate-limit-remaining`, `x-rate-limit-reset`)
- When rate limits are hit, the API returns a 429 error with a `retry-after` header
- Our implementation tracks rate limits and can pause/resume collection when limits are approached

### Time-Based Collection Findings
- The X API supports both ID-based (`since_id`/`until_id`) and time-based (`start_time`/`end_time`) parameters
- Time-based parameters must use ISO 8601 format (YYYY-MM-DDThh:mm:ssZ)
- The API returns a 400 error when future dates are provided
- Time windows should be validated before making API requests

## Risks and Mitigations
- **Risk**: X API changes could break integration
  - **Mitigation**: Implement robust error handling and monitoring
- **Risk**: Rate limits could prevent complete collection
  - **Mitigation**: Implement incremental collection with state tracking
- **Risk**: Large tweet volumes could impact performance
  - **Mitigation**: Implement batch processing and pagination
- **Risk**: Invalid time windows could cause API errors
  - **Mitigation**: Validate time windows before making requests
