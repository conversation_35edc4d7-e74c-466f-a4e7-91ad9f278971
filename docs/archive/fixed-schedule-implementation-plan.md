# Implementation Plan: Fixed Schedule Newsletter Publishing

## Overview
Implement a fixed schedule approach for the newsletter publishing system, where content is published based on a weekly cutoff time (Sunday at 11:59 PM). This ensures that each newsletter contains all content collected since the previous newsletter publication.

## Implementation Steps

### Phase 1: Create Last Publication Timestamp Storage

1. [x] Create a new module `core/storage/publication_state.py` to manage publication state
2. [x] Implement functions to:
   - [x] Initialize the publication state storage (create table if not exists)
   - [x] Get the last publication timestamp
   - [x] Update the last publication timestamp
3. [x] Store the timestamp in the SQLite database (using a dedicated table for publication state)
4. [x] Add appropriate error handling and logging
5. [x] Write unit tests for the publication state functions
6. [x] Implement logic to handle the first run (when no previous timestamp exists) by using a default of 7 days ago

### Phase 2: Modify News Item Retrieval Logic

1. [x] Implement a new function `get_news_items_since` in `core/storage/news_items.py` that:
   - [x] Queries the database for items collected since a given timestamp
   - [x] Returns them sorted by publication date
2. [x] Write unit tests for the `get_news_items_since` function
3. [ ] TODO: Modify the database schema to add a "Maybe" option for relevance (instead of just True/False)

### Phase 3: Integrate with Notion Publishing

1. [x] Update the `publish_to_notion` function to accept a `since_timestamp` parameter (optional)
2. [x] Modify the function to use this parameter when creating the timestamped subpage
3. [x] Update the Notion publisher test to verify timestamp-based filtering

### Phase 4: Integrate with Main Script

1. [x] Modify `scripts/main.py` to:
   - [x] Get the last publication timestamp at the start of the run
   - [x] Pass this timestamp to the Notion publishing function
   - [x] Update the timestamp after successful publishing
2. [x] Add appropriate logging to track which items are being published
3. [x] Update the results summary to show the publication time range

### Phase 5: Testing and Verification

1. [x] Create a test script that:
   - [x] Collects a small amount of content
   - [x] Verifies the correct items are selected for publishing
   - [x] Confirms the timestamp is updated correctly
2. [x] Test with various timestamp scenarios (e.g., items collected before/after the cutoff)

### Phase 6: Documentation

1. [x] Update the README with information about the new publication schedule
2. [x] Document how the system determines which items to publish
3. [x] Add comments to the code explaining the timestamp-based filtering logic

## Testing Approach

For each phase:
1. Write unit tests for new functions before implementation
2. Create integration tests that verify the components work together
3. Test with realistic data scenarios:
   - Items collected before the last publication
   - Items collected after the last publication
   - First-run scenario with no existing timestamp
