# Weekly to Daily Collection Refactoring Plan

## Overview
This plan outlines the systematic removal of weekly collection logic while preserving the daily accretion functionality. The system will continue to run daily and append to weekly Notion pages, creating new pages on Sunday transitions.

---

[x] Phase 1: Remove Weekly-Specific Scripts and Tests
    [x] Step 1.1: Remove Weekly Scheduler
        [x] Substep 1.1.1: Delete scripts/schedule_weekly.py
        [x] Substep 1.1.2: Remove any references to schedule_weekly.py in documentation
    [x] Test for Step 1.1: Verify file deletion and no broken imports
    
    [x] Step 1.2: Remove Weekly Wrapper Script
        [x] Substep 1.2.1: Delete scripts/generate_newsletter.sh
        [x] Substep 1.2.2: Update any documentation mentioning generate_newsletter.sh
    [x] Test for Step 1.2: Verify file deletion and check for broken references
    
    [x] Step 1.3: Remove Weekly Test Files
        [x] Substep 1.3.1: Delete tests/x/test_weekly_collection.py
        [x] Substep 1.3.2: Delete tests/x/test_weekly_collection_function.py
        [x] Substep 1.3.3: Search for and remove any imports of these test files
    [x] Test for Step 1.3: Run test suite to ensure no broken imports

[x] Phase 2: Refactor main.py Command Line Arguments
    [x] Step 2.1: Remove Daily Mode Flag
        [x] Substep 2.1.1: Remove --daily-mode argument definition from parse_args()
        [x] Substep 2.1.2: Remove args.daily_mode references in extract_config_for_source()
        [x] Substep 2.1.3: Update default tweet_collection_days to 1 instead of 7
    [x] Test for Step 2.1: Run main.py --help to verify argument changes
    
    [x] Step 2.2: Remove Force Full Publication Flag
        [x] Substep 2.2.1: Remove --force-full-publication argument definition
        [x] Substep 2.2.2: Remove all args.force_full_publication references
    [x] Test for Step 2.2: Verify argument removal and no AttributeError
    
    [x] Step 2.3: Update Daily Collection Hours Default
        [x] Substep 2.3.1: Change --daily-collection-hours to be the primary time parameter
        [x] Substep 2.3.2: Remove conditional logic for daily vs weekly hour calculation
    [x] Test for Step 2.3: Test argument parsing with various hour values

[x] Phase 3: Simplify main.py Core Logic
    [x] Step 3.1: Remove Run Mode Branching
        [x] Substep 3.1.1: Remove run_type variable and related logging (lines 159-165)
        [x] Substep 3.1.2: Update initial logging to say "TAC Daily News Aggregation System"
        [x] Substep 3.1.3: Remove conditional daily_runs initialization (make it always initialize)
    [x] Test for Step 3.1: Run main.py and verify proper initialization
    
    [x] Step 3.2: Simplify Notion Publishing Logic
        [x] Substep 3.2.1: Remove the if args.daily_mode branch structure (lines 320-360)
        [x] Substep 3.2.2: Keep only the daily mode logic as the default behavior
        [x] Substep 3.2.3: Remove weekly mode publishing logic (lines 361-390)
        [x] Substep 3.2.4: Remove last_publication variable usage in weekly context
    [x] Test for Step 3.2: Test Notion publishing with mock data
    
    [x] Step 3.3: Update Results Display
        [x] Substep 3.3.1: Change "TAC Weekly News Aggregation Results" to "TAC Daily News Aggregation Results"
        [x] Substep 3.3.2: Remove weekly-specific output formatting
        [x] Substep 3.3.3: Remove publication time range display for weekly mode
    [x] Test for Step 3.3: Run main.py and verify output format

[x] Phase 4: Update Configuration and Defaults
    [x] Step 4.1: Update GitHub Actions Workflow
        [x] Substep 4.1.1: Remove --daily-mode flag from the run command
        [x] Substep 4.1.2: Update workflow name if needed
        [x] Substep 4.1.3: Verify --daily-collection-hours parameter remains
    [x] Test for Step 4.1: Manually trigger workflow and verify execution
    
    [x] Step 4.2: Update Import Statements and Module Names
        [x] Substep 4.2.1: Search for "Weekly" in import statements and update to "Daily"
        [x] Substep 4.2.2: Update module docstrings mentioning "Weekly"
    [x] Test for Step 4.2: Run import tests to verify no broken imports

[ ] Phase 5: Storage Layer Simplification
    [x] Step 5.1: Update publication_state.py Documentation
        [x] Substep 5.1.1: Update module docstring from "Weekly" to "Daily"
        [x] Substep 5.1.2: Update function docstrings to reflect daily operation
        [x] Substep 5.1.3: Remove legacy get_last_publication_timestamp function entirely
    [x] Test for Step 5.1: Test publication state functions
    
    [x] Step 5.2: Verify Week Boundary Logic Remains Intact
        [x] Substep 5.2.1: Review is_new_week() function (no changes needed)
        [x] Substep 5.2.2: Review update_weekly_page_id() function (no changes needed)
        [x] Substep 5.2.3: Test week boundary detection logic
    [x] Test for Step 5.2: Unit test for week boundary transitions

[ ] Phase 6: Documentation Updates
    [ ] Step 6.1: Update README.md
        [ ] Substep 6.1.1: Change project title from "Weekly" to "Daily"
        [ ] Substep 6.1.2: Update description to explain daily collection with weekly organization
        [ ] Substep 6.1.3: Remove references to weekly scheduling
        [ ] Substep 6.1.4: Update command examples to remove --daily-mode
    [ ] Test for Step 6.1: Review documentation for accuracy
    
    [ ] Step 6.2: Update CLAUDE.md
        [ ] Substep 6.2.1: Update "Running the Application" section
        [ ] Substep 6.2.2: Remove weekly scheduler references
        [ ] Substep 6.2.3: Update architecture overview to reflect daily operation
        [ ] Substep 6.2.4: Update "Commands" section examples
    [ ] Test for Step 6.2: Verify all commands in documentation work
    
    [ ] Step 6.3: Update Other Documentation Files
        [ ] Substep 6.3.1: Search for and update "Weekly" references in .md files
        [ ] Substep 6.3.2: Update product_requirements_document.md if applicable
        [ ] Substep 6.3.3: Check and update any workflow documentation
    [ ] Test for Step 6.3: Grep for remaining "weekly" references

[ ] Phase 7: Final Integration Testing
    [ ] Step 7.1: End-to-End Daily Collection Test
        [ ] Substep 7.1.1: Run main.py with minimal parameters
        [ ] Substep 7.1.2: Verify data collection from all sources
        [ ] Substep 7.1.3: Verify Notion page appending
    [ ] Test for Step 7.1: Full integration test with live data
    
    [ ] Step 7.2: Week Boundary Transition Test
        [ ] Substep 7.2.1: Simulate Sunday midnight transition
        [ ] Substep 7.2.2: Verify new page creation
        [ ] Substep 7.2.3: Verify continued appending to new page
    [ ] Test for Step 7.2: Mock time-based week transition test
    
    [ ] Step 7.3: Database State Verification
        [ ] Substep 7.3.1: Check publication_state table
        [ ] Substep 7.3.2: Verify daily_runs tracking
        [ ] Substep 7.3.3: Confirm no orphaned weekly references
    [ ] Test for Step 7.3: Database integrity checks

---

## Commit Strategy
- After each completed Step: Commit with message format: "refactor: [Step description]"
- After each completed Phase: Commit with message format: "feat: Complete Phase N - [Phase description]"
- Include this updated plan file in each commit to track progress

## Rollback Strategy
- Each substep is designed to be atomic and reversible
- Git history will allow reverting individual steps if needed
- Database changes are backward compatible

## Success Criteria
- [ ] All weekly-specific code removed
- [ ] Daily collection runs without --daily-mode flag
- [ ] GitHub Actions workflow executes successfully
- [ ] Week boundary transitions work correctly
- [ ] All tests pass
- [ ] Documentation accurately reflects daily operation