# Notion Integration and Content Limiting Issues

## Issues Identified

1. **Excessive Notion Blocks**
   - The script added 784 blocks to Notion despite only fetching 56 unique news items
   - It reported publishing 192 news items to the timestamped Notion subpage
   - This suggests it's pulling additional items from the database beyond what we just collected

2. **Telegram Limit Not Applied**
   - We specified `--telegram-limit 3` but it still fetched 50 items
   - The environment variable approach for limiting Telegram messages isn't working correctly

3. **Tweet Collection Issues**
   - The script processed 2 tweets but filtered them out due to the time window
   - This resulted in 0 tweets being collected
   - Time-based filtering may be too restrictive for testing purposes

## Proposed Solutions

1. **For Excessive Notion Blocks:**
   - Modify the `main.py` script to either:
     - Use the `all_news_items` list we've already collected instead of fetching from the database again
     - Add a command-line argument like `--notion-days` to control how many days of content to publish
     - Add a command-line argument to limit the total number of items published to Notion

2. **For Telegram Limit Not Applied:**
   - Check the `fetch_channel_messages` function in `telegram_scraper.py` to ensure it's properly using the `TELEGRAM_MESSAGE_LIMIT` environment variable
   - Consider modifying the function to directly accept a limit parameter instead of relying on the environment variable
   - Ensure the limit is being applied at the right level (per channel vs. total)

3. **For Tweet Collection Issues:**
   - Add a command-line argument to disable time-based filtering for testing purposes
   - This would allow tweets to be collected regardless of their timestamp

## Implementation Priority

1. Fix the Telegram limit issue first, as it's the most straightforward
2. Address the Notion publishing issue next, to limit the number of items published
3. Consider the tweet collection enhancement last, as it's less critical for testing

These changes would allow us to run a truly minimal test of the entire pipeline, with just a few items from each source and a small number of items published to Notion.
