# TAC Newsletter System TODO List

This document tracks current priorities, recently completed items, and future enhancements for the TAC Newsletter System. For more detailed specifications, see the [X Integration PRD](x_integration_prd.md).

## Recently Completed Items

- ✅ Implemented English language filtering for tweets using a hybrid approach (API + processor + LLM)
- ✅ Completed time-based collection with date-to-ID mapping and validation
- ✅ Added comprehensive testing for time window validation and tweet filtering
- ✅ Refactored X integration into modular components for better maintainability
- ✅ Fixed import structure across the codebase to support feature-based directory organization

## Current Priorities

### Research Tasks
- [x] **Pagination Token Validity**: Determine how long pagination tokens remain valid
  - **Finding**: X API pagination tokens do not expire according to official documentation
  - **Source**: "The next_token does not expire. Multiple requests using the same next_token value will receive the same results, regardless of when the request is made." - X API docs
  - **Implication**: We can safely store pagination tokens and resume collection at any time without worrying about token expiration
- [x] **Error Recovery**: Determine what happens if a token expires during collection
  - **Finding**: Pagination token errors are likely rare based on X API documentation and community experience
  - **Recommendation**: Implement basic error logging to monitor for token errors rather than complex recovery mechanisms
  - **Action**: Continue saving both pagination tokens and time window information to enable fallback strategies if needed
- [x] **State Persistence**: Determine how frequently we should save state during collection
  - **Finding**: The optimal frequency is after each page of tweets is fetched (not just at the end of collection)
  - **Recommendation**: Implement a callback mechanism in `get_all_list_tweets` that updates state after each page
  - **Rationale**: Balances recovery granularity with performance, aligns with X API rate limits (25 requests/15min)
- [x] **Error Handling**: Identify the best approach for handling common API errors
  - **Finding**: X API errors fall into 5 categories: authentication, rate limit, resource, parameter, and server errors
  - **Recommendation**: Implement categorized error handling with appropriate retry logic for each error type
  - **Implementation**: Add a `handle_api_error` method to the `XApiClient` class with exponential backoff for retryable errors
- [x] **List Tweets Endpoint Limitations**: Determine if the List Tweets endpoint supports time-based parameters
  - **Finding**: The List Tweets endpoint (`/2/lists/{id}/tweets`) does not support time-based parameters (`start_time` and `end_time`)
  - **Evidence**: Consistent 400 Bad Request errors when using time-based parameters with this endpoint
  - **Solution**: Implement post-retrieval filtering by date for time-based collection
  - **Implementation**: Fetch tweets without time parameters and filter them by date after retrieval
- [x] **Pagination Token Approach Validation**: Test if pagination tokens can reliably replace time-based parameters
  - **Hypothesis**: We can use pagination tokens to collect tweets continuously and then filter by timestamp
  - **Test Plan**:
    1. **Day 1**: Fetch first page of tweets, record IDs and timestamps, save pagination token
    2. **Day 2**: Use saved token to fetch next page, record IDs and timestamps
    3. **Analysis**: Verify no duplicates between days, confirm we can filter by time window
  - **Success Criteria**: No duplicate tweets between collections, accurate time-based filtering
  - **Results**: Test PASSED - No duplicates between collections, 100% time window coverage

### 1. X Integration: State Tracking and Rate Limit Handling

#### 1.0 Database Setup for State Tracking
Create the necessary database structure for storing collection state.

- [x] **Create database table for collection state**
  - [x] Define schema for `x_collection_state` table (already defined in `init_db` function)
  - [x] Implement function to create table if it doesn't exist (already implemented in `init_db` function)
  - [x] Add initialization script to ensure table exists before use (completed in commit eacf679)
  - [x] **Test**: Verify table is created and can store/retrieve state (completed in commit eacf679)

#### 1.1 Basic State Tracking
Implement a simple checkpoint system to track collection progress.

- [x] **Update `XCollector.fetch_x_list_tweets` to save state after each page**
  - [x] Add page callback functionality to `XApiClient.get_all_list_tweets` (completed in commit 1f5f15a)
  - [x] Save newest and oldest tweet IDs after each page (completed in commit a6f2432)
  - [x] Save pagination token after each page (completed in commit a6f2432)
  - [x] Save timestamps of collection window (completed in commit a6f2432)
  - [x] **Test**: Verify state is saved correctly during multi-page collection (completed in commit a6f2432)

- [x] **Implement simple resumption logic**
  - [x] Add initial pagination token support to `XApiClient.get_all_list_tweets` (completed in commit d75258a)
  - [x] Enhance `resume_collection` to use saved state effectively (completed in commit 306c786)
  - [x] **Test**: Verify pagination token works for resuming collection (completed in commit d75258a)
  - [x] **Finding**: X API pagination tokens do not appear to expire based on our testing

#### 1.2 Rate Limit Handling
Implement basic rate limit detection and waiting.

- [x] **Enhance rate limit handling in `XApiClient`**
  - [x] Add rate limit tracking to `XApiClient` (completed in commit 7b90e74)
  - [x] When a 429 response is received, wait until the reset time (completed in commit 33e764f)
  - [x] Add clear logging when rate limits are hit and when waiting (completed in commit 7b90e74)
  - [x] **Test**: Verify rate limit tracking works with real API (completed in commit df02da9)
  - [x] **Test**: Verify system pauses and resumes correctly when rate limits are hit (completed in commit 33e764f)

- [x] **Add preemptive rate limit checking**
  - [x] Check remaining requests before making API calls (completed in commit 9f1e8d8)
  - [x] Wait if remaining requests are too low (completed in commit 9f1e8d8)
  - [x] Add exponential backoff for error handling (completed in commit 9f1e8d8)
  - [x] Implement automatic retry mechanisms (completed in commit 9f1e8d8)
  - [x] **Test**: Verify system can avoid hitting rate limits (completed in commit 9f1e8d8)

#### 1.3 Reliable Weekly Collection
Implement a function to collect all tweets from the past week.

- [x] **Create weekly collection function**
  - [x] Calculate time window for past 7 days
  - [x] Use state tracking to handle rate limits and interruptions
  - [x] Save tweets to database as they're collected
  - [x] **Test**: Verify all tweets from past week are collected

#### 1.3.1 Pagination Token Approach (Validated)
Implement the pagination token approach for reliable weekly collection.

- [x] **Implement minimal test for pagination token approach**
  - [x] Create Day 1 script to fetch initial tweets and save pagination token
  - [x] Create Day 2 script to use saved token and fetch next batch
  - [x] Implement analysis to verify no duplicates and proper time filtering
  - [x] **Test**: Run the two-day test to validate the approach
  - **Results**: Test PASSED - No duplicates between collections, 100% time window coverage

- [x] **Implement pagination token collection strategy**
  - [x] Update `XCollector` to use pagination tokens for continuous collection
  - [x] Implement post-collection time filtering for weekly reports
  - [x] Add safeguards for token persistence and recovery
  - [x] **Test**: Verify weekly collection works reliably with this approach
  - **Results**: Successfully implemented and tested. The approach ensures complete weekly coverage without missing tweets.

#### 1.4 Integration with Main Application
Integrate X collection with the main application.

- [x] **Update main.py to use X integration**
  - [x] Uncomment and update X integration code in main script
  - [x] Ensure proper error handling
  - [x] **Test**: Run main application and verify X integration works

### 2. Google Form Integration

Form responses spreadsheet: https://docs.google.com/spreadsheets/d/15O_GGK_gTjOyuwRSBHTWEgqM1jRXQ5jSkCYFdxw9ukA/edit?gid=607465681

- [x] Implement form submission retrieval
- [x] Add processing of form data into NewsItems
- [x] Integrate with main application
- [x] **Test**: Verify form submissions can be retrieved and processed correctly

## Tech Debt

- [ ] Refactor `core/storage.py` as it's becoming monolithic and hard to maintain

## Current Tasks

- [x] **Connect AI relevance evaluation to tweet processing**
  - [x] Add a step to evaluate all collected tweets for relevance
  - [x] Update tweet relevance status in the database
  - [x] Ensure relevant tweets are included in the newsletter
  - [x] **Test**: Verify that relevant tweets appear in the final output
  - **Results**: Successfully implemented and tested. The system now evaluates all collected tweets for relevance and includes the relevant ones in the newsletter.

## Future Enhancements

- [ ] Add more sophisticated language detection for edge cases
- [ ] Improve error reporting and monitoring
- [ ] Add metrics collection for system performance
- [ ] Implement content extraction from submitted links
- [ ] Modify database schema to support a three-state relevance field (Yes/No/Maybe) instead of boolean
  - [ ] Update the `news_items` table schema
  - [ ] Modify the `NewsItem` model to use the new type
  - [ ] Update all code that reads or writes to the `relevance` field
  - [ ] Create new AI prompts for three-state relevance evaluation

## Known Issues

- X API returns 400 error for future dates in time-based queries
- ~~Telegram messages are truncated in the output~~ - FIXED: Now using Telegram's maximum message length (4096 characters)
- Rate limits can interrupt long collection sessions
- Need better handling of retweets with commentary detection
- X API pagination tokens are required for resuming collection from a specific point
- When using both time-based parameters (start_time/end_time) and ID-based parameters (since_id/until_id) together, the API may return 400 Bad Request errors
- Tweet collection requires multiple runs to complete for large lists - the main script now handles this automatically
- For reliable pagination, it's best to use minimal parameters in the initial request
- **The List Tweets endpoint (`/2/lists/{id}/tweets`) does not support time-based parameters**, requiring post-retrieval filtering for time-based collection

## Client Requests

- ✅ Ensure only English tweets are included in the newsletter
- [x] Focus on collecting tweets from the past week for each newsletter
- [x] Prioritize reliability over completeness for the initial implementation

## Implementation Notes

### Development Approach
- Following "slow is fast" philosophy - building incrementally with thorough testing
- Prefer small, safe, testable steps to build on a strong foundation
- Conduct housekeeping tasks (updating documentation, committing code) before continuing with implementation

### X API Notes
- X API has a rate limit of approximately 25 requests per 15-minute window for the List Tweets endpoint
- Rate limit information is provided in response headers (`x-rate-limit-limit`, `x-rate-limit-remaining`, `x-rate-limit-reset`)
- When rate limits are hit, the API returns a 429 error with a `retry-after` header
- The X API supports both ID-based (`since_id`/`until_id`) and time-based (`start_time`/`end_time`) parameters for most endpoints
- Time-based parameters must use ISO 8601 format (YYYY-MM-DDThh:mm:ssZ)
- Pagination tokens are required for resuming collection from a specific point
- Pagination tokens do not appear to expire, making them reliable for resuming collection
- Mixing too many different parameter types (time-based, ID-based) can lead to 400 Bad Request errors
- For reliable pagination, it's best to use minimal parameters in the initial request
- The API will not accept future dates in time-based queries
- **IMPORTANT**: The List Tweets endpoint (`/2/lists/{id}/tweets`) does not support time-based parameters (`start_time` and `end_time`), despite what the general API documentation suggests
- For time-based filtering with the List Tweets endpoint, we must fetch tweets without time parameters and filter them by date after retrieval
- Our testing confirmed that pagination tokens do not expire and can be used to resume collection even after several days
- Complete tweet collection often requires multiple runs due to API limits (100 tweets per request)
- The main script now automates the process of collecting all tweets by running multiple collection attempts with appropriate delays
- When a collection is complete, it's marked as such in the database and no longer appears as an active collection
- The time window for tweet collection is calculated from the current date, not from when the collection was started
