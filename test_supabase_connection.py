#!/usr/bin/env python3
"""
Test Supabase database connection
"""
import os
from dotenv import load_dotenv
import psycopg2
from urllib.parse import urlparse

# Load environment variables
load_dotenv(override=True)

def test_connection():
    """Test the Supabase database connection."""
    db_url = os.getenv("SUPABASE_DB_URL")
    
    if not db_url:
        print("❌ SUPABASE_DB_URL not found in environment variables")
        return False
        
    print(f"📡 Attempting to connect to Supabase...")
    print(f"   URL: {db_url[:30]}...") # Show only first part for security
    
    try:
        # Parse the connection URL
        parsed = urlparse(db_url)
        
        # Connect using the URL
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        
        print(f"✅ Connection successful!")
        print(f"   PostgreSQL version: {version[0]}")
        
        # Check if we can create tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            LIMIT 5;
        """)
        tables = cursor.fetchall()
        
        print(f"\n📊 Existing tables in 'public' schema:")
        if tables:
            for table in tables:
                print(f"   - {table[0]}")
        else:
            print("   (No tables found - database is empty)")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== Testing Supabase Database Connection ===\n")
    success = test_connection()
    
    if success:
        print("\n✅ Database connection test passed!")
        print("   Next step: Create migration script to transfer SQLite data")
    else:
        print("\n❌ Database connection test failed!")
        print("   Please check your SUPABASE_DB_URL in .env file")