# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
.venv/

# Python packaging
*.egg-info/
dist/
build/
*.egg

# Data
data/
!data/x_list_members.csv
*.session
*.numbers

# Test results
test_results/

# Logs
logs/
*.log
tac_news_aggregator_*.log
tweet_collection_*.log

# Local configuration
.DS_Store
# .env is committed for this private repository
.env.test
credentials.json
telegram_session_string.txt

# SpecStory
.specstory/

# IDE
.vscode/
.idea/