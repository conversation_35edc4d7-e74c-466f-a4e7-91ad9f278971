#!/usr/bin/env python3
"""
X API Rate Limit Test Script

This script tests the rate limits of the X API for the List Tweets endpoint.
It makes repeated calls to the endpoint and logs the rate limit headers
to determine the actual limits for our authentication level.
"""

import os
import time
import json
from datetime import datetime
import requests
from dotenv import load_dotenv

from core.config import X_BEARER_TOKEN, X_LIST_ID
from core.utils.logging import get_logger

# This research script needs file logging for analysis
logger = get_logger(__name__, add_file_handler=True)

# X API base URL
X_API_BASE_URL = "https://api.twitter.com/2"

def make_api_request(list_id, max_results=10, pagination_token=None):
    """
    Make a request to the X API List Tweets endpoint and return the response.
    
    Args:
        list_id: The ID of the X List to fetch tweets from
        max_results: Maximum number of results to return per request
        pagination_token: Token for pagination (if continuing from a previous request)
        
    Returns:
        Tuple of (response object, rate limit info dictionary)
    """
    if not X_BEARER_TOKEN:
        logger.error("X_BEARER_TOKEN not found in environment variables.")
        return None, None
    
    # Endpoint for List Tweets
    url = f"{X_API_BASE_URL}/lists/{list_id}/tweets"
    
    # Parameters for the request
    params = {
        'max_results': max_results,
        'expansions': 'author_id',
        'tweet.fields': 'created_at,public_metrics',
        'user.fields': 'username,name'
    }
    
    # Add pagination token if provided
    if pagination_token:
        params['pagination_token'] = pagination_token
    
    # Headers with bearer token
    headers = {
        'Authorization': f'Bearer {X_BEARER_TOKEN}'
    }
    
    # Make the request
    try:
        response = requests.get(url, params=params, headers=headers)
        
        # Extract rate limit information from headers
        rate_limit_info = {
            'limit': response.headers.get('x-rate-limit-limit'),
            'remaining': response.headers.get('x-rate-limit-remaining'),
            'reset': response.headers.get('x-rate-limit-reset')
        }
        
        return response, rate_limit_info
        
    except Exception as e:
        logger.error(f"Error making API request: {e}")
        return None, None

def test_rate_limits(list_id, max_requests=100, delay=1):
    """
    Test the rate limits of the X API by making repeated requests.
    
    Args:
        list_id: The ID of the X List to fetch tweets from
        max_requests: Maximum number of requests to make
        delay: Delay between requests in seconds
        
    Returns:
        Dictionary with rate limit test results
    """
    results = {
        'requests_made': 0,
        'rate_limit_hit': False,
        'rate_limit_info': [],
        'errors': []
    }
    
    logger.info(f"Starting rate limit test for list_id: {list_id}")
    logger.info(f"Will make up to {max_requests} requests with {delay}s delay between them")
    
    pagination_token = None
    
    for i in range(max_requests):
        logger.info(f"Making request {i+1}/{max_requests}")
        
        # Make the request
        response, rate_limit_info = make_api_request(list_id, pagination_token=pagination_token)
        results['requests_made'] += 1
        
        # Log rate limit information
        if rate_limit_info:
            logger.info(f"Rate limit info: {json.dumps(rate_limit_info)}")
            rate_limit_info['request_number'] = i+1
            rate_limit_info['timestamp'] = datetime.now().isoformat()
            results['rate_limit_info'].append(rate_limit_info)
        
        # Check if we hit a rate limit
        if response and response.status_code == 429:
            logger.warning(f"Rate limit hit after {i+1} requests!")
            results['rate_limit_hit'] = True
            results['errors'].append({
                'request_number': i+1,
                'status_code': response.status_code,
                'response': response.text,
                'timestamp': datetime.now().isoformat()
            })
            
            # Get retry-after header if available
            retry_after = response.headers.get('retry-after')
            if retry_after:
                retry_seconds = int(retry_after)
                logger.info(f"Retry-After header suggests waiting {retry_seconds} seconds")
                
                # Wait for the suggested time plus a small buffer
                wait_time = retry_seconds + 5
                logger.info(f"Waiting {wait_time} seconds before continuing...")
                time.sleep(wait_time)
            else:
                # If no retry-after header, wait for rate limit reset time
                if rate_limit_info and rate_limit_info['reset']:
                    reset_time = int(rate_limit_info['reset'])
                    current_time = int(time.time())
                    wait_time = reset_time - current_time + 5  # Add 5 seconds buffer
                    
                    if wait_time > 0:
                        logger.info(f"Waiting {wait_time} seconds for rate limit reset...")
                        time.sleep(wait_time)
                else:
                    # If we can't determine wait time, use a default
                    logger.info("No retry information available, waiting 15 minutes...")
                    time.sleep(900)  # 15 minutes in seconds
        
        # Check for other errors
        elif response and response.status_code != 200:
            logger.error(f"Error response: {response.status_code} - {response.text}")
            results['errors'].append({
                'request_number': i+1,
                'status_code': response.status_code,
                'response': response.text,
                'timestamp': datetime.now().isoformat()
            })
        
        # Process successful response
        elif response:
            # Try to get pagination token for next request
            try:
                response_json = response.json()
                meta = response_json.get('meta', {})
                pagination_token = meta.get('next_token')
                
                # If no more results, reset pagination token to start over
                if not pagination_token:
                    pagination_token = None
                    logger.info("No more results, resetting pagination token")
            except Exception as e:
                logger.error(f"Error parsing response JSON: {e}")
                pagination_token = None
        
        # Wait before next request (unless we just did a longer wait for rate limit)
        if not (response and response.status_code == 429):
            time.sleep(delay)
    
    logger.info(f"Rate limit test completed. Made {results['requests_made']} requests.")
    return results

def save_results(results, filename=None):
    """
    Save test results to a JSON file.
    
    Args:
        results: Dictionary with test results
        filename: Optional filename to save results to
        
    Returns:
        Path to saved file
    """
    if not filename:
        filename = f"x_api_rate_limit_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Ensure the directory exists
    os.makedirs(os.path.dirname(os.path.abspath(filename)), exist_ok=True)
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {filename}")
    return filename

def analyze_results(results):
    """
    Analyze rate limit test results and print a summary.
    
    Args:
        results: Dictionary with test results
    """
    logger.info("=== Rate Limit Test Analysis ===")
    
    # Basic statistics
    logger.info(f"Total requests made: {results['requests_made']}")
    logger.info(f"Rate limit hit: {results['rate_limit_hit']}")
    logger.info(f"Total errors: {len(results['errors'])}")
    
    # Analyze rate limit information
    if results['rate_limit_info']:
        # Get the first rate limit info to determine the limit
        first_info = results['rate_limit_info'][0]
        limit = first_info.get('limit')
        logger.info(f"Rate limit (requests per window): {limit}")
        
        # Calculate how quickly we used up the rate limit
        if results['rate_limit_hit'] and limit:
            requests_until_limit = next(
                (info['request_number'] for info in results['rate_limit_info'] 
                 if info.get('remaining') == '0'),
                results['requests_made']
            )
            logger.info(f"Requests until rate limit hit: {requests_until_limit}")
        
        # Check if rate limit reset worked
        if results['rate_limit_hit'] and len(results['rate_limit_info']) > requests_until_limit:
            logger.info("Rate limit reset was successful")
            
            # Calculate reset time
            reset_times = [
                int(info.get('reset', 0)) for info in results['rate_limit_info']
                if info.get('reset')
            ]
            if len(reset_times) >= 2:
                reset_interval = reset_times[1] - reset_times[0]
                logger.info(f"Approximate rate limit reset interval: {reset_interval} seconds")
    
    # Recommendations
    logger.info("\n=== Recommendations ===")
    if results['rate_limit_hit']:
        limit = results['rate_limit_info'][0].get('limit') if results['rate_limit_info'] else 'unknown'
        logger.info(f"The rate limit for this endpoint is {limit} requests per window.")
        logger.info("Recommendations for implementation:")
        logger.info("1. Implement robust rate limit tracking")
        logger.info("2. Add pause/resume logic when approaching limits")
        logger.info("3. Save state after each successful request")
        logger.info("4. Use exponential backoff for retry logic")
    else:
        logger.info("No rate limit was hit during testing.")
        logger.info("Consider increasing the number of requests or decreasing the delay to find the limit.")

def main():
    """Main function to run the rate limit test."""
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Test X API rate limits')
    parser.add_argument('--list-id', type=str, default=X_LIST_ID,
                        help='ID of the X List to test with')
    parser.add_argument('--max-requests', type=int, default=100,
                        help='Maximum number of requests to make')
    parser.add_argument('--delay', type=float, default=1.0,
                        help='Delay between requests in seconds')
    args = parser.parse_args()
    
    # Run the test
    results = test_rate_limits(args.list_id, args.max_requests, args.delay)
    
    # Save results
    save_results(results)
    
    # Analyze results
    analyze_results(results)

if __name__ == "__main__":
    main()
