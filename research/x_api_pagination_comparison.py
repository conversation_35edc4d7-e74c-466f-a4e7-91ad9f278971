#!/usr/bin/env python3
"""
X API Pagination Comparison Test

This script compares different pagination methods for the X API:
1. ID-based pagination (since_id/until_id)
2. Time-based pagination (start_time/end_time)

It fetches tweets using both methods for the same time period and compares
the results to determine which method is more reliable and complete.
"""

import os
import json
from datetime import datetime, timedelta
import requests
from dotenv import load_dotenv

from core.config import X_BEARER_TOKEN, X_LIST_ID
from core.utils.logging import get_logger

logger = get_logger(__name__, add_file_handler=True)

# X API base URL
X_API_BASE_URL = "https://api.twitter.com/2"

def fetch_tweets_by_time(list_id, start_time, end_time, max_results=100):
    """
    Fetch tweets from a list using time-based pagination.
    
    Args:
        list_id: The ID of the X List to fetch tweets from
        start_time: Start time for tweet filtering (ISO 8601 format)
        end_time: End time for tweet filtering (ISO 8601 format)
        max_results: Maximum number of results to return per request
        
    Returns:
        List of tweets and pagination metadata
    """
    if not X_BEARER_TOKEN:
        logger.error("X_BEARER_TOKEN not found in environment variables.")
        return [], {}
    
    all_tweets = []
    pagination_token = None
    pagination_meta = {
        'requests_made': 0,
        'total_tweets': 0,
        'rate_limit_info': []
    }
    
    while True:
        # Endpoint for List Tweets
        url = f"{X_API_BASE_URL}/lists/{list_id}/tweets"
        
        # Parameters for the request
        params = {
            'max_results': max_results,
            'expansions': 'author_id',
            'tweet.fields': 'created_at,public_metrics',
            'user.fields': 'username,name',
            'start_time': start_time,
            'end_time': end_time
        }
        
        # Add pagination token if provided
        if pagination_token:
            params['pagination_token'] = pagination_token
        
        # Headers with bearer token
        headers = {
            'Authorization': f'Bearer {X_BEARER_TOKEN}'
        }
        
        # Make the request
        try:
            logger.info(f"Making time-based request {pagination_meta['requests_made'] + 1}")
            response = requests.get(url, params=params, headers=headers)
            pagination_meta['requests_made'] += 1
            
            # Extract rate limit information from headers
            rate_limit_info = {
                'limit': response.headers.get('x-rate-limit-limit'),
                'remaining': response.headers.get('x-rate-limit-remaining'),
                'reset': response.headers.get('x-rate-limit-reset')
            }
            pagination_meta['rate_limit_info'].append(rate_limit_info)
            
            # Check for errors
            if response.status_code != 200:
                logger.error(f"Error response: {response.status_code} - {response.text}")
                break
            
            # Process successful response
            response_json = response.json()
            tweets = response_json.get('data', [])
            all_tweets.extend(tweets)
            pagination_meta['total_tweets'] = len(all_tweets)
            
            # Check for more results
            meta = response_json.get('meta', {})
            pagination_token = meta.get('next_token')
            
            # If no more results, break the loop
            if not pagination_token:
                break
                
        except Exception as e:
            logger.error(f"Error making API request: {e}")
            break
    
    logger.info(f"Time-based pagination complete. Fetched {len(all_tweets)} tweets in {pagination_meta['requests_made']} requests.")
    return all_tweets, pagination_meta

def fetch_tweets_by_id(list_id, since_id=None, until_id=None, max_results=100):
    """
    Fetch tweets from a list using ID-based pagination.
    
    Args:
        list_id: The ID of the X List to fetch tweets from
        since_id: Only return tweets with IDs greater than this
        until_id: Only return tweets with IDs less than this
        max_results: Maximum number of results to return per request
        
    Returns:
        List of tweets and pagination metadata
    """
    if not X_BEARER_TOKEN:
        logger.error("X_BEARER_TOKEN not found in environment variables.")
        return [], {}
    
    all_tweets = []
    pagination_token = None
    pagination_meta = {
        'requests_made': 0,
        'total_tweets': 0,
        'rate_limit_info': []
    }
    
    while True:
        # Endpoint for List Tweets
        url = f"{X_API_BASE_URL}/lists/{list_id}/tweets"
        
        # Parameters for the request
        params = {
            'max_results': max_results,
            'expansions': 'author_id',
            'tweet.fields': 'created_at,public_metrics',
            'user.fields': 'username,name'
        }
        
        # Add ID-based parameters if provided
        if since_id:
            params['since_id'] = since_id
        if until_id:
            params['until_id'] = until_id
        
        # Add pagination token if provided
        if pagination_token:
            params['pagination_token'] = pagination_token
        
        # Headers with bearer token
        headers = {
            'Authorization': f'Bearer {X_BEARER_TOKEN}'
        }
        
        # Make the request
        try:
            logger.info(f"Making ID-based request {pagination_meta['requests_made'] + 1}")
            response = requests.get(url, params=params, headers=headers)
            pagination_meta['requests_made'] += 1
            
            # Extract rate limit information from headers
            rate_limit_info = {
                'limit': response.headers.get('x-rate-limit-limit'),
                'remaining': response.headers.get('x-rate-limit-remaining'),
                'reset': response.headers.get('x-rate-limit-reset')
            }
            pagination_meta['rate_limit_info'].append(rate_limit_info)
            
            # Check for errors
            if response.status_code != 200:
                logger.error(f"Error response: {response.status_code} - {response.text}")
                break
            
            # Process successful response
            response_json = response.json()
            tweets = response_json.get('data', [])
            all_tweets.extend(tweets)
            pagination_meta['total_tweets'] = len(all_tweets)
            
            # Check for more results
            meta = response_json.get('meta', {})
            pagination_token = meta.get('next_token')
            
            # If no more results, break the loop
            if not pagination_token:
                break
                
        except Exception as e:
            logger.error(f"Error making API request: {e}")
            break
    
    logger.info(f"ID-based pagination complete. Fetched {len(all_tweets)} tweets in {pagination_meta['requests_made']} requests.")
    return all_tweets, pagination_meta

def get_id_range_for_time_period(list_id, start_time, end_time):
    """
    Get the ID range for a specific time period by fetching a sample of tweets.
    
    Args:
        list_id: The ID of the X List to fetch tweets from
        start_time: Start time for tweet filtering (ISO 8601 format)
        end_time: End time for tweet filtering (ISO 8601 format)
        
    Returns:
        Tuple of (since_id, until_id) or (None, None) if not found
    """
    # Fetch a small sample of tweets for the time period
    tweets, _ = fetch_tweets_by_time(list_id, start_time, end_time, max_results=10)
    
    if not tweets:
        logger.warning("No tweets found for the specified time period.")
        return None, None
    
    # Sort tweets by ID
    tweets_sorted = sorted(tweets, key=lambda x: x['id'])
    
    # Get the oldest and newest tweet IDs
    oldest_id = tweets_sorted[0]['id']
    newest_id = tweets_sorted[-1]['id']
    
    logger.info(f"ID range for time period: since_id={oldest_id}, until_id={newest_id}")
    return oldest_id, newest_id

def compare_results(time_based_tweets, id_based_tweets):
    """
    Compare the results from time-based and ID-based pagination.
    
    Args:
        time_based_tweets: List of tweets fetched using time-based pagination
        id_based_tweets: List of tweets fetched using ID-based pagination
        
    Returns:
        Dictionary with comparison results
    """
    # Extract tweet IDs for comparison
    time_based_ids = set(tweet['id'] for tweet in time_based_tweets)
    id_based_ids = set(tweet['id'] for tweet in id_based_tweets)
    
    # Find common and unique tweets
    common_ids = time_based_ids.intersection(id_based_ids)
    only_in_time_based = time_based_ids - id_based_ids
    only_in_id_based = id_based_ids - time_based_ids
    
    # Calculate statistics
    total_unique_tweets = len(time_based_ids.union(id_based_ids))
    time_based_coverage = len(time_based_ids) / total_unique_tweets if total_unique_tweets > 0 else 0
    id_based_coverage = len(id_based_ids) / total_unique_tweets if total_unique_tweets > 0 else 0
    overlap_percentage = len(common_ids) / total_unique_tweets if total_unique_tweets > 0 else 0
    
    # Prepare comparison results
    comparison = {
        'total_unique_tweets': total_unique_tweets,
        'time_based_count': len(time_based_tweets),
        'id_based_count': len(id_based_tweets),
        'common_count': len(common_ids),
        'only_in_time_based_count': len(only_in_time_based),
        'only_in_id_based_count': len(only_in_id_based),
        'time_based_coverage': time_based_coverage,
        'id_based_coverage': id_based_coverage,
        'overlap_percentage': overlap_percentage,
        'only_in_time_based': list(only_in_time_based),
        'only_in_id_based': list(only_in_id_based)
    }
    
    return comparison

def save_results(results, filename=None):
    """
    Save test results to a JSON file.
    
    Args:
        results: Dictionary with test results
        filename: Optional filename to save results to
        
    Returns:
        Path to saved file
    """
    if not filename:
        filename = f"x_api_pagination_comparison_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Ensure the directory exists
    os.makedirs(os.path.dirname(os.path.abspath(filename)), exist_ok=True)
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {filename}")
    return filename

def analyze_results(comparison):
    """
    Analyze comparison results and print a summary.
    
    Args:
        comparison: Dictionary with comparison results
    """
    logger.info("=== Pagination Comparison Analysis ===")
    
    # Basic statistics
    logger.info(f"Total unique tweets across both methods: {comparison['total_unique_tweets']}")
    logger.info(f"Tweets from time-based pagination: {comparison['time_based_count']}")
    logger.info(f"Tweets from ID-based pagination: {comparison['id_based_count']}")
    logger.info(f"Common tweets found by both methods: {comparison['common_count']}")
    
    # Coverage analysis
    logger.info(f"Time-based coverage: {comparison['time_based_coverage']:.2%}")
    logger.info(f"ID-based coverage: {comparison['id_based_coverage']:.2%}")
    logger.info(f"Overlap between methods: {comparison['overlap_percentage']:.2%}")
    
    # Unique tweets analysis
    logger.info(f"Tweets only found by time-based pagination: {comparison['only_in_time_based_count']}")
    logger.info(f"Tweets only found by ID-based pagination: {comparison['only_in_id_based_count']}")
    
    # Recommendations
    logger.info("\n=== Recommendations ===")
    if comparison['time_based_coverage'] > comparison['id_based_coverage']:
        logger.info("Time-based pagination appears to be more complete for this test period.")
        logger.info("Recommendation: Use time-based pagination (start_time/end_time) as the primary method.")
    elif comparison['id_based_coverage'] > comparison['time_based_coverage']:
        logger.info("ID-based pagination appears to be more complete for this test period.")
        logger.info("Recommendation: Use ID-based pagination (since_id/until_id) as the primary method.")
    else:
        logger.info("Both pagination methods performed similarly for this test period.")
        logger.info("Recommendation: Consider using a hybrid approach or choose based on other factors like API behavior.")
    
    # Additional recommendations
    if comparison['overlap_percentage'] < 0.9:  # Less than 90% overlap
        logger.info("\nNote: There is significant difference between the results from the two methods.")
        logger.info("Consider implementing both methods and combining results for maximum coverage.")

def main():
    """Main function to run the pagination comparison test."""
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Compare X API pagination methods')
    parser.add_argument('--list-id', type=str, default=X_LIST_ID,
                        help='ID of the X List to test with')
    parser.add_argument('--days', type=int, default=1,
                        help='Number of days in the past to test')
    parser.add_argument('--max-results', type=int, default=100,
                        help='Maximum number of results per request')
    args = parser.parse_args()
    
    # Calculate time period for testing
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=args.days)
    
    # Format times for API
    start_time_str = start_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    end_time_str = end_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    
    logger.info(f"Testing pagination methods for period: {start_time_str} to {end_time_str}")
    
    # Get ID range for the time period
    since_id, until_id = get_id_range_for_time_period(args.list_id, start_time_str, end_time_str)
    
    # Fetch tweets using time-based pagination
    logger.info("Fetching tweets using time-based pagination...")
    time_based_tweets, time_based_meta = fetch_tweets_by_time(
        args.list_id, start_time_str, end_time_str, args.max_results
    )
    
    # Fetch tweets using ID-based pagination
    logger.info("Fetching tweets using ID-based pagination...")
    id_based_tweets, id_based_meta = fetch_tweets_by_id(
        args.list_id, since_id, until_id, args.max_results
    )
    
    # Compare results
    comparison = compare_results(time_based_tweets, id_based_tweets)
    
    # Add metadata to results
    results = {
        'test_parameters': {
            'list_id': args.list_id,
            'start_time': start_time_str,
            'end_time': end_time_str,
            'since_id': since_id,
            'until_id': until_id,
            'max_results': args.max_results
        },
        'time_based_meta': time_based_meta,
        'id_based_meta': id_based_meta,
        'comparison': comparison
    }
    
    # Save results
    save_results(results)
    
    # Analyze results
    analyze_results(comparison)

if __name__ == "__main__":
    main()
