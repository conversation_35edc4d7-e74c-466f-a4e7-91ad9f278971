# X API Research Findings

## Rate Limit Testing

### Test Results
- The X API has a rate limit of **25 requests per 15-minute window** for the List Tweets endpoint.
- After reaching the limit, the API returns a 429 "Too Many Requests" error.
- The rate limit headers (`x-rate-limit-limit`, `x-rate-limit-remaining`, `x-rate-limit-reset`) are consistently provided in responses.
- The `x-rate-limit-reset` header provides a Unix timestamp for when the rate limit will reset.

### Recommendations
1. Implement robust rate limit tracking by parsing these headers from each response.
2. Add pause/resume logic that waits until the reset time when approaching the limit.
3. Save state after each successful request to enable resumption if interrupted.
4. Use exponential backoff for retry logic when encountering errors.

## Pagination Method Comparison

### Test Results
- We were unable to complete a full comparison due to rate limit constraints.
- Both time-based (`start_time`/`end_time`) and ID-based (`since_id`/`until_id`) pagination methods are available.
- The API returned 429 errors when attempting to test both methods in succession.

### Preliminary Recommendations
1. Implement a hybrid approach that uses both methods as appropriate:
   - Use time-based pagination for initial collection to ensure we get the correct time window
   - Use ID-based pagination for resumption if interrupted
2. Add sufficient delays between requests to avoid hitting rate limits
3. Implement comprehensive error handling for 429 responses

## Next Steps
1. Modify the test scripts to include longer delays between requests
2. Run the pagination comparison test after the rate limit window resets
3. Collect more data on the correlation between tweet IDs and timestamps
4. Test pagination token behavior and longevity

## Implementation Implications
- The 25 requests per 15-minute window limit means we need to carefully manage our collection process
- For a weekly collection, we may need to spread requests over multiple hours
- State tracking becomes critical to handle the necessary pauses and resumptions
- We should prioritize implementing the state tracking database in Step 1 of our plan
