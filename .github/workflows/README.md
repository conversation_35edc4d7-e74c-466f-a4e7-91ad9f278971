# GitHub Actions Setup for TAC News Aggregation

This workflow automatically runs the TAC News Aggregation System daily at 9:00 AM UTC.

## Setup Instructions

### 1. Configure GitHub Secrets

Go to your repository's Settings → Secrets and variables → Actions, then add these secrets:

#### Required Secrets:

**Telegram:**
- `TELEGRAM_API_ID` - Your Telegram API ID
- `TELEGRAM_API_HASH` - Your Telegram API Hash
- `TELEGRAM_PHONE_NUMBER` - Your phone number for Telegram
- `TELEGRAM_SESSION_STRING` - Base64 encoded session string (see below)

**X/Twitter:**
- `X_BEARER_TOKEN` - Your X API Bearer Token
- `X_LIST_ID` - The X List ID to monitor

**Google Forms:**
- `GOOGLE_FORM_SHEET_ID` - Your Google Sheet ID
- `GOOGLE_SERVICE_ACCOUNT_JSON` - Full JSON content of service account key

**Notion:**
- `NOTION_API_TOKEN` - Your Notion integration token
- `NOTION_PAGE_ID` - Parent page ID for newsletters

**OpenAI:**
- `OPENAI_API_KEY` - Your OpenAI API key for relevance evaluation

### 2. Optional Configuration Variables

Go to Settings → Secrets and variables → Actions → Variables tab:

- `KEYWORDS` - Comma-separated keywords (default: RWA-related terms)
- `SAMPLE_SIZE` - Number of items to evaluate (default: 50)

### 3. Generate Telegram Session String

To generate the `TELEGRAM_SESSION_STRING`, run locally:

```python
from telethon.sync import TelegramClient
from telethon.sessions import StringSession
import base64

api_id = "YOUR_API_ID"
api_hash = "YOUR_API_HASH"

with TelegramClient(StringSession(), api_id, api_hash) as client:
    session_string = client.session.save()
    encoded = base64.b64encode(session_string.encode()).decode()
    print(f"TELEGRAM_SESSION_STRING={encoded}")
```

### 4. Test the Workflow

After adding all secrets:
1. Go to Actions tab
2. Select "Daily News Collection" workflow
3. Click "Run workflow" button to test manually

## Schedule

The workflow runs daily at:
- 9:00 AM UTC
- 1:00 AM PST
- 4:00 AM EST

To change the schedule, edit the cron expression in `.github/workflows/daily-collection.yml`.

## Monitoring

- Check the Actions tab for run history and logs
- Failed runs will show with a red X
- Logs are saved as artifacts for 7 days

## Troubleshooting

1. **Authentication Errors**: Double-check all secrets are set correctly
2. **Telegram Session Issues**: Regenerate the session string if it expires
3. **Rate Limits**: The workflow may fail if API rate limits are hit
4. **Missing Dependencies**: Ensure requirements.txt is up to date