name: Daily News Collection

on:
  schedule:
    # Run at 9:00 AM UTC every day
    # This is 2:00 AM PST / 5:00 AM EST
    - cron: '0 9 * * *'
  workflow_dispatch:  # Allow manual triggering for testing

jobs:
  collect-news:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch all history to ensure we can reset to latest
        
    - name: Ensure latest main branch
      run: |
        echo "Initial checkout commit: $(git rev-parse HEAD)"
        git fetch origin main
        git reset --hard origin/main
        echo "Reset to latest main commit: $(git rev-parse HEAD)"
        echo "Commit date: $(git log -1 --format=%cd)"
        echo "Commit message: $(git log -1 --pretty=%B)"
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e .
    
    - name: Run daily collection
      env:
        # Telegram configuration
        TELEGRAM_API_ID: ${{ secrets.TELEGRAM_API_ID }}
        TELEGRAM_API_HASH: ${{ secrets.TELEGRAM_API_HASH }}
        TELEGRAM_PHONE_NUMBER: ${{ secrets.TELEGRAM_PHONE_NUMBER }}
        TELEGRAM_SESSION_STRING: ${{ secrets.TELEGRAM_SESSION_STRING }}
        
        # X/Twitter configuration
        X_BEARER_TOKEN: ${{ secrets.X_BEARER_TOKEN }}
        X_LIST_ID: ${{ secrets.X_LIST_ID }}
        
        # Google Forms configuration
        GOOGLE_FORM_SHEET_ID: ${{ secrets.GOOGLE_FORM_SHEET_ID }}
        GOOGLE_SERVICE_ACCOUNT_JSON: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_JSON }}
        
        # Notion configuration
        NOTION_API_TOKEN: ${{ secrets.NOTION_API_TOKEN }}
        NOTION_PAGE_ID: ${{ secrets.NOTION_PAGE_ID }}
        
        # Anthropic configuration
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        
        # Email notification configuration
        EMAIL_ENABLED: ${{ vars.EMAIL_ENABLED || 'true' }}
        SMTP_HOST: ${{ secrets.SMTP_HOST }}
        SMTP_PORT: ${{ secrets.SMTP_PORT || '587' }}
        SMTP_USER: ${{ secrets.SMTP_USER }}
        SMTP_PASSWORD: ${{ secrets.SMTP_PASSWORD }}
        EMAIL_FROM_ADDRESS: ${{ secrets.EMAIL_FROM_ADDRESS }}
        EMAIL_RECIPIENTS: ${{ secrets.EMAIL_RECIPIENTS }}
        
        # Database configuration
        SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
        
        # Optional configuration
        KEYWORDS: ${{ vars.KEYWORDS || 'Real World Assets,RWA,Tokenization,Tokenized,digital assets,blockchain real estate,tokenized securities,asset tokenization,security tokens,digital securities,tokenized assets' }}
        SAMPLE_SIZE: ${{ vars.SAMPLE_SIZE || '50' }}
        
      run: |
        python scripts/main.py --daily-collection-hours 24 --enable-email-alerts
    
    - name: Upload logs as artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: collection-logs
        path: |
          *.log
          logs/
        retention-days: 7