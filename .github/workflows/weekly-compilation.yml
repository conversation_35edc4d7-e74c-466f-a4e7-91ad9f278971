name: Weekly Compilation

on:
  schedule:
    # Run at 11:00 UTC every Thursday (2 hours after daily collection)
    - cron: '0 11 * * 4'
  workflow_dispatch:  # Allow manual trigger for testing

jobs:
  create-compilation:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e .
    
    - name: Create weekly compilation
      env:
        NOTION_API_TOKEN: ${{ secrets.NOTION_API_TOKEN }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
      run: |
        echo "Creating weekly compilation for current week..."
        # The script will automatically get the current weekly page ID from the database
        python scripts/create_weekly_compilation.py \
          ${{ secrets.NOTION_COMPILATION_PARENT_ID }}
    
    - name: Upload logs as artifact
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: compilation-logs
        path: |
          *.log
          logs/
        retention-days: 7