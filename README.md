# TAC Weekly News Aggregation System

A system that fetches news related to tokenized assets and real-world assets (RWA) from Google News RSS, Telegram channels, X Lists, and Google Form submissions, stores them in a local database, uses <PERSON> (<PERSON><PERSON><PERSON>'s <PERSON><PERSON>) to evaluate their relevance, and publishes a weekly newsletter to Notion.

## Features

- Fetches news from Google News RSS using predefined keywords
- Scrapes messages from Telegram channels focused on crypto and RWA topics (preserving up to 4096 characters per message)
- Collects tweets from X Lists with AI-based relevance evaluation
- Collects submissions from a Google Form for TAC members
- Parses RSS feed data, Telegram messages, tweets, and form submissions into structured format
- Stores news items in a local SQLite database
- Uses <PERSON> to perform relevance checks on articles
- Publishes the weekly newsletter to a timestamped subpage in Notion
- Logs operations and displays results

## Setup

1. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

   For development, install the package in editable mode:
   ```bash
   pip install -e .
   ```
   This will make the package importable from anywhere, resolving import issues.

2. Create a `.env` file in the project root with your API keys:
   ```
   ANTHROPIC_API_KEY=your_anthropic_api_key_here
   TELEGRAM_API_ID=your_telegram_api_id_here
   TELEGRAM_API_HASH=your_telegram_api_hash_here
   X_BEARER_TOKEN=your_x_bearer_token_here
   GOOGLE_FORM_SHEET_ID=your_google_form_sheet_id_here
   GOOGLE_CREDENTIALS_FILE=path_to_your_google_credentials_file
   NOTION_API_TOKEN=your_notion_api_token_here
   NOTION_PAGE_ID=your_notion_page_id_here sup
   ```

3. Run the Telegram setup script to authenticate (only needed once):
   ```bash
   python sources/telegram/telegram_setup.py
   ```
   This will prompt you to enter your phone number and verification code.

4. Set up Google Form integration (if using):
   - Create a service account in the Google Cloud Console
   - Enable the Google Sheets API
   - Download the service account credentials JSON file
   - Share your Google Form responses spreadsheet with the service account email
   - The form responses spreadsheet is available at:
     https://docs.google.com/spreadsheets/d/15O_GGK_gTjOyuwRSBHTWEgqM1jRXQ5jSkCYFdxw9ukA/edit?gid=*********
   - For local development: Save the credentials as `credentials.json` in the project root
   - For GitHub Actions: Add the entire JSON content as a secret named `GOOGLE_SERVICE_ACCOUNT_JSON`
   - Run the setup script to verify the configuration:
   ```bash
   python sources/google_form/google_form_setup.py
   ```

5. Ensure the `data` directory exists (it will be created automatically if not)

## Usage

### Main Script with Command-Line Arguments

The main script now handles the complete process, including tweet collection:
```bash
python scripts/main.py [options]
```

Available options:

**Tweet Collection Options:**
- `--skip-tweet-collection`: Skip tweet collection entirely
- `--max-tweet-results N`: Maximum number of results per tweet collection attempt (default: 100)
- `--tweet-collection-days N`: Number of days to look back for tweets (default: 7)
- `--max-tweet-attempts N`: Maximum number of tweet collection attempts (default: 5)
- `--tweet-attempt-delay N`: Delay between tweet collection attempts in seconds (default: 0)

**Content Source Options:**
- `--skip-google-news`: Skip Google News RSS fetching
- `--google-news-items N`: Maximum number of items to fetch per Google News keyword (default: 10)
- `--skip-telegram`: Skip Telegram channel scraping
- `--telegram-limit N`: Override the number of messages to fetch per Telegram channel
- `--skip-form`: Skip Google Form submissions

**Publication Options:**
- `--skip-notion`: Skip publishing to Notion

**Evaluation Options:**
- `--sample-size N`: Override the number of items to check with LLM for relevance

Examples:
```bash
# Run with default settings (includes tweet collection)
python scripts/main.py

# Skip tweet collection and use tweets already in the database
python scripts/main.py --skip-tweet-collection

# Collect tweets with custom parameters
python scripts/main.py --max-tweet-results 50 --tweet-collection-days 3 --max-tweet-attempts 3

# Skip certain components
python scripts/main.py --skip-telegram --skip-form
```

The script will:
1. Collect tweets from the X List (unless skipped)
2. Fetch news from Google News RSS for the keywords defined in `core/config.py` (unless skipped)
3. Scrape messages from Telegram channels defined in `core/config.py` (unless skipped)
4. Fetch submissions from the Google Form (if configured and not skipped)
5. Parse and store unique items in the SQLite database
6. Evaluate relevance of new items using Claude AI
7. Check for time-sensitive content and send email alerts (if enabled)
8. Retrieve relevant items not yet published to the current week's page
9. Append new relevant items to the weekly Notion page (Thursday to Thursday)
10. Create a new weekly page when Thursday transition is detected
11. Display the results and log the operations

### Daily Collection System

The system uses a daily collection approach with weekly newsletter publishing:

1. **Daily Runs**: The script runs daily to collect and evaluate new content from all sources.

2. **Weekly Newsletter**: Content is appended to a weekly Notion page that covers Thursday to Thursday.

3. **Automatic Week Transitions**: When Thursday is detected, the system automatically creates a new weekly page.

4. **Content Deduplication**: The system ensures items are only published once by tracking what's already on the current week's page.

5. **Relevance Filtering**: Only content marked as RELEVANT is published to Notion.

6. **Time-Sensitive Alerts**: Content marked as time-sensitive triggers email notifications (when enabled).

7. **Email Options**: Use `--enable-email-alerts` to send notifications for urgent content, or `--email-dry-run` to test without sending.

This approach ensures continuous content collection with organized weekly newsletters and immediate alerts for time-sensitive information.

### Testing Components

You can test individual components separately:

```bash
# Test Telegram scraper
python tests/telegram/test_telegram.py

# Test X integration
python tests/x/test_x_integration.py

# Test tweet relevance evaluation
python tests/x/test_tweet_relevance_evaluation.py

# Test Notion integration
python tests/notion/test_notion_connection.py
```

## Project Structure

```
tac-newsletter/
├── core/                   # Core functionality
│   ├── __init__.py
│   ├── config.py          # Configuration parameters and settings
│   ├── models.py          # Pydantic model defining the structure of news items
│   ├── storage/           # Database operations
│   │   ├── __init__.py
│   │   ├── db_init.py     # Database initialization
│   │   ├── news_items.py  # News item storage operations
│   │   ├── tweet_items.py # Tweet storage operations
│   │   └── publication_state.py # Publication timestamp tracking
│   └── ai_relevance_judge.py # Unified approach for evaluating content relevance using Claude
├── sources/               # Data source modules
│   ├── __init__.py
│   ├── google_rss.py      # Functions for fetching and parsing Google News RSS feeds
│   ├── google_form/       # Google Form integration
│   │   ├── __init__.py
│   │   ├── google_forms.py      # Functions for fetching submissions from Google Forms
│   │   └── google_form_setup.py  # Setup script for Google Form integration
│   ├── telegram/          # Telegram integration
│   │   ├── __init__.py
│   │   ├── telegram_scraper.py   # Functions for scraping Telegram channels
│   │   └── telegram_setup.py     # Setup script for Telegram authentication
│   ├── x/                 # X (Twitter) integration components
│   │   ├── __init__.py
│   │   ├── api_client.py        # Client for X API
│   │   ├── collector.py         # Tweet collection functionality
│   │   ├── state_manager.py     # Manages collection state
│   │   ├── tweet_processor.py   # Processes tweets
│   │   ├── collection_runner.py # Handles running the collection process
│   │   ├── pagination_token_store.py # Manages pagination tokens
│   │   ├── time_filter.py       # Filters tweets by time
│   │   └── x_list_scraper.py    # Scrapes X lists
│   └── notion/            # Notion integration components
│       ├── __init__.py
│       ├── notion_publisher.py  # Publishes content to Notion
│       └── subpage_creator.py   # Creates timestamped subpages in Notion
├── scripts/               # Application scripts
│   ├── main.py            # Main script that orchestrates the entire process (including tweet collection)
│   ├── generate_newsletter.sh # Shell script wrapper for main.py with default parameters
│   └── schedule_weekly.py # Script for scheduling weekly runs
├── tests/                 # Test scripts
│   ├── __init__.py
│   ├── db/                # Database tests
│   │   ├── __init__.py
│   │   ├── test_db_schema.py
│   │   ├── test_tweet_db.py
│   │   └── test_tweet_storage.py
│   ├── google_form/       # Google Form integration tests
│   │   ├── __init__.py
│   │   └── test_form_integration.py
│   ├── notion/            # Notion integration tests
│   │   ├── __init__.py
│   │   ├── test_notion_integration.py
│   │   └── test_notion_publisher.py
│   ├── rss/               # RSS feed tests
│   │   ├── __init__.py
│   │   └── test_rss.py
│   ├── telegram/          # Telegram integration tests
│   │   ├── __init__.py
│   │   ├── test_telegram.py
│   │   └── test_message_truncation.py  # Tests for Telegram message truncation
│   └── x/                 # X (Twitter) integration tests
│       ├── __init__.py
│       ├── test_language_filtering.py
│       ├── test_main_integration.py
│       ├── test_pagination_token_live.py
│       ├── test_state_persistence_live.py
│       ├── test_time_based_collection.py
│       ├── test_x_ai_analysis.py
│       ├── test_x_integration.py
│       ├── test_x_list.py
│       └── test_x_state_tracking.py
├── data/                  # Data files
│   ├── news_feed.db       # SQLite database storing all fetched news items
│   └── x_list_members.csv # List of X members for the TAC list
├── logs/                  # Log files from application runs
├── test_results/          # JSON files with test results
├── docs/                  # Documentation
│   ├── x_integration_prd.md  # Product requirements document for X integration
│   └── TODO.md            # Project TODO list
├── .gitignore
├── README.md
└── requirements.txt
```

## Scheduled Execution

To run this script weekly, you can set up a cron job (on Unix-based systems) or a scheduled task (on Windows).

Example cron job to run the complete newsletter generation process every Sunday at 11:59 PM (Pacific Time):
```
59 23 * * 0 cd /path/to/TAC\ Weekly\ News\ Aggregation\ System && ./scripts/generate_newsletter.sh
```

Alternatively, you can call the main script directly with custom parameters:
```
59 23 * * 0 cd /path/to/TAC\ Weekly\ News\ Aggregation\ System && python scripts/main.py --max-tweet-results 100 --tweet-collection-days 7
```

The system is designed to run on a fixed weekly schedule (Sunday at 11:59 PM Pacific Time), with each run:
1. Collecting content from all sources
2. Publishing only the content collected since the last successful publication
3. Updating the publication timestamp in the database

This ensures that each newsletter contains only new content, even if the script is run multiple times between scheduled publications.

## Extending the System

The system is designed to be modular for easy extension. To add new sources:
1. Create new modules in the `sources/` directory
2. Implement functions that return `NewsItem` objects
3. Integrate these functions in `scripts/main.py`

## Import Structure

The project uses a feature-based directory structure with a consistent import system:

1. **Core Module**: The `core` package exports all essential models, utilities, and configuration:
   ```python
   from core.models import NewsItem, TweetItem
   from core.config import DATABASE_PATH, X_BEARER_TOKEN
   from core.storage.news_items import save_news_items, get_news_items_since
   from core.storage.tweet_items import save_tweet_items
   from core.storage.publication_state import get_last_publication_timestamp, update_publication_timestamp
   ```

2. **Source Modules**: Each source module handles its own imports and exports specific functionality:
   ```python
   from sources.x.collector import XCollector
   from sources.x.api_client import XApiClient
   from sources.google_rss import fetch_google_news
   from sources.notion.notion_publisher import publish_to_notion
   from sources.telegram.telegram_scraper import fetch_telegram_news
   ```

3. **Development Mode**: Installing the package in development mode (`pip install -e .`) ensures all modules can be imported correctly from anywhere in the codebase.