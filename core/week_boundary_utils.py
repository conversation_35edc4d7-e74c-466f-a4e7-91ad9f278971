"""
Week boundary utilities for the TAC Daily News Aggregation System.

This module provides functions for calculating week boundaries and managing
the Thursday midnight cutoff for weekly page transitions in Pacific Time.
"""

from datetime import datetime, timedelta
import logging
import pytz
from core.timezone_config import now_utc

logger = logging.getLogger(__name__)

# Pacific timezone for all week calculations
PACIFIC_TZ = pytz.timezone('US/Pacific')


def get_current_week_start():
    """
    Get the start of the current week (Thursday at midnight Pacific Time).
    
    Returns:
        datetime: The start of the current week (Thursday 00:00:00 PT)
    """
    # Get current time in Pacific timezone
    now = datetime.now(PACIFIC_TZ)
    
    # Calculate days since Thursday (Thursday = 3 in <PERSON>'s weekday())
    # Python's weekday() returns Monday = 0, Tuesday = 1, Wednesday = 2, Thursday = 3, Friday = 4, Saturday = 5, Sunday = 6
    days_since_thursday = (now.weekday() - 3) % 7
    
    # Go back to Thursday and set time to midnight
    week_start = (now - timedelta(days=days_since_thursday)).replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    
    logger.debug(f"Current week start: {week_start.isoformat()}")
    return week_start


def get_next_week_cutoff():
    """
    Get the cutoff time for the next week (next Thursday at midnight Pacific Time).
    
    Returns:
        datetime: The start of the next week (next Thursday 00:00:00 PT)
    """
    current_week_start = get_current_week_start()
    next_week_start = current_week_start + timedelta(days=7)
    
    logger.debug(f"Next week cutoff: {next_week_start.isoformat()}")
    return next_week_start


def get_week_date_range(week_start_date=None):
    """
    Get a formatted date range string for a week.
    
    Args:
        week_start_date (datetime, optional): Start of the week. 
                                            Defaults to current week start.
    
    Returns:
        str: Formatted date range (e.g., "Dec 31 - Jan 6, 2024")
    """
    if week_start_date is None:
        week_start_date = get_current_week_start()
    
    week_end_date = week_start_date + timedelta(days=6)
    
    # Handle year transitions
    if week_start_date.year == week_end_date.year:
        # Same year
        date_range = f"{week_start_date.strftime('%b %d')} - {week_end_date.strftime('%b %d, %Y')}"
    else:
        # Different years (week spans year boundary)
        date_range = f"{week_start_date.strftime('%b %d, %Y')} - {week_end_date.strftime('%b %d, %Y')}"
    
    return date_range


def is_in_current_week(timestamp):
    """
    Check if a timestamp falls within the current week.
    
    Args:
        timestamp (datetime): The timestamp to check
    
    Returns:
        bool: True if the timestamp is in the current week, False otherwise
    """
    current_week_start = get_current_week_start()
    next_week_cutoff = get_next_week_cutoff()
    
    return current_week_start <= timestamp < next_week_cutoff


def get_week_number(date=None):
    """
    Get the ISO week number for a given date.
    
    Args:
        date (datetime, optional): The date to check. Defaults to now.
    
    Returns:
        tuple: (year, week_number) following ISO 8601
    """
    if date is None:
        date = now_utc()
    
    # Get ISO calendar (year, week_number, day_of_week)
    iso_year, iso_week, _ = date.isocalendar()
    
    return iso_year, iso_week


def calculate_daily_window(end_time=None, hours=24):
    """
    Calculate a time window for daily collection.
    
    Args:
        end_time (datetime, optional): End of the window. Defaults to now.
        hours (int): Number of hours to look back. Defaults to 24.
    
    Returns:
        tuple: (start_time, end_time)
    """
    if end_time is None:
        end_time = now_utc()
    
    start_time = end_time - timedelta(hours=hours)
    
    return start_time, end_time