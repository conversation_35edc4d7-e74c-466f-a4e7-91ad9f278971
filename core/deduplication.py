"""
Deduplication module for TAC News Aggregation System.

This module provides functionality to identify and mark duplicate news items
based on URL matching, prioritizing items from primary sources. All items
are preserved with duplicate metadata for analysis.
"""

from typing import List, Dict, Set, Union, Tuple
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
import re
from collections import defaultdict

from core.models import NewsItem, TweetItem
from core.utils.logging import get_logger

logger = get_logger(__name__)

# Source priority order (higher index = higher priority)
SOURCE_PRIORITY = {
    'google_form': 0,      # Lowest priority - manual submissions
    'telegram': 1,         # Secondary commentary/links
    'x_list_scraper': 2,   # Primary social media
    'google_news_rss': 3   # Highest priority - primary news articles
}

# Common tracking parameters to remove for URL normalization
TRACKING_PARAMS = {
    'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
    'fbclid', 'gclid', 'msclkid', 'ref', 'referer', 'referrer',
    '__twitter_impression', 'amp', 's', 'feature', 'sr_share'
}


def normalize_url(url: str) -> str:
    """
    Normalize a URL for comparison purposes.
    
    - Convert to lowercase
    - Remove tracking parameters
    - Remove trailing slashes
    - Remove fragment (#)
    - Sort remaining query parameters for consistency
    """
    try:
        parsed = urlparse(url.lower())
        
        # Remove fragment
        parsed = parsed._replace(fragment='')
        
        # Parse and filter query parameters
        if parsed.query:
            params = parse_qs(parsed.query, keep_blank_values=True)
            # Remove tracking parameters
            filtered_params = {
                k: v for k, v in params.items() 
                if k not in TRACKING_PARAMS
            }
            # Sort parameters for consistency
            sorted_params = sorted(filtered_params.items())
            new_query = urlencode(sorted_params, doseq=True)
            parsed = parsed._replace(query=new_query)
        
        # Reconstruct URL
        normalized = urlunparse(parsed)
        
        # Remove trailing slash from path (but keep root slash)
        if normalized.endswith('/') and not normalized.endswith('://'):
            normalized = normalized.rstrip('/')
        
        return normalized
    except Exception as e:
        logger.warning(f"Error normalizing URL {url}: {e}")
        return url.lower()  # Fallback to simple lowercase


def extract_urls(item: Union[NewsItem, TweetItem]) -> Set[str]:
    """
    Extract all URLs from an item.
    
    For NewsItem: returns the main link
    For TweetItem: returns all URLs mentioned in the tweet
    """
    urls = set()
    
    if isinstance(item, NewsItem):
        urls.add(str(item.link))
    elif isinstance(item, TweetItem):
        # Add all URLs from the tweet
        for url in item.urls:
            urls.add(str(url))
        
        # Also check tweet text for any embedded links that might have been missed
        # This is a simple regex pattern for common URL formats
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s<>"{}|\\^`\[\].,;:!?\'"()]*'
        found_urls = re.findall(url_pattern, item.text)
        for url in found_urls:
            urls.add(url)
    
    return urls


def get_item_identifier(item: Union[NewsItem, TweetItem]) -> Tuple[str, str]:
    """
    Get the type and identifier for an item.
    
    Returns:
        Tuple of (item_type, item_id)
    """
    if isinstance(item, NewsItem):
        return ('news_item', str(item.link))
    else:  # TweetItem
        return ('tweet_item', item.tweet_id)


def mark_duplicates(items: List[Union[NewsItem, TweetItem]]) -> Tuple[List[Union[NewsItem, TweetItem]], int]:
    """
    Mark duplicate items based on URL matching without removing them.
    
    This function:
    1. Groups items by normalized URLs
    2. Selects the highest priority item as canonical
    3. Marks all other items as duplicates with metadata
    4. Returns all items with duplicate flags set
    
    Args:
        items: List of NewsItem and/or TweetItem objects
        
    Returns:
        Tuple of (all_items_with_duplicate_flags, num_duplicates_marked)
    """
    if not items:
        return items, 0
    
    logger.info(f"=== Starting deduplication for {len(items)} items ===")
    
    # Log source distribution
    source_counts = defaultdict(int)
    for item in items:
        source_counts[getattr(item, 'source', 'unknown')] += 1
    
    logger.info("Initial item distribution by source:")
    for source, count in sorted(source_counts.items()):
        logger.info(f"  - {source}: {count} items")
    
    # Dictionary to track normalized URL -> list of (priority, item)
    url_to_items: Dict[str, List[Tuple[int, Union[NewsItem, TweetItem]]]] = defaultdict(list)
    
    # Map each item to all its URLs using item ID as key
    item_to_urls: Dict[str, Tuple[Union[NewsItem, TweetItem], Set[str]]] = {}
    
    # Process each item
    for item in items:
        urls = extract_urls(item)
        item_type, item_id = get_item_identifier(item)
        item_to_urls[item_id] = (item, urls)
        
        # Get priority for this item's source
        source = getattr(item, 'source', 'unknown')
        priority = SOURCE_PRIORITY.get(source, -1)
        
        # Add this item to all its normalized URLs
        for url in urls:
            normalized_url = normalize_url(url)
            url_to_items[normalized_url].append((priority, item))
    
    # Track which items are canonical (not duplicates) by their ID
    canonical_item_ids = set()
    
    # For each URL, find the highest priority item
    for normalized_url, items_with_priority in url_to_items.items():
        if len(items_with_priority) > 1:
            logger.debug(f"Found {len(items_with_priority)} items sharing URL: {normalized_url[:80]}...")
            # Sort by priority (descending) to get the canonical item
            sorted_items = sorted(items_with_priority, key=lambda x: x[0], reverse=True)
            canonical_priority, canonical_item = sorted_items[0]
            canonical_type, canonical_id = get_item_identifier(canonical_item)
            canonical_item_ids.add(canonical_id)
            
            # Mark all other items as duplicates
            for priority, item in sorted_items[1:]:
                item_type, item_id = get_item_identifier(item)
                if item_id not in canonical_item_ids:  # Don't mark an item as duplicate if it's canonical for another URL
                    item.is_duplicate = True
                    item.duplicate_of_url = normalized_url
                    item.duplicate_of_item_type = canonical_type
                    item.duplicate_of_item_id = canonical_id
                    
                    # Generate human-readable reason
                    canonical_source = getattr(canonical_item, 'source', 'unknown')
                    item_source = getattr(item, 'source', 'unknown')
                    
                    if canonical_source == item_source:
                        # Same source - must be duplicate content within that source
                        if isinstance(item, TweetItem) and isinstance(canonical_item, TweetItem):
                            item.deduplication_reason = f"Duplicate tweet with same URL(s) (tweet {item.tweet_id} duplicates {canonical_item.tweet_id})"
                        else:
                            item.deduplication_reason = f"Duplicate content with same URL within {item_source}"
                    else:
                        # Different sources - explain priority
                        item.deduplication_reason = f"Same URL from lower priority source: {item_source} (priority {priority}) < {canonical_source} (priority {canonical_priority})"
        else:
            # Single item for this URL, it's canonical
            _, item = items_with_priority[0]
            item_type, item_id = get_item_identifier(item)
            canonical_item_ids.add(item_id)
    
    # Count duplicates
    duplicates_marked = sum(1 for item in items if item.is_duplicate)
    
    # Log deduplication results
    logger.info(f"\n=== Deduplication Results ===")
    logger.info(f"Total items processed: {len(items)}")
    logger.info(f"Duplicates marked: {duplicates_marked}")
    logger.info(f"Canonical items: {len(items) - duplicates_marked}")
    
    if duplicates_marked > 0:
        # Count by source
        source_counts = defaultdict(lambda: {'total': 0, 'duplicates': 0, 'canonical': 0})
        duplicate_examples = []
        
        for item in items:
            source = getattr(item, 'source', 'unknown')
            source_counts[source]['total'] += 1
            if item.is_duplicate:
                source_counts[source]['duplicates'] += 1
                # Collect first 3 examples for logging
                if len(duplicate_examples) < 3:
                    item_type, item_id = get_item_identifier(item)
                    duplicate_examples.append({
                        'type': item_type,
                        'source': source,
                        'id': item_id,
                        'duplicate_of': item.duplicate_of_item_id,
                        'url': item.duplicate_of_url,
                        'reason': item.deduplication_reason
                    })
            else:
                source_counts[source]['canonical'] += 1
        
        logger.info("\nDeduplication statistics by source:")
        for source, counts in sorted(source_counts.items()):
            logger.info(f"  - {source}: {counts['total']} total "
                       f"({counts['canonical']} canonical, {counts['duplicates']} duplicates)")
        
        logger.info("\nExample duplicates detected:")
        for i, example in enumerate(duplicate_examples, 1):
            logger.info(f"  {i}. {example['type']} from {example['source']}")
            logger.info(f"     ID: {example['id'][:50]}...")
            logger.info(f"     Duplicate of: {example['duplicate_of'][:50]}...")
            logger.info(f"     Matched URL: {example.get('url', 'N/A')[:80]}...")
            logger.info(f"     Reason: {example['reason']}")
    else:
        logger.info("No duplicates found - all items are unique")
    
    logger.info("=== Deduplication complete ===\n")
    
    return items, duplicates_marked