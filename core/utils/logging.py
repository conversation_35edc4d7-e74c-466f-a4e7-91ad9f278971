"""
Centralized logging configuration for the TAC News Aggregation System.

This module provides a single source of truth for logging configuration,
eliminating the need for duplicate logging setup code across the codebase.
"""

import logging
import sys
from datetime import datetime
from typing import Optional, List
from core.timezone_config import now_utc

# Track if logging has been initialized
_initialized = False
# Track if file handler has been added
_file_handler_added = False


def get_logger(name: Optional[str] = None, add_file_handler: bool = False) -> logging.Logger:
    """
    Get a configured logger instance.
    
    This function ensures that logging is configured consistently across
    the entire application. The configuration is only set up once, on the
    first call to this function.
    
    Args:
        name: The name for the logger. If None, uses the caller's __name__.
              Typically, you should pass __name__ from the calling module.
        add_file_handler: If True, adds a dated file handler for the main script.
                         This should only be set to True by the main script.
    
    Returns:
        A configured logging.Logger instance.
        
    Example:
        from core.utils.logging import get_logger
        logger = get_logger(__name__)
        logger.info("This is a log message")
        
        # In main script only:
        logger = get_logger(__name__, add_file_handler=True)
    """
    global _initialized, _file_handler_added
    
    if not _initialized:
        # Configure the root logger only once
        handlers: List[logging.Handler] = [logging.StreamHandler(sys.stdout)]
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        _initialized = True
    
    # Add file handler if requested and not already added
    if add_file_handler and not _file_handler_added:
        file_handler = logging.FileHandler(
            f"tac_news_aggregator_{now_utc().strftime('%Y%m%d')}.log"
        )
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        logging.getLogger().addHandler(file_handler)
        _file_handler_added = True
    
    # Return a logger with the specified name
    return logging.getLogger(name)


def set_log_level(level: str) -> None:
    """
    Set the log level for all loggers.
    
    Args:
        level: The log level as a string (e.g., 'DEBUG', 'INFO', 'WARNING', 'ERROR')
    """
    numeric_level = getattr(logging, level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {level}')
    
    logging.getLogger().setLevel(numeric_level)
    
    # Update all existing loggers
    for logger_name in logging.root.manager.loggerDict:
        logging.getLogger(logger_name).setLevel(numeric_level)