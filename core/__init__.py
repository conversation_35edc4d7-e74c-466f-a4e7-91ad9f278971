"""Core module for TAC Weekly News Aggregation System.

This module exports core models, utilities, and configuration.
"""

# Define what's exported
__all__ = [
    # Models
    'NewsItem', 'TweetItem',

    # Storage utilities
    'init_db', 'save_news_items', 'get_recent_news_items', 'update_item_relevance',
    'get_recent_items', 'save_tweet_items', 'get_recent_tweets', 'update_tweet_relevance',
    'update_tweet_newsletter_status', 'initialize_collection_state', 'update_collection_state',
    'get_collection_state', 'get_active_collection', 'clear_collection_state',

    # Configuration
    'ANTHROPIC_API_KEY', 'NOTION_API_TOKEN', 'NOTION_PAGE_ID',
    'X_BEARER_TOKEN', 'X_LIST_ID', 'X_TWEET_LIMIT', 'TELEGRAM_API_ID', 'TELEGRAM_API_HASH',
    'GOOGLE_CREDENTIALS_FILE', 'GOOGLE_FORM_SHEET_ID'
]

# Export core models
from .models import NewsItem, TweetItem

# Export storage utilities
from .storage import (
    init_db,
    save_news_items,
    get_recent_news_items,
    update_item_relevance,
    get_recent_items,
    save_tweet_items,
    get_recent_tweets,
    update_tweet_relevance,
    update_tweet_newsletter_status,
    initialize_collection_state,
    update_collection_state,
    get_collection_state,
    get_active_collection,
    clear_collection_state
)

# Export configuration
from .config import (
    ANTHROPIC_API_KEY,
    NOTION_API_TOKEN,
    NOTION_PAGE_ID,
    X_BEARER_TOKEN,
    X_LIST_ID,
    X_TWEET_LIMIT,
    TELEGRAM_API_ID,
    TELEGRAM_API_HASH,
    GOOGLE_CREDENTIALS_FILE,
    GOOGLE_FORM_SHEET_ID
)