"""
Centralized timezone configuration for TAC News Aggregation System.

This module provides a single source of truth for timezone handling across the application.
All timezone operations should use these utilities to ensure consistency.
"""

from datetime import datetime
from typing import Optional, Union
import pytz
from pytz import timezone

# Primary timezone for the application (client is in PST)
APP_TIMEZONE = timezone('America/Los_Angeles')
APP_TIMEZONE_NAME = 'America/Los_Angeles'
APP_TIMEZONE_ABBR = 'PST/PDT'  # Changes with daylight saving

# Storage timezone (always UTC)
STORAGE_TIMEZONE = pytz.UTC
STORAGE_TIMEZONE_NAME = 'UTC'

def now_utc() -> datetime:
    """Get current time in UTC with timezone info."""
    return datetime.now(STORAGE_TIMEZONE)

def now_pst() -> datetime:
    """Get current time in PST/PDT with timezone info."""
    return datetime.now(APP_TIMEZONE)

def ensure_utc(dt: datetime) -> datetime:
    """
    Ensure a datetime is in UTC timezone.
    
    Args:
        dt: Datetime to convert
        
    Returns:
        Timezone-aware datetime in UTC
        
    Raises:
        ValueError: If datetime is naive and we can't safely convert
    """
    if dt.tzinfo is None:
        # For naive datetimes, we assume they're already in UTC
        # This matches current system behavior but logs a warning
        import logging
        logging.getLogger(__name__).warning(
            f"Naive datetime {dt} encountered. Assuming UTC. "
            "Please use timezone-aware datetimes."
        )
        return dt.replace(tzinfo=STORAGE_TIMEZONE)
    
    # Convert timezone-aware datetime to UTC
    return dt.astimezone(STORAGE_TIMEZONE)

def ensure_pst(dt: datetime) -> datetime:
    """
    Ensure a datetime is in PST/PDT timezone.
    
    Args:
        dt: Datetime to convert
        
    Returns:
        Timezone-aware datetime in PST/PDT
        
    Raises:
        ValueError: If datetime is naive and we can't safely convert
    """
    if dt.tzinfo is None:
        # For naive datetimes, we assume they're in UTC (system default)
        # Convert to PST/PDT
        import logging
        logging.getLogger(__name__).warning(
            f"Naive datetime {dt} encountered. Assuming UTC for conversion to PST. "
            "Please use timezone-aware datetimes."
        )
        dt = dt.replace(tzinfo=STORAGE_TIMEZONE)
    
    # Convert timezone-aware datetime to PST/PDT
    return dt.astimezone(APP_TIMEZONE)

def format_time_pst(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S %Z") -> str:
    """
    Format a datetime in PST/PDT timezone.
    
    Args:
        dt: Datetime to format
        format_str: strftime format string
        
    Returns:
        Formatted string in PST/PDT
    """
    pst_time = ensure_pst(dt)
    return pst_time.strftime(format_str)

def format_time_utc(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S UTC") -> str:
    """
    Format a datetime in UTC timezone.
    
    Args:
        dt: Datetime to format
        format_str: strftime format string
        
    Returns:
        Formatted string in UTC
    """
    utc_time = ensure_utc(dt)
    return utc_time.strftime(format_str)

def make_timezone_aware(dt: datetime, assume_tz: Optional[pytz.tzinfo.BaseTzInfo] = None) -> datetime:
    """
    Make a naive datetime timezone-aware.
    
    Args:
        dt: Datetime to make timezone-aware
        assume_tz: Timezone to assume if datetime is naive (defaults to UTC)
        
    Returns:
        Timezone-aware datetime
    """
    if dt.tzinfo is not None:
        return dt
    
    if assume_tz is None:
        assume_tz = STORAGE_TIMEZONE
        
    return dt.replace(tzinfo=assume_tz)

def parse_datetime_assume_utc(dt_string: str, format_str: str) -> datetime:
    """
    Parse a datetime string and assume it's in UTC if no timezone info provided.
    
    This is specifically for data ingestion where external sources provide
    naive timestamps that we know should be interpreted as UTC.
    
    Args:
        dt_string: The datetime string to parse
        format_str: The strptime format string
        
    Returns:
        Timezone-aware datetime in UTC
    """
    dt = datetime.strptime(dt_string, format_str)
    return dt.replace(tzinfo=STORAGE_TIMEZONE)

def utc_from_timestamp(timestamp: float) -> datetime:
    """
    Convert a Unix timestamp to a timezone-aware UTC datetime.
    
    Args:
        timestamp: Unix timestamp (seconds since epoch)
        
    Returns:
        Timezone-aware datetime in UTC
    """
    return datetime.fromtimestamp(timestamp, tz=STORAGE_TIMEZONE)

# Backward compatibility with existing code
PACIFIC_TZ = APP_TIMEZONE  # For week_boundary_utils.py
UTC_TZ = STORAGE_TIMEZONE  # For storage layers