"""
Email alert system for time-sensitive content in the TAC News Aggregation System.
Sends daily digest emails when time-sensitive content is detected.
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from datetime import datetime
from typing import List, Tuple, Optional, Union
from jinja2 import Template

from core.config import (
    EMAIL_ENABLED, SMTP_HOST, SMTP_PORT, SMTP_USER, 
    SMTP_PASSWORD, EMAIL_FROM_ADDRESS, EMAIL_RECIPIENTS,
    NOTION_PAGE_ID
)
from core.models import NewsItem, TweetItem
from core.enums import RelevanceStatus
from core.utils.logging import get_logger
from core.timezone_config import now_utc

logger = get_logger(__name__)

# Email template for time-sensitive content alerts
EMAIL_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .item { margin-bottom: 30px; border-left: 4px solid #3498db; padding-left: 15px; }
        .item-title { font-weight: bold; font-size: 18px; margin-bottom: 5px; }
        .item-meta { color: #666; font-size: 14px; margin-bottom: 10px; }
        .item-reason { background-color: #e8f4f8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .item-link { color: #3498db; text-decoration: none; }
        .footer { background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 Time-Sensitive TAC News Alert</h1>
        <p>{{ date_str }}</p>
    </div>
    
    <div class="content">
        <p>The following {{ item_count }} item(s) have been identified as time-sensitive and require your attention:</p>
        
        {% for item, reason in items %}
        <div class="item">
            <div class="item-title">
                <a href="{{ item.link }}" style="color: #2c3e50; text-decoration: none; font-weight: bold;">
                    {{ item.title if item.title else item.text[:100] + '...' }}
                </a>
            </div>
            <div class="item-meta">
                {% if item.__class__.__name__ == 'NewsItem' %}
                    Source: {{ item.publisher }} | Published: {{ item.published_at.strftime('%Y-%m-%d %H:%M UTC') }}
                {% else %}
                    Tweet by @{{ item.author_username or 'unknown' }} | Posted: {{ item.created_at.strftime('%Y-%m-%d %H:%M UTC') }}
                {% endif %}
            </div>
            <div class="item-reason">
                <strong>⏰ Time-sensitive because:</strong> {{ reason }}
            </div>
            {% if item.__class__.__name__ == 'NewsItem' %}
                <div class="item-summary">{{ item.summary[:300] }}...</div>
                <a href="{{ item.link }}" class="item-link">Read full article →</a>
            {% else %}
                <div class="item-summary">{{ item.text }}</div>
                <a href="https://twitter.com/{{ item.author_username }}/status/{{ item.tweet_id }}" class="item-link">View on X →</a>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if limit_warning %}
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin-top: 30px;">
            <strong>⚠️ {{ limit_warning }}</strong>
        </div>
        {% endif %}
        
        {% if notion_url %}
        <p style="margin-top: 40px;">
            <a href="{{ notion_url }}" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                View in Notion Dashboard →
            </a>
        </p>
        {% endif %}
    </div>
    
    <div class="footer">
        <p>This is an automated alert from the TAC News Aggregation System.</p>
        <p>You received this because time-sensitive content was detected in today's collection.</p>
    </div>
</body>
</html>
"""

def send_email_alert(
    subject: str,
    html_content: str,
    recipients: Optional[List[str]] = None
) -> bool:
    """
    Send an email alert using configured SMTP settings.
    
    Args:
        subject: Email subject line
        html_content: HTML formatted email body
        recipients: Optional list of recipients (uses config if not provided)
        
    Returns:
        True if email sent successfully, False otherwise
    """
    if not EMAIL_ENABLED:
        logger.info("Email alerts are disabled")
        return False
    
    # Validate configuration
    if not all([SMTP_HOST, SMTP_USER, SMTP_PASSWORD, EMAIL_FROM_ADDRESS]):
        logger.error("Email configuration incomplete. Please check SMTP settings.")
        return False
    
    # Use provided recipients or fall back to config
    if not recipients:
        if not EMAIL_RECIPIENTS:
            logger.error("No email recipients configured")
            return False
        recipients = [r.strip() for r in EMAIL_RECIPIENTS.split(',')]
    
    try:
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = EMAIL_FROM_ADDRESS
        msg['To'] = ', '.join(recipients)
        
        # Add HTML content
        html_part = MIMEText(html_content, 'html')
        msg.attach(html_part)
        
        # Send email
        with smtplib.SMTP(SMTP_HOST, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_USER, SMTP_PASSWORD)
            server.send_message(msg)
        
        logger.info(f"Email alert sent successfully to {len(recipients)} recipients")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send email alert: {e}")
        return False

# DEPRECATED: check_time_sensitive_items has been replaced by:
# 1. core.time_sensitive_service.get_time_sensitive_items_for_period() for querying
# 2. send_time_sensitive_email() for email rendering
# The old function has been removed to ensure single source of truth


def send_time_sensitive_email(
    email_items: List[Tuple[Union[NewsItem, TweetItem], str]],
    total_count: int,
    excluded_count: int,
    weekly_subpage_id: Optional[str] = None,
    time_sensitive_block_id: Optional[str] = None
) -> bool:
    """
    Send email notification for time-sensitive items.
    This is now a simple renderer - no filtering logic.
    
    Args:
        email_items: Items to include in the email body
        total_count: Total number of time-sensitive items found
        excluded_count: Number excluded from email
        weekly_subpage_id: The current weekly Notion subpage ID
        time_sensitive_block_id: The block ID of the time-sensitive section
        
    Returns:
        True if email sent successfully
    """
    if not email_items:
        logger.info("No time-sensitive items to email")
        return False
        
    logger.info(f"Sending email for {len(email_items)} items (total: {total_count})")
    
    # Prepare limit warning if needed
    limit_warning = ""
    if excluded_count > 0:
        limit_warning = f"Note: {excluded_count} additional time-sensitive items were detected but excluded from this email to prevent spam."
    
    # Build Notion URL
    notion_url = None
    if weekly_subpage_id:
        page_id_for_url = weekly_subpage_id.replace('-', '')
        notion_url = f"https://www.notion.so/{page_id_for_url}"
        if time_sensitive_block_id:
            block_anchor = time_sensitive_block_id.replace('-', '')
            notion_url += f"#{block_anchor}"
        logger.info(f"Built Notion URL: {notion_url}")
    elif NOTION_PAGE_ID:
        page_id_for_url = NOTION_PAGE_ID.replace('-', '')
        notion_url = f"https://www.notion.so/{page_id_for_url}"
        logger.info(f"Using fallback Notion URL: {notion_url}")
    
    # Render email
    template = Template(EMAIL_TEMPLATE)
    html_content = template.render(
        date_str=now_utc().strftime('%B %d, %Y'),
        item_count=total_count,  # Show total count in header
        items=email_items,       # But only include limited items in body
        notion_url=notion_url,
        limit_warning=limit_warning
    )
    
    # Send email
    subject = f"🚨 TAC News Alert: {total_count} Time-Sensitive Items"
    return send_email_alert(subject, html_content)

