import os
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables from .env file
load_dotenv(override=True)

# Base directory
BASE_DIR = Path(__file__).resolve().parent

# Data directory
DATA_DIR = BASE_DIR / "data"

# Database configuration moved to PostgreSQL
# Use SUPABASE_DB_URL environment variable instead

# Ensure data directory exists
DATA_DIR.mkdir(exist_ok=True)

# API Keys
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")

# Telegram API credentials
TELEGRAM_API_ID = int(os.getenv("TELEGRAM_API_ID", 0))
TELEGRAM_API_HASH = os.getenv("TELEGRAM_API_HASH", "")

# Telegram channels to scrape (channel usernames without '@')
TELEGRAM_CHANNELS = [
    "@RWAxyzNewswire"  # RWA.xyz Newswire channel
]

# Number of recent messages to fetch per channel
TELEGRAM_MESSAGE_LIMIT = 50

# X (Twitter) API credentials
X_BEARER_TOKEN = os.getenv("X_BEARER_TOKEN")
X_API_KEY = os.getenv("X_API_KEY")
X_API_SECRET = os.getenv("X_API_SECRET")
X_ACCESS_TOKEN = os.getenv("X_ACCESS_TOKEN")
X_ACCESS_TOKEN_SECRET = os.getenv("X_ACCESS_TOKEN_SECRET")

# X List configuration
X_LIST_ID = "1906777783583584471"  # List ID containing RWA/tokenized assets accounts
X_TWEET_LIMIT = 100  # Maximum number of tweets to fetch per run

# Google News RSS search parameters
KEYWORDS = ["tokenized assets", "real world assets", "RWA"]

# Google Form integration settings
# Form responses spreadsheet: https://docs.google.com/spreadsheets/d/15O_GGK_gTjOyuwRSBHTWEgqM1jRXQ5jSkCYFdxw9ukA/edit?gid=*********
GOOGLE_FORM_SHEET_ID = os.getenv("GOOGLE_FORM_SHEET_ID")  # ID of the Google Sheet containing form responses
GOOGLE_CREDENTIALS_FILE = os.getenv("GOOGLE_CREDENTIALS_FILE", BASE_DIR / "credentials.json")  # Path to service account credentials

# Notion integration settings
NOTION_API_TOKEN = os.getenv("NOTION_API_TOKEN", "")  # Notion API token
NOTION_PAGE_ID = os.getenv("NOTION_PAGE_ID", "")  # ID of the Notion page for newsletter output

# LLM Settings
SAMPLE_SIZE = 5  # Number of articles to check with LLM
LLM_MODEL = "claude-sonnet-4-********"  # Cost-effective model

# Email Notification Settings
EMAIL_ENABLED = os.getenv("EMAIL_ENABLED", "").lower() == "true"
SMTP_HOST = os.getenv("SMTP_HOST", "")
SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
SMTP_USER = os.getenv("SMTP_USER", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")
EMAIL_FROM_ADDRESS = os.getenv("EMAIL_FROM_ADDRESS", "")
EMAIL_RECIPIENTS = os.getenv("EMAIL_RECIPIENTS", "")  # Comma-separated list of email addresses