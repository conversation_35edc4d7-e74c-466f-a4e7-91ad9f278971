"""
Centralized service for time-sensitive item queries.
This is the single source of truth for what constitutes time-sensitive content.
"""

from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Union, Optional
from core.models import NewsItem, TweetItem
from core.enums import RelevanceStatus
from core.storage.news_items_pg import get_recent_news_items
from core.storage.tweet_items_pg import get_recent_tweets
from core.utils.logging import get_logger
from core.timezone_config import ensure_utc, now_utc

logger = get_logger(__name__)

# Configuration
MAX_EMAIL_ITEMS = 10  # Spam prevention limit for emails
DEFAULT_LOOKBACK_HOURS = 24  # How far back to look for time-sensitive items


def get_time_sensitive_items_for_period(
    hours: int = DEFAULT_LOOKBACK_HOURS,
    limit_for_email: bool = False
) -> Dict[str, Union[List[Tuple[Union[NewsItem, TweetItem], str]], int]]:
    """
    Get time-sensitive items from the database for a given period.
    This is THE authoritative query for time-sensitive content.
    
    Args:
        hours: Number of hours to look back (default: 24)
        limit_for_email: Whether to apply email spam limit
        
    Returns:
        Dictionary with:
        - 'all_items': List of all time-sensitive items with reasons
        - 'email_items': Limited list for email (if limit_for_email=True)
        - 'total_count': Total number of time-sensitive items
        - 'excluded_count': Number excluded from email
    """
    cutoff = now_utc() - timedelta(hours=hours)
    logger.info(f"Querying time-sensitive items since {cutoff.isoformat()}")
    
    # Get items from database - use days parameter
    days = max(1, hours // 24)  # At least 1 day
    if hours % 24 > 0:
        days += 1  # Round up if partial day
    
    news_items = get_recent_news_items(days=days, relevance=RelevanceStatus.RELEVANT)
    tweets = get_recent_tweets(days=days)
    
    # Combine and filter
    all_items = []
    
    # Filter news by publication date and time-sensitivity
    for item in news_items:
        if (hasattr(item, 'published_at') and item.published_at):
            # Ensure timezone-aware comparison
            pub_time = ensure_utc(item.published_at)
            
            if (pub_time > cutoff and
                hasattr(item, 'is_time_sensitive') and item.is_time_sensitive):
                reason = getattr(item, 'timeliness_reason', 'Marked as time-sensitive')
                all_items.append((item, reason))
    
    # Filter tweets by creation date, relevance, and time-sensitivity
    for tweet in tweets:
        if (hasattr(tweet, 'created_at') and tweet.created_at):
            # Ensure timezone-aware comparison
            created_time = ensure_utc(tweet.created_at)
            
            if (created_time > cutoff and
                tweet.relevance == RelevanceStatus.RELEVANT and
                hasattr(tweet, 'is_time_sensitive') and tweet.is_time_sensitive):
                reason = getattr(tweet, 'timeliness_reason', 'Marked as time-sensitive')
                all_items.append((tweet, reason))
    
    # Sort by date (newest first) with stable secondary sort by ID
    all_items.sort(
        key=lambda x: (
            getattr(x[0], 'published_at', getattr(x[0], 'created_at', now_utc())),
            getattr(x[0], 'id', 0)
        ),
        reverse=True
    )
    
    result = {
        'all_items': all_items,
        'email_items': all_items,  # Default to all
        'total_count': len(all_items),
        'excluded_count': 0
    }
    
    # Apply email limit if requested
    if limit_for_email and len(all_items) > MAX_EMAIL_ITEMS:
        result['email_items'] = all_items[:MAX_EMAIL_ITEMS]
        result['excluded_count'] = len(all_items) - MAX_EMAIL_ITEMS
        logger.info(f"Limited email items from {len(all_items)} to {MAX_EMAIL_ITEMS}, excluded {result['excluded_count']}")
    
    logger.info(f"Found {result['total_count']} time-sensitive items in last {hours} hours")
    return result