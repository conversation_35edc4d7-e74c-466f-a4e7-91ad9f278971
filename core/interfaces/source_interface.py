from abc import ABC, abstractmethod
from typing import List, Union, Optional, Dict, Any
from core.models import NewsItem, TweetItem

class DataSource(ABC):
    @abstractmethod
    def get_source_name(self) -> str:
        """Returns a human-readable name for the source."""
        pass

    @abstractmethod
    def fetch_items(self, config_params: Optional[Dict[str, Any]] = None) -> List[Union[NewsItem, TweetItem]]:
        """Fetches data and returns a list of NewsItem or TweetItem objects."""
        pass
