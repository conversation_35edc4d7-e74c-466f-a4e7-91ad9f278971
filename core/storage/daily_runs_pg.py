"""
Daily run tracking for the TAC News Aggregation System.
PostgreSQL version using pg_utils.

This module handles logging and tracking of daily collection runs.
"""

from datetime import datetime, timedelta, timezone
from typing import Optional, List, Dict
from core.utils.logging import get_logger
from core.timezone_config import now_utc
from core.storage.pg_utils import (
    get_connection,
    execute_query,
    fetch_all,
    fetch_one,
    insert_returning,
    table_exists
)

logger = get_logger(__name__)

def init_daily_runs():
    """
    Initialize the daily runs tracking table.
    
    Note: In PostgreSQL, we assume the table was created during migration.
    This function now just verifies the table exists.
    """
    try:
        if table_exists('daily_runs'):
            logger.info("Daily runs table verified")
            return True
        else:
            # Create the table if it doesn't exist
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                CREATE TABLE daily_runs (
                    id SERIAL PRIMARY KEY,
                    run_timestamp TIMESTAMP NOT NULL,
                    run_type TEXT NOT NULL,
                    status TEXT NOT NULL,
                    items_fetched INTEGER DEFAULT 0,
                    items_published INTEGER DEFAULT 0,
                    error_message TEXT,
                    notion_page_id TEXT,
                    completed_at TIMESTAMP
                )
                ''')
                
                # Create indexes
                cursor.execute('CREATE INDEX idx_run_timestamp ON daily_runs(run_timestamp DESC)')
                cursor.execute('CREATE INDEX idx_run_type_status ON daily_runs(run_type, status)')
                cursor.execute('CREATE INDEX idx_run_status ON daily_runs(status)')
                
            logger.info("Daily runs table created")
            return True
    except Exception as e:
        logger.error(f"Error initializing daily runs table: {e}")
        return False

def start_run(run_type: str = 'daily') -> Optional[int]:
    """
    Record the start of a new run.
    
    Args:
        run_type: Type of run ('daily', 'weekly', 'manual')
        
    Returns:
        The ID of the new run record, or None if failed
    """
    try:
        timestamp = now_utc()
        run_id = insert_returning(
            "INSERT INTO daily_runs (run_timestamp, run_type, status) VALUES (%s, %s, 'started')",
            (timestamp, run_type),
            returning_column='id'
        )
        
        if run_id:
            logger.info(f"Started {run_type} run with ID {run_id}")
        return run_id
    except Exception as e:
        logger.error(f"Error starting run: {e}")
        return None

def complete_run(run_id: int, items_fetched: int = 0, items_published: int = 0, 
                 notion_page_id: Optional[str] = None) -> bool:
    """
    Mark a run as completed.
    
    Args:
        run_id: ID of the run to complete
        items_fetched: Number of items fetched during the run
        items_published: Number of items published to Notion
        notion_page_id: ID of the Notion page if items were published
        
    Returns:
        True if successful, False otherwise
    """
    try:
        completed_at = now_utc()
        affected = execute_query("""
            UPDATE daily_runs 
            SET status = 'completed',
                items_fetched = %s,
                items_published = %s,
                notion_page_id = %s,
                completed_at = %s
            WHERE id = %s
        """, (items_fetched, items_published, notion_page_id, completed_at, run_id))
        
        if affected > 0:
            logger.info(f"Completed run {run_id}: fetched={items_fetched}, published={items_published}")
            return True
        else:
            return False
    except Exception as e:
        logger.error(f"Error completing run: {e}")
        return False

def fail_run(run_id: int, error_message: str) -> bool:
    """
    Mark a run as failed.
    
    Args:
        run_id: ID of the run to mark as failed
        error_message: Error message describing the failure
        
    Returns:
        True if successful, False otherwise
    """
    try:
        completed_at = now_utc()
        affected = execute_query("""
            UPDATE daily_runs 
            SET status = 'failed',
                error_message = %s,
                completed_at = %s
            WHERE id = %s
        """, (error_message, completed_at, run_id))
        
        if affected > 0:
            logger.info(f"Marked run {run_id} as failed: {error_message}")
            return True
        else:
            return False
    except Exception as e:
        logger.error(f"Error marking run as failed: {e}")
        return False

def get_recent_runs(limit: int = 10) -> List[Dict]:
    """
    Get the most recent runs.
    
    Args:
        limit: Maximum number of runs to return
        
    Returns:
        List of run records as dictionaries
    """
    try:
        rows = fetch_all("""
            SELECT id, run_timestamp, run_type, status, items_fetched, 
                   items_published, error_message, notion_page_id, completed_at
            FROM daily_runs
            ORDER BY run_timestamp DESC
            LIMIT %s
        """, (limit,))
        
        return rows
    except Exception as e:
        logger.error(f"Error getting recent runs: {e}")
        return []

def get_last_successful_run(run_type: Optional[str] = None) -> Optional[Dict]:
    """
    Get the last successful run.
    
    Args:
        run_type: Filter by run type if specified
        
    Returns:
        Run record as dictionary, or None if no successful runs
    """
    try:
        if run_type:
            row = fetch_one("""
                SELECT id, run_timestamp, run_type, status, items_fetched, 
                       items_published, error_message, notion_page_id, completed_at
                FROM daily_runs
                WHERE status = 'completed' AND run_type = %s
                ORDER BY run_timestamp DESC
                LIMIT 1
            """, (run_type,))
        else:
            row = fetch_one("""
                SELECT id, run_timestamp, run_type, status, items_fetched, 
                       items_published, error_message, notion_page_id, completed_at
                FROM daily_runs
                WHERE status = 'completed'
                ORDER BY run_timestamp DESC
                LIMIT 1
            """)
        
        return row
    except Exception as e:
        logger.error(f"Error getting last successful run: {e}")
        return None


def get_last_successful_run_timestamp() -> datetime:
    """
    Get the start time of the last successful run.
    
    If no successful runs exist, returns 7 days ago as a default.
    
    Returns:
        datetime: The timestamp of the last successful run or 7 days ago
    """
    try:
        # Get the run_timestamp (which is the start time) of the last successful run
        row = fetch_one("""
            SELECT run_timestamp
            FROM daily_runs
            WHERE status = 'completed'
            ORDER BY run_timestamp DESC
            LIMIT 1
        """)
        
        if row:
            # PostgreSQL returns datetime objects directly
            timestamp = row['run_timestamp']
            if timestamp.tzinfo is None:
                timestamp = timestamp.replace(tzinfo=timezone.utc)
            else:
                timestamp = timestamp.astimezone(timezone.utc)
            
            logger.info(f"Found last successful run at: {timestamp.isoformat()}")
            return timestamp
        else:
            # No successful runs found, default to 7 days ago
            default_timestamp = datetime.now(timezone.utc) - timedelta(days=7)
            logger.info(f"No successful runs found, using default timestamp: {default_timestamp.isoformat()}")
            return default_timestamp
            
    except Exception as e:
        logger.error(f"Error getting last successful run timestamp: {e}")
        # On error, default to 7 days ago
        default_timestamp = datetime.now(timezone.utc) - timedelta(days=7)
        logger.info(f"Error occurred, using default timestamp: {default_timestamp.isoformat()}")
        return default_timestamp