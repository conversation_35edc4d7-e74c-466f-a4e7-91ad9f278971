"""
Publication state management for the TAC Daily News Aggregation System.
PostgreSQL version using pg_utils.

This module handles storing and retrieving the timestamp of the last newsletter publication,
as well as tracking weekly page boundaries for organizing daily content into weekly Notion pages.
"""

from datetime import datetime, timedelta
import pytz
from core.utils.logging import get_logger
from core.timezone_config import now_utc
from core.storage.pg_utils import (
    get_connection,
    execute_query,
    fetch_one,
    table_exists
)

logger = get_logger(__name__)

# Pacific timezone for all week calculations
PACIFIC_TZ = pytz.timezone('US/Pacific')

def init_publication_state():
    """
    Initialize the publication state storage by creating the necessary table if it doesn't exist.
    
    Note: In PostgreSQL, we assume the table was created during migration.
    This function now just verifies the table exists.
    """
    try:
        if table_exists('publication_state'):
            logger.info("Publication state table verified")
            return True
        else:
            logger.error("Publication state table does not exist! Run migration first.")
            return False
    except Exception as e:
        logger.error(f"Error verifying publication state table: {e}")
        return False


def is_new_week():
    """
    Check if we've crossed into a new week (Thursday midnight Pacific Time cutoff).
    
    Used by the daily collection system to determine when to create a new weekly
    Notion page for organizing daily content.
    
    Returns:
        bool: True if it's a new week and we need to create a new page, False otherwise
    """
    try:
        # Get the stored week start date
        result = fetch_one("SELECT week_start_date FROM publication_state WHERE id = 1")
        
        if not result or not result.get('week_start_date'):
            # No week start date stored, this is a new week
            logger.info("No week start date found, treating as new week")
            return True
        
        # Parse the stored week start date
        stored_week_start = datetime.fromisoformat(result['week_start_date'])
        
        # Make stored date timezone-aware if it isn't already
        if stored_week_start.tzinfo is None:
            stored_week_start = PACIFIC_TZ.localize(stored_week_start)
        
        # Calculate current week start (Thursday at midnight Pacific Time)
        now = datetime.now(PACIFIC_TZ)
        days_since_thursday = (now.weekday() - 3) % 7
        current_week_start = (now - timedelta(days=days_since_thursday)).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        
        # Check if we're in a different week
        is_new = current_week_start > stored_week_start
        
        if is_new:
            logger.info(f"New week detected. Stored: {stored_week_start}, Current: {current_week_start}")
        
        return is_new
        
    except Exception as e:
        logger.error(f"Error checking if new week: {e}")
        # Safer to assume it's not a new week to avoid creating duplicate pages
        return False

def get_current_weekly_page_id():
    """
    Get the ID of the current weekly Notion page.
    
    Returns:
        str or None: The Notion page ID for the current week, or None if not set
    """
    try:
        result = fetch_one("SELECT current_weekly_page_id FROM publication_state WHERE id = 1")
        
        if result:
            return result.get('current_weekly_page_id')  # Can be None if not set
        else:
            logger.info("No publication state record found")
            return None
    except Exception as e:
        logger.error(f"Error retrieving current weekly page ID: {e}")
        return None

def update_weekly_page_id(page_id, week_start_date=None):
    """
    Update the current weekly Notion page ID and optionally the week start date.
    
    Args:
        page_id (str): The Notion page ID for the current week
        week_start_date (datetime, optional): The start date of the current week. 
                                            Defaults to current week's start.
        
    Returns:
        bool: True if the update was successful, False otherwise.
    """
    if week_start_date is None:
        # Calculate current week start (Thursday at midnight Pacific Time)
        now = datetime.now(PACIFIC_TZ)
        days_since_thursday = (now.weekday() - 3) % 7
        week_start_date = (now - timedelta(days=days_since_thursday)).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
    
    try:
        # Convert week_start_date to ISO format string for storage
        week_start_str = week_start_date.isoformat()
        
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # PostgreSQL UPSERT using ON CONFLICT
            cursor.execute("""
                INSERT INTO publication_state 
                    (id, last_published_at, current_weekly_page_id, week_start_date) 
                VALUES 
                    (1, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    current_weekly_page_id = EXCLUDED.current_weekly_page_id,
                    week_start_date = EXCLUDED.week_start_date
            """, (now_utc(), page_id, week_start_str))
        
        logger.info(f"Updated weekly page ID to {page_id} for week starting {week_start_str}")
        return True
    except Exception as e:
        logger.error(f"Error updating weekly page ID: {e}")
        raise  # Let the error propagate

def update_publication_timestamp(timestamp=None):
    """
    Update the timestamp of the last daily collection run.
    
    Called after successfully publishing daily content to Notion to track
    when the system last ran.
    
    Args:
        timestamp (datetime, optional): The timestamp to store. Defaults to current time.
        
    Returns:
        bool: True if the update was successful, False otherwise.
    """
    if timestamp is None:
        timestamp = now_utc()
    
    try:
        # Convert timestamp to ISO format string for storage
        timestamp_str = timestamp.isoformat()
        
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # PostgreSQL UPSERT
            cursor.execute("""
                INSERT INTO publication_state 
                    (id, last_published_at) 
                VALUES 
                    (1, %s)
                ON CONFLICT (id) DO UPDATE SET
                    last_published_at = EXCLUDED.last_published_at
            """, (timestamp,))  # Note: PostgreSQL can handle datetime directly
        
        logger.info(f"Updated last publication timestamp to {timestamp_str}")
        return True
    except Exception as e:
        logger.error(f"Error updating publication timestamp: {e}")
        raise  # Let the error propagate

def get_last_publication_timestamp():
    """
    Get the timestamp of the last publication.
    
    Returns:
        datetime or None: The last publication timestamp, or None if not set
    """
    try:
        result = fetch_one("SELECT last_published_at FROM publication_state WHERE id = 1")
        
        if result and result.get('last_published_at'):
            # PostgreSQL returns datetime objects directly
            return result['last_published_at']
        else:
            logger.info("No publication timestamp found")
            return None
    except Exception as e:
        logger.error(f"Error retrieving last publication timestamp: {e}")
        return None