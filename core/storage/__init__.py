"""
Storage module for the TAC Weekly News Aggregation System.

This module provides functions for storing and retrieving data from the database.
It is organized into several submodules:
- db_init: Database initialization functions
- news_items: News item storage and retrieval
- tweet_items: Tweet item storage and retrieval
- x_collection: X collection state management
- daily_runs: Daily run tracking and logging

All functions are re-exported at the module level for backward compatibility.
"""

# Import functions from submodules
from .db_init_pg import init_db
from .news_items_pg import (
    save_news_items,
    update_item_relevance,
    get_recent_items,
    get_recent_news_items,
    get_news_items_since,
)
from .tweet_items_pg import (
    save_tweet_items,
    get_recent_tweets,
    update_tweet_relevance,
    update_tweet_timeliness,
    get_unevaluated_tweet_items,
    update_tweet_newsletter_status,
)
from .x_collection_pg import (
    initialize_collection_state,
    update_collection_state,
    get_collection_state,
    get_active_collection,
    clear_collection_state,
)
from .daily_runs_pg import (
    init_daily_runs,
    start_run,
    complete_run,
    fail_run,
    get_recent_runs,
    get_last_successful_run,
)

# Export functions
__all__ = [
    'init_db',
    'save_news_items',
    'update_item_relevance',
    'get_recent_items',
    'get_recent_news_items',
    'get_news_items_since',
    'save_tweet_items',
    'get_recent_tweets',
    'update_tweet_relevance',
    'update_tweet_timeliness',
    'get_unevaluated_tweet_items',
    'update_tweet_newsletter_status',
    'initialize_collection_state',
    'update_collection_state',
    'get_collection_state',
    'get_active_collection',
    'clear_collection_state',
    'init_daily_runs',
    'start_run',
    'complete_run',
    'fail_run',
    'get_recent_runs',
    'get_last_successful_run',
]
