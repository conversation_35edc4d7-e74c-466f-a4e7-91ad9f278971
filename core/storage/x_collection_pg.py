"""
X collection state management functions for the TAC Weekly News Aggregation System.
PostgreSQL version using pg_utils.
"""

import uuid
from datetime import datetime
from typing import Optional, Dict
from core.utils.logging import get_logger
from core.timezone_config import now_utc
from core.storage.pg_utils import (
    get_connection,
    execute_query,
    fetch_one,
    insert_returning
)

logger = get_logger(__name__)

def initialize_collection_state(start_time: datetime, end_time: datetime) -> Optional[str]:
    """
    Initialize a new collection state entry for X tweet collection.

    Args:
        start_time: Start time of the collection window
        end_time: End time of the collection window

    Returns:
        Collection ID if successful, None otherwise
    """
    try:
        # Generate a unique collection ID
        collection_id = str(uuid.uuid4())
        now = now_utc()

        # Insert new collection state
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
            INSERT INTO x_collection_state
            (collection_id, start_time, end_time, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s)
            ''', (
                collection_id,
                start_time,  # PostgreSQL handles datetime objects
                end_time,
                'in_progress',
                now,
                now
            ))

        logger.info(f"Initialized collection state with ID: {collection_id}")
        return collection_id

    except Exception as e:
        logger.error(f"Error initializing collection state: {e}")
        return None


def update_collection_state(collection_id: str, **kwargs) -> bool:
    """
    Update an existing collection state entry.

    Args:
        collection_id: ID of the collection to update
        **kwargs: Fields to update (oldest_tweet_id, newest_tweet_id, pagination_token,
                  status, last_request_time, requests_made, tweets_collected, notes)

    Returns:
        True if successful, False otherwise
    """
    if not kwargs:
        logger.warning("No update parameters provided for collection state update")
        return False

    try:
        # Build the update query dynamically based on provided kwargs
        set_clauses = []
        params = []

        for key, value in kwargs.items():
            if key in ['oldest_tweet_id', 'newest_tweet_id', 'pagination_token', 'status',
                       'last_request_time', 'requests_made', 'tweets_collected', 'notes']:
                set_clauses.append(f"{key} = %s")
                params.append(value)

        # Always update the updated_at timestamp
        set_clauses.append("updated_at = %s")
        params.append(now_utc())

        # Add collection_id to params
        params.append(collection_id)

        # Execute the update
        query = f"UPDATE x_collection_state SET {', '.join(set_clauses)} WHERE collection_id = %s"
        affected = execute_query(query, tuple(params))

        if affected > 0:
            logger.debug(f"Updated collection state for ID: {collection_id}")
            return True
        else:
            logger.warning(f"No collection state found with ID: {collection_id}")
            return False

    except Exception as e:
        logger.error(f"Error updating collection state: {e}")
        return False


def get_collection_state(collection_id: Optional[str] = None, status: Optional[str] = None) -> Optional[Dict]:
    """
    Get collection state information.

    Args:
        collection_id: Optional ID of a specific collection to retrieve
        status: Optional status to filter by (e.g., 'in_progress')

    Returns:
        Dictionary with collection state or None if not found
    """
    try:
        query = "SELECT * FROM x_collection_state"
        params = []
        where_clauses = []

        if collection_id:
            where_clauses.append("collection_id = %s")
            params.append(collection_id)

        if status:
            where_clauses.append("status = %s")
            params.append(status)

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        # Order by most recently updated first
        query += " ORDER BY updated_at DESC LIMIT 1"

        row = fetch_one(query, tuple(params) if params else None)

        if row:
            # Already a dictionary from pg_utils
            return dict(row)
        else:
            return None

    except Exception as e:
        logger.error(f"Error retrieving collection state: {e}")
        return None


def get_active_collection() -> Optional[Dict]:
    """
    Get the currently active collection (status = 'in_progress').

    Returns:
        Dictionary with collection state or None if no active collection
    """
    return get_collection_state(status='in_progress')


def clear_collection_state(collection_id: str) -> bool:
    """
    Clear/delete a collection state entry.

    Args:
        collection_id: ID of the collection to delete

    Returns:
        True if successful, False otherwise
    """
    try:
        affected = execute_query("DELETE FROM x_collection_state WHERE collection_id = %s", (collection_id,))

        if affected > 0:
            logger.info(f"Cleared collection state with ID: {collection_id}")
            return True
        else:
            logger.warning(f"No collection state found with ID: {collection_id}")
            return False

    except Exception as e:
        logger.error(f"Error clearing collection state: {e}")
        return False