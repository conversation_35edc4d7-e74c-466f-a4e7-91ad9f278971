"""
PostgreSQL utility functions for database operations.
Provides a compatibility layer to minimize changes in existing storage modules.
"""
import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager
import psycopg2
import psycopg2.extras
from datetime import datetime

logger = logging.getLogger(__name__)

# Get database URL from environment
DATABASE_URL = os.getenv("SUPABASE_DB_URL") or os.getenv("DATABASE_URL")

@contextmanager
def get_connection():
    """
    Get a PostgreSQL database connection.
    
    Yields:
        psycopg2 connection object
    """
    if not DATABASE_URL:
        raise ValueError("No database URL found. Set SUPABASE_DB_URL or DATABASE_URL environment variable.")
    
    conn = None
    try:
        conn = psycopg2.connect(DATABASE_URL)
        # Use RealDictCursor to get results as dictionaries
        conn.cursor_factory = psycopg2.extras.RealDictCursor
        
        # Set session timezone to UTC to ensure all operations use UTC
        with conn.cursor() as cursor:
            cursor.execute("SET TIME ZONE 'UTC'")
        
        yield conn
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Database error: {e}")
        raise
    finally:
        if conn:
            conn.close()

def execute_query(query: str, params: Optional[Tuple] = None) -> int:
    """
    Execute a query (INSERT, UPDATE, DELETE) and return affected rows.
    
    Args:
        query: SQL query with %s placeholders
        params: Tuple of parameters
        
    Returns:
        Number of affected rows
    """
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)
        return cursor.rowcount

def fetch_all(query: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
    """
    Execute a SELECT query and return all results as list of dicts.
    
    Args:
        query: SQL query with %s placeholders
        params: Tuple of parameters
        
    Returns:
        List of dictionaries representing rows
    """
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)
        return cursor.fetchall()

def fetch_one(query: str, params: Optional[Tuple] = None) -> Optional[Dict[str, Any]]:
    """
    Execute a SELECT query and return first result as dict.
    
    Args:
        query: SQL query with %s placeholders
        params: Tuple of parameters
        
    Returns:
        Dictionary representing row or None
    """
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)
        return cursor.fetchone()

def execute_many(query: str, params_list: List[Tuple]) -> int:
    """
    Execute a query multiple times with different parameters.
    
    Args:
        query: SQL query with %s placeholders
        params_list: List of parameter tuples
        
    Returns:
        Total number of affected rows
    """
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.executemany(query, params_list)
        return cursor.rowcount

def insert_returning(query: str, params: Optional[Tuple] = None, returning_column: str = "id") -> Any:
    """
    Execute INSERT query with RETURNING clause.
    
    Args:
        query: INSERT query (RETURNING clause will be added)
        params: Tuple of parameters
        returning_column: Column to return (default: "id")
        
    Returns:
        Value of the returning column
    """
    # Add RETURNING clause if not present
    if "RETURNING" not in query.upper():
        query = f"{query} RETURNING {returning_column}"
    
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)
        result = cursor.fetchone()
        return result[returning_column] if result else None

def table_exists(table_name: str) -> bool:
    """
    Check if a table exists in the database.
    
    Args:
        table_name: Name of the table
        
    Returns:
        True if table exists, False otherwise
    """
    query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = %s
        )
    """
    result = fetch_one(query, (table_name,))
    return result['exists'] if result else False

def adapt_sqlite_query(query: str) -> str:
    """
    Adapt common SQLite syntax to PostgreSQL.
    
    Args:
        query: SQLite-style query
        
    Returns:
        PostgreSQL-compatible query
    """
    # Replace ? placeholders with %s
    query = query.replace("?", "%s")
    
    # Common SQLite to PostgreSQL conversions
    replacements = {
        "INSERT OR REPLACE": "INSERT ... ON CONFLICT DO UPDATE",
        "INSERT OR IGNORE": "INSERT ... ON CONFLICT DO NOTHING",
        "datetime('now')": "CURRENT_TIMESTAMP",
        "date('now')": "CURRENT_DATE",
        "strftime('%s', 'now')": "EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)",
        "AUTOINCREMENT": "SERIAL",
    }
    
    for sqlite_syntax, pg_syntax in replacements.items():
        if sqlite_syntax in query:
            # Log the conversion for debugging
            logger.warning(f"Query adaptation needed: {sqlite_syntax} -> {pg_syntax}")
            logger.warning(f"Original query: {query}")
            logger.warning("Please update the query to use PostgreSQL syntax directly")
    
    return query

def format_timestamp(dt: Optional[datetime]) -> Optional[str]:
    """
    Format datetime for PostgreSQL.
    
    Args:
        dt: datetime object or None
        
    Returns:
        ISO format string or None
    """
    return dt.isoformat() if dt else None

def row_to_dict(row: Any) -> Dict[str, Any]:
    """
    Convert a database row to dictionary.
    Works with both RealDictRow (already a dict) and regular tuples.
    
    Args:
        row: Database row
        
    Returns:
        Dictionary representation
    """
    if isinstance(row, dict):
        return dict(row)  # Create a copy
    elif hasattr(row, '_asdict'):
        return row._asdict()
    elif hasattr(row, 'keys'):
        return dict(row)
    else:
        # Fallback for tuple results
        logger.warning("Row is not a dictionary, returning as-is")
        return row

# Compatibility helpers for minimal code changes

def init_db():
    """
    Compatibility function for database initialization.
    The actual schema should be managed by migrations.
    """
    logger.info("init_db() called - schema should be managed by migrations")
    return True