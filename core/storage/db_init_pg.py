"""
Database initialization functions for the TAC Weekly News Aggregation System.
PostgreSQL version using pg_utils.

Note: In PostgreSQL, we assume tables were created during migration.
This module verifies tables exist and can create missing indexes.
"""

from core.utils.logging import get_logger
from core.storage.pg_utils import get_connection, table_exists
from .daily_runs_pg import init_daily_runs

logger = get_logger(__name__)

def init_db():
    """Initialize/verify the database with the necessary tables."""
    
    # List of required tables
    required_tables = [
        'news_items',
        'tweet_items', 
        'x_collection_state',
        'publication_state',
        'daily_runs'
    ]
    
    # Check if all tables exist
    missing_tables = []
    for table in required_tables:
        if not table_exists(table):
            missing_tables.append(table)
    
    if missing_tables:
        logger.error(f"Missing required tables: {missing_tables}")
        logger.error("Please run the migration script first: python migrate_to_supabase.py")
        return False
    
    # Verify/create indexes (PostgreSQL will skip if they already exist)
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # News items indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_items_relevance_fetched_at ON news_items (relevance, fetched_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_items_published_at ON news_items (published_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_news_items_is_time_sensitive ON news_items (is_time_sensitive)')
            
            # Tweet items indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_created_at ON tweet_items(created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_author_id ON tweet_items(author_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_relevance ON tweet_items(relevance)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_source ON tweet_items(source)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_items_relevance_fetched_at ON tweet_items (relevance, fetched_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tweet_items_is_time_sensitive ON tweet_items (is_time_sensitive)')
            
            # X collection state indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_collection_status ON x_collection_state(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_collection_time ON x_collection_state(start_time, end_time)')
            
            # Daily runs indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_run_timestamp ON daily_runs(run_timestamp DESC)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_run_type_status ON daily_runs(run_type, status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_run_status ON daily_runs(status)')
            
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")
        return False
    
    # Initialize the daily runs table (verifies it exists)
    if not init_daily_runs():
        return False
    
    logger.info("Database verified and initialized successfully")
    return True