"""
Tweet item storage and retrieval functions for the TAC Weekly News Aggregation System.
PostgreSQL version using pg_utils.
"""

import json
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any
from core.models import TweetItem
from core.enums import RelevanceStatus
from core.utils.logging import get_logger
from core.storage.pg_utils import (
    get_connection,
    execute_query,
    execute_many,
    fetch_all,
    fetch_one,
    row_to_dict
)
from core.timezone_config import ensure_utc as _ensure_utc_impl, now_utc

logger = get_logger(__name__)


def _ensure_utc(dt: Optional[datetime]) -> Optional[datetime]:
    """Ensures a datetime object is timezone-aware and set to UTC."""
    if dt is None:
        return None
    return _ensure_utc_impl(dt)


def _tweet_item_to_tuple(item: TweetItem) -> tuple:
    """
    Convert a TweetItem to a tuple matching TWEET_COLUMNS order.
    
    Args:
        item: TweetItem to convert
        
    Returns:
        Tuple of values in TWEET_COLUMNS order
    """
    values = []
    for col in TWEET_COLUMNS:
        if col == 'urls':
            values.append(json.dumps([str(url) for url in item.urls]) if item.urls else '[]')
        elif col in ['hashtags', 'mentions']:
            value = getattr(item, col)
            values.append(json.dumps(value) if value else '[]')
        elif col == 'relevance':
            values.append(item.relevance.value if item.relevance else None)
        else:
            values.append(getattr(item, col))
    return tuple(values)


def _process_tweet_rows(rows: List[Dict[str, Any]], operation_name: str) -> List[TweetItem]:
    """
    Process database rows into TweetItem objects with consistent error handling.
    
    Args:
        rows: List of database row dictionaries
        operation_name: Name of the operation for logging context
        
    Returns:
        List of successfully processed TweetItem objects
    """
    items = []
    for row in rows:
        try:
            items.append(_row_to_tweet_item(row))
        except (json.JSONDecodeError, ValueError) as e:
            tweet_id = row.get('tweet_id', 'UNKNOWN')
            logger.error(f"Data conversion failed for tweet_id={tweet_id} in {operation_name}. Error type: {type(e).__name__}")
        except Exception:
            tweet_id = row.get('tweet_id', 'UNKNOWN')
            logger.exception(f"Unexpected error processing tweet_id={tweet_id} in {operation_name}")
    return items


# Define all columns for explicit selection
TWEET_COLUMNS = [
    "tweet_id", "text", "author_id", "author_username", "author_name",
    "created_at", "fetched_at", "is_retweet", "is_quote", "is_reply",
    "has_commentary", "in_reply_to_tweet_id", "in_reply_to_user_id",
    "quoted_tweet_id", "retweeted_tweet_id", "urls", "hashtags", "mentions",
    "like_count", "retweet_count", "reply_count", "quote_count",
    "impression_count", "bookmark_count", "source", "relevance",
    "is_time_sensitive", "timeliness_reason", "included_in_newsletter",
    "processed_at", "notes", "is_duplicate", "duplicate_of_url",
    "duplicate_of_item_type", "duplicate_of_item_id", "deduplication_reason"
]

# Cache commonly used column string
_COLUMNS_STR = ", ".join(TWEET_COLUMNS)


def _row_to_tweet_item(row: Dict[str, Any]) -> TweetItem:
    """
    Convert a database row to a TweetItem object.
    
    Args:
        row: A dictionary containing tweet data
        
    Returns:
        TweetItem object
    """
    # Parse JSON fields
    urls = json.loads(row['urls']) if row['urls'] else []
    hashtags = json.loads(row['hashtags']) if row['hashtags'] else []
    mentions = json.loads(row['mentions']) if row['mentions'] else []

    # PostgreSQL returns datetime objects, ensure UTC
    created_at_dt = _ensure_utc(row['created_at'])
    fetched_at_dt = _ensure_utc(row['fetched_at'])
    processed_at_dt = _ensure_utc(row['processed_at'])

    # Create TweetItem with all fields
    return TweetItem(
        tweet_id=row['tweet_id'],
        text=row['text'],
        created_at=created_at_dt,
        fetched_at=fetched_at_dt,

        # User information
        author_id=row['author_id'],
        author_username=row['author_username'],
        author_name=row['author_name'],

        # Tweet type flags
        is_retweet=row['is_retweet'],
        is_quote=row['is_quote'],
        is_reply=row['is_reply'],
        has_commentary=row['has_commentary'],

        # Related tweet IDs
        in_reply_to_tweet_id=row['in_reply_to_tweet_id'],
        in_reply_to_user_id=row['in_reply_to_user_id'],
        quoted_tweet_id=row['quoted_tweet_id'],
        retweeted_tweet_id=row['retweeted_tweet_id'],

        # URLs and media
        urls=urls,
        hashtags=hashtags,
        mentions=mentions,

        # Individual engagement metrics
        like_count=row['like_count'] or 0,
        retweet_count=row['retweet_count'] or 0,
        reply_count=row['reply_count'] or 0,
        quote_count=row['quote_count'] or 0,
        impression_count=row['impression_count'],
        bookmark_count=row['bookmark_count'],

        # Processing fields
        source=row['source'],
        relevance=RelevanceStatus(row['relevance']) if row['relevance'] else None,
        is_time_sensitive=row.get('is_time_sensitive', False),
        timeliness_reason=row.get('timeliness_reason'),
        included_in_newsletter=row['included_in_newsletter'],
        processed_at=processed_at_dt,
        notes=row['notes'],
        
        # Deduplication fields
        is_duplicate=row.get('is_duplicate', False),
        duplicate_of_url=row.get('duplicate_of_url'),
        duplicate_of_item_type=row.get('duplicate_of_item_type'),
        duplicate_of_item_id=row.get('duplicate_of_item_id'),
        deduplication_reason=row.get('deduplication_reason')
    )

def save_tweet_items(items: List[TweetItem]) -> int:
    """
    Save a list of tweet items to the database using a fast, bulk-insert method.
    Uses ON CONFLICT DO NOTHING to handle duplicates efficiently.
    Returns the number of items successfully saved.
    """
    if not items:
        return 0

    # Prepare all data for the bulk insert
    tweets_to_insert = [_tweet_item_to_tuple(item) for item in items]

    # Build dynamic SQL query based on TWEET_COLUMNS
    placeholders = ", ".join(["%s"] * len(TWEET_COLUMNS))
    
    sql = f'''
        INSERT INTO tweet_items ({_COLUMNS_STR})
        VALUES ({placeholders})
        ON CONFLICT (tweet_id) DO NOTHING
    '''

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # Perform bulk insert
            cursor.executemany(sql, tweets_to_insert)
            
            # Use cursor.rowcount for efficiency
            saved_count = cursor.rowcount
            
        logger.info(f"Attempted to save {len(items)} tweets, successfully saved {saved_count} new tweets.")
        return saved_count
    except Exception as e:
        logger.error(f"Database error during bulk insert: {e}")
        return 0


def get_recent_tweets(limit: int = 10, source: Optional[str] = None, days: Optional[int] = None) -> List[TweetItem]:
    """
    Retrieve the most recent tweets from the database, excluding duplicates.

    Args:
        limit: Maximum number of tweets to retrieve
        source: Optional source to filter by
        days: Optional number of days to look back

    Returns:
        List of TweetItem objects (excludes items marked as duplicates)
    """
    # Build query with explicit columns
    query = f"SELECT {_COLUMNS_STR} FROM tweet_items"
    params = []
    where_clauses = []

    # Add source filter if provided
    if source:
        where_clauses.append("source = %s")
        params.append(source)

    # Add date filter if days is provided
    if days is not None:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        where_clauses.append("created_at >= %s")
        params.append(cutoff_date)

    # Always exclude duplicates
    where_clauses.append("is_duplicate = FALSE")

    # Add WHERE clause if we have any filters
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)

    # Add ORDER BY and LIMIT
    query += " ORDER BY created_at DESC LIMIT %s"
    params.append(limit)

    try:
        rows = fetch_all(query, tuple(params))
        return _process_tweet_rows(rows, "get_recent_tweets")
    except Exception as e:
        logger.error(f"Database error in get_recent_tweets: {e}")
        return []


def update_tweet_relevance(tweet_id: str, relevance: RelevanceStatus) -> bool:
    """Update the relevance of a tweet by its ID."""
    try:
        affected = execute_query(
            "UPDATE tweet_items SET relevance = %s WHERE tweet_id = %s",
            (relevance.value, tweet_id)
        )
        return affected > 0
    except Exception as e:
        logger.error(f"Error updating relevance for tweet {tweet_id}: {e}")
        return False


def update_tweet_timeliness(tweet_id: str, is_time_sensitive: bool, timeliness_reason: Optional[str] = None) -> bool:
    """Update the timeliness information of a tweet by its ID."""
    try:
        affected = execute_query(
            "UPDATE tweet_items SET is_time_sensitive = %s, timeliness_reason = %s WHERE tweet_id = %s",
            (is_time_sensitive, timeliness_reason, tweet_id)
        )
        return affected > 0
    except Exception as e:
        logger.error(f"Error updating timeliness for tweet {tweet_id}: {e}")
        return False


def get_unevaluated_tweet_items(limit: Optional[int] = None) -> List[TweetItem]:
    """Retrieve tweet items that haven't been evaluated for relevance yet, excluding duplicates."""
    # Build query with explicit columns
    query = f"SELECT {_COLUMNS_STR} FROM tweet_items WHERE relevance IS NULL AND is_duplicate = FALSE ORDER BY created_at DESC"
    params = None
    
    if limit:
        query += " LIMIT %s"
        params = (limit,)

    try:
        rows = fetch_all(query, params)
        return _process_tweet_rows(rows, "get_unevaluated_tweet_items")
    except Exception as e:
        logger.error(f"Database error in get_unevaluated_tweet_items: {e}")
        return []


def get_unevaluated_tweet_items_since(timestamp: datetime, limit: Optional[int] = None) -> List[TweetItem]:
    """Retrieve unevaluated tweet items fetched since a specific timestamp, excluding duplicates."""
    # Build query with explicit columns
    query = f"SELECT {_COLUMNS_STR} FROM tweet_items WHERE relevance IS NULL AND fetched_at >= %s AND is_duplicate = FALSE ORDER BY fetched_at ASC"
    params = [timestamp]  # PostgreSQL handles datetime objects
    
    if limit:
        query += " LIMIT %s"
        params.append(limit)
    
    try:
        rows = fetch_all(query, tuple(params))
        return _process_tweet_rows(rows, "get_unevaluated_tweet_items_since")
    except Exception as e:
        logger.error(f"Database error in get_unevaluated_tweet_items_since: {e}")
        return []


def update_tweet_newsletter_status(tweet_id: str, included: bool) -> bool:
    """Update whether a tweet is included in the newsletter."""
    try:
        affected = execute_query(
            "UPDATE tweet_items SET included_in_newsletter = %s, processed_at = %s WHERE tweet_id = %s",
            (included, datetime.now(timezone.utc), tweet_id)
        )
        return affected > 0
    except Exception as e:
        logger.error(f"Error updating newsletter status for tweet {tweet_id}: {e}")
        return False