"""
News item storage and retrieval functions for the TAC Weekly News Aggregation System.
PostgreSQL version using pg_utils.
"""

from datetime import datetime, timedelta, timezone
from typing import List, Optional, Union, Dict, Any
from core.models import NewsItem
from core.enums import RelevanceStatus
from core.utils.logging import get_logger
from core.storage.pg_utils import (
    get_connection,
    execute_query,
    execute_many,
    fetch_all,
    fetch_one,
    row_to_dict
)
from pydantic import HttpUrl

logger = get_logger(__name__)


def _row_to_news_item(row: Dict[str, Any]) -> NewsItem:
    """Convert a database row to a NewsItem object with proper timezone handling."""
    # PostgreSQL returns datetime objects directly, ensure UTC
    published_at_dt = row['published_at']
    if published_at_dt.tzinfo is None:
        published_at_dt = published_at_dt.replace(tzinfo=timezone.utc)
    else:
        published_at_dt = published_at_dt.astimezone(timezone.utc)
    
    # Standardize fetched_at as well
    fetched_at_dt = row['fetched_at']
    if fetched_at_dt.tzinfo is None:
        fetched_at_dt = fetched_at_dt.replace(tzinfo=timezone.utc)
    else:
        fetched_at_dt = fetched_at_dt.astimezone(timezone.utc)

    return NewsItem(
        title=row['title'],
        link=row['link'],
        published_at=published_at_dt,
        summary=row['summary'],
        publisher=row['publisher'],
        source=row['source'],
        fetched_at=fetched_at_dt,
        relevance=RelevanceStatus(row['relevance']) if row['relevance'] else None,
        is_time_sensitive=row.get('is_time_sensitive', False),
        timeliness_reason=row.get('timeliness_reason'),
        is_duplicate=row.get('is_duplicate', False),
        duplicate_of_url=row.get('duplicate_of_url'),
        duplicate_of_item_type=row.get('duplicate_of_item_type'),
        duplicate_of_item_id=row.get('duplicate_of_item_id'),
        deduplication_reason=row.get('deduplication_reason')
    )


def save_news_items(items: List[NewsItem]) -> int:
    """
    Save a list of news items to the database using bulk insert.
    Uses ON CONFLICT DO NOTHING to handle duplicates efficiently.
    Returns the number of items successfully saved.
    """
    if not items:
        return 0

    # Prepare data for bulk insert
    data_to_insert = []
    for item in items:
        data_to_insert.append((
            item.title,
            str(item.link),
            item.published_at,  # PostgreSQL handles datetime objects
            item.summary,
            item.publisher,
            item.source,
            item.fetched_at,
            item.relevance.value if item.relevance else None,
            item.is_time_sensitive,
            item.timeliness_reason,
            item.is_duplicate,
            item.duplicate_of_url,
            item.duplicate_of_item_type,
            item.duplicate_of_item_id,
            item.deduplication_reason
        ))

    sql = '''
        INSERT INTO news_items
        (title, link, published_at, summary, publisher, source, fetched_at, relevance, 
         is_time_sensitive, timeliness_reason, is_duplicate, duplicate_of_url, 
         duplicate_of_item_type, duplicate_of_item_id, deduplication_reason)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (link) DO NOTHING
    '''
    
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # Get count before insert
            cursor.execute("SELECT COUNT(*) FROM news_items")
            count_before = cursor.fetchone()['count']
            
            # Perform bulk insert
            cursor.executemany(sql, data_to_insert)
            
            # Get count after insert
            cursor.execute("SELECT COUNT(*) FROM news_items")
            count_after = cursor.fetchone()['count']
            
            saved_count = count_after - count_before
            
        logger.info(f"Attempted to save {len(items)} items, successfully saved {saved_count} new items to database")
        return saved_count
    except Exception as e:
        logger.error(f"Database error during bulk insert of news items: {e}")
        raise


def update_item_relevance(link: Union[str, HttpUrl], relevance: RelevanceStatus) -> bool:
    """Update the relevance of a news item by its link."""
    link_str = str(link)

    try:
        affected = execute_query(
            "UPDATE news_items SET relevance = %s WHERE link = %s",
            (relevance.value, link_str)
        )
        return affected > 0
    except Exception as e:
        logger.error(f"Error updating relevance for {link}: {e}")
        return False


def update_item_timeliness(link: Union[str, HttpUrl], is_time_sensitive: bool, timeliness_reason: Optional[str] = None) -> bool:
    """Update the timeliness information of a news item by its link."""
    link_str = str(link)

    try:
        affected = execute_query(
            "UPDATE news_items SET is_time_sensitive = %s, timeliness_reason = %s WHERE link = %s",
            (is_time_sensitive, timeliness_reason, link_str)
        )
        return affected > 0
    except Exception as e:
        logger.error(f"Error updating timeliness for {link}: {e}")
        return False


def get_recent_items(limit: int = 10, source: Optional[str] = None) -> List[NewsItem]:
    """Retrieve the most recent news items from the database."""
    if source:
        query = "SELECT * FROM news_items WHERE source = %s ORDER BY fetched_at DESC LIMIT %s"
        params = (source, limit)
    else:
        query = "SELECT * FROM news_items ORDER BY fetched_at DESC LIMIT %s"
        params = (limit,)

    rows = fetch_all(query, params)

    items = []
    for row in rows:
        try:
            item = _row_to_news_item(row)
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")

    return items


def get_unevaluated_news_items(limit: Optional[int] = None) -> List[NewsItem]:
    """Retrieve news items that haven't been evaluated for relevance yet, excluding duplicates."""
    query = "SELECT * FROM news_items WHERE relevance IS NULL AND is_duplicate = FALSE ORDER BY fetched_at DESC"
    params = None
    
    if limit:
        query += " LIMIT %s"
        params = (limit,)

    rows = fetch_all(query, params)

    items = []
    for row in rows:
        try:
            item = _row_to_news_item(row)
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")

    return items


def get_unevaluated_news_items_since(timestamp: datetime, limit: Optional[int] = None) -> List[NewsItem]:
    """Retrieve unevaluated news items fetched since a specific timestamp, excluding duplicates."""
    query = "SELECT * FROM news_items WHERE relevance IS NULL AND fetched_at >= %s AND is_duplicate = FALSE ORDER BY fetched_at ASC"
    params = [timestamp]  # PostgreSQL handles datetime objects
    
    if limit:
        query += " LIMIT %s"
        params.append(limit)
    
    rows = fetch_all(query, tuple(params))
    
    items = []
    for row in rows:
        try:
            item = _row_to_news_item(row)
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")
    
    return items


def get_recent_news_items(days: int = 7, relevance: Optional[RelevanceStatus] = None) -> List[NewsItem]:
    """Retrieve news items from the last X days, optionally filtered by relevance, excluding duplicates."""
    # Calculate the date X days ago
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)

    if relevance is None:
        # Get all items from the last X days, excluding duplicates
        query = "SELECT * FROM news_items WHERE published_at >= %s AND is_duplicate = FALSE ORDER BY published_at DESC"
        params = (cutoff_date,)
    else:
        # Get items from the last X days with specific relevance, excluding duplicates
        query = "SELECT * FROM news_items WHERE published_at >= %s AND relevance = %s AND is_duplicate = FALSE ORDER BY published_at DESC"
        params = (cutoff_date, relevance.value)

    rows = fetch_all(query, params)

    items = []
    for row in rows:
        try:
            item = _row_to_news_item(row)
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")

    return items


def get_news_items_since(timestamp: Union[datetime, str], relevance: Optional[RelevanceStatus] = None) -> List[NewsItem]:
    """
    Retrieve news items collected since the given timestamp, optionally filtered by relevance.

    Args:
        timestamp: The cutoff timestamp (items collected after this will be returned)
        relevance: If provided, only return items with this relevance status

    Returns:
        List of NewsItem objects collected since the timestamp
    """
    # PostgreSQL can handle datetime objects directly
    if isinstance(timestamp, str):
        timestamp = datetime.fromisoformat(timestamp)

    # Build query based on parameters
    if relevance is None:
        # Get all items since the timestamp
        query = "SELECT * FROM news_items WHERE fetched_at >= %s ORDER BY published_at DESC"
        params = (timestamp,)
    else:
        # Get items since the timestamp with specific relevance
        query = "SELECT * FROM news_items WHERE fetched_at >= %s AND relevance = %s ORDER BY published_at DESC"
        params = (timestamp, relevance.value)

    rows = fetch_all(query, params)

    items = []
    for row in rows:
        try:
            item = _row_to_news_item(row)
            items.append(item)
        except Exception as e:
            logger.error(f"Error converting row to NewsItem: {e}")

    return items