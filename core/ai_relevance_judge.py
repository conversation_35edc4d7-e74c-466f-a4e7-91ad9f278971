import logging
import os
from typing import List, <PERSON><PERSON>, Optional
import anthropic
from anthropic.types import ToolU<PERSON><PERSON>lock
from core.models import NewsItem, TweetItem
from core.enums import RelevanceStatus
from core.interfaces.llm_preparable_interface import LLMPreparable
from core.config import ANTHROPIC_API_KEY, LLM_MODEL

logger = logging.getLogger(__name__)

# Define paths to prompt files
PROMPTS_DIR = os.path.join(os.path.dirname(__file__), "llm", "prompts")
RELEVANCE_SYSTEM_PROMPT_PATH = os.path.join(PROMPTS_DIR, "relevance_evaluation_system_prompt.txt")
RELEVANCE_USER_PROMPT_PATH = os.path.join(PROMPTS_DIR, "relevance_evaluation_user_prompt.txt")

def load_prompt(file_path: str) -> str:
    """
    Load a prompt from a file.

    Args:
        file_path: Path to the prompt file

    Returns:
        The prompt text
    """
    try:
        with open(file_path, 'r') as file:
            return file.read().strip()
    except Exception as e:
        logger.error(f"Error loading prompt from {file_path}: {e}")
        # Return a default prompt if the file can't be loaded
        return ""

# Initialize Anthropic client
client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)

# Define the tool schema for structured output
RELEVANCE_EVALUATION_TOOL = {
    "name": "evaluate_relevance",
    "description": "Evaluate content relevance and time-sensitivity for the newsletter",
    "input_schema": {
        "type": "object",
        "properties": {
            "category": {
                "type": "string",
                "enum": ["Yes", "No", "Maybe"],
                "description": "Whether to include in newsletter: Yes (Include), No (Exclude), or Maybe (Needs manual review)"
            },
            "time_sensitive": {
                "type": "boolean",
                "description": "Whether the content is time-sensitive"
            },
            "timeliness_reason": {
                "type": "string",
                "description": "If time-sensitive, explain why (e.g., 'Event happening tomorrow', 'Announcement made today')"
            },
            "rationale": {
                "type": "string",
                "description": "Brief explanation citing specific criteria for the relevance decision"
            }
        },
        "required": ["category", "time_sensitive", "rationale"]
    }
}

def _evaluate_content_relevance(title: str, content: str) -> Tuple[RelevanceStatus, bool, Optional[str]]:
    """
    Evaluate if content is relevant and time-sensitive using Anthropic's LLM with structured output.

    This function takes the essential information needed for evaluation
    and doesn't care about the source object type.

    Args:
        title: The title of the content
        content: The main content/body text to evaluate

    Returns:
        Tuple of (RelevanceStatus, is_time_sensitive, timeliness_reason)
    """
    if not ANTHROPIC_API_KEY:
        logger.error("ANTHROPIC_API_KEY is not set. Skipping relevance check.")
        return RelevanceStatus.NEEDS_REVIEW, False, None

    try:
        # Load prompts from files
        system_prompt = load_prompt(RELEVANCE_SYSTEM_PROMPT_PATH)
        user_prompt_template = load_prompt(RELEVANCE_USER_PROMPT_PATH)

        # If prompts couldn't be loaded, log error and return NEEDS_REVIEW
        if not system_prompt or not user_prompt_template:
            logger.error("Failed to load prompts for content evaluation")
            return RelevanceStatus.NEEDS_REVIEW, False, None

        # Append the content to evaluate to the existing prompt template
        complete_user_prompt = f"""
{user_prompt_template}

Here is the content item to evaluate:
Title: {title}
Content: {content}

Please use the evaluate_relevance tool to provide your structured evaluation."""

        response = client.messages.create(
            model=LLM_MODEL,
            max_tokens=500,
            temperature=0,
            system=system_prompt,
            tools=[RELEVANCE_EVALUATION_TOOL],
            tool_choice={"type": "tool", "name": "evaluate_relevance"},
            messages=[{"role": "user", "content": complete_user_prompt}]
        )

        # Extract the tool use response
        tool_use = None
        for content_block in response.content:
            if isinstance(content_block, ToolUseBlock) and content_block.name == "evaluate_relevance":
                tool_use = content_block
                break

        if not tool_use:
            logger.error("No tool use found in LLM response")
            return RelevanceStatus.NEEDS_REVIEW, False, None

        # Parse the structured response
        evaluation = tool_use.input
        logger.info(f"LLM evaluation for '{title}': {evaluation}")

        # Map category to RelevanceStatus
        category = evaluation.get("category", "").lower()
        if category == "yes":
            relevance_status = RelevanceStatus.RELEVANT
        elif category == "no":
            relevance_status = RelevanceStatus.NOT_RELEVANT
        elif category == "maybe":
            relevance_status = RelevanceStatus.NEEDS_REVIEW
        else:
            logger.warning(f"Unexpected category value: {evaluation.get('category')}")
            relevance_status = RelevanceStatus.NEEDS_REVIEW

        # Extract time-sensitivity information
        is_time_sensitive = evaluation.get("time_sensitive", False)
        timeliness_reason = evaluation.get("timeliness_reason") if is_time_sensitive else None

        return relevance_status, is_time_sensitive, timeliness_reason

    except Exception as e:
        logger.error(f"Error evaluating content: {e}")
        return RelevanceStatus.NEEDS_REVIEW, False, None

def evaluate_item_relevance(item: LLMPreparable) -> RelevanceStatus:
    """
    Evaluate an item for relevance and time-sensitivity using Anthropic's LLM with structured output.

    This is the primary function for evaluating content relevance.
    It uses the LLMPreparable interface to get the title and content
    from the item, then passes them to _evaluate_content_relevance.

    Args:
        item: The item to evaluate (must implement LLMPreparable)

    Returns:
        RelevanceStatus enum indicating relevance (RELEVANT, NOT_RELEVANT, or NEEDS_REVIEW)
    """
    if not ANTHROPIC_API_KEY:
        logger.error("ANTHROPIC_API_KEY is not set. Skipping relevance check.")
        return RelevanceStatus.NEEDS_REVIEW

    try:
        # Get title and content from the item using the LLMPreparable interface
        title, content = item.get_llm_input()

        # Evaluate the content for relevance and time-sensitivity
        status, is_time_sensitive, timeliness_reason = _evaluate_content_relevance(title, content)

        # Update the item's attributes if they exist
        if hasattr(item, 'relevance'):
            item.relevance = status
        
        if hasattr(item, 'is_time_sensitive'):
            item.is_time_sensitive = is_time_sensitive
            
        if hasattr(item, 'timeliness_reason') and timeliness_reason:
            item.timeliness_reason = timeliness_reason

        return status

    except Exception as e:
        logger.error(f"Error evaluating item relevance: {e}")
        return RelevanceStatus.NEEDS_REVIEW
