# Changelog

All notable changes to the TAC Weekly News Aggregation System will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## 2025-06-17

### Changed - X API Client Refactoring
- Extracted rate limiting logic into dedicated `RateLimiter` class (commit: pending)
  - Moved 5 rate limit methods from `XApiClient` to new `sources/x/rate_limiter.py`
  - Fixed DRY violation by creating `_perform_wait_with_progress()` helper method
  - Reduced `api_client.py` from 749 to 540 lines (209 lines removed)
  - Improved separation of concerns and maintainability
  - Fixed security vulnerability: sanitized exception logging to prevent bearer token leakage
  - Added exception handling in pagination loop to prevent data loss on errors
  - Fixed timezone handling bug in `stop_when_older_than` comparison
  - All existing functionality preserved with better organization

### Fixed - Notion Time-Sensitive Display
- Moved time-sensitive items to proper location within daily sections (commit: 51d64c8)
  - Time-sensitive items now appear AFTER the daily header, not above it
  - Changed time-sensitive section from H1 to H3 for proper hierarchy within daily sections
  - Added duplicate prevention logic to exclude time-sensitive items from regular categories
  - Each item now appears only once in the appropriate section
  - Modified 116 lines in `sources/notion/notion_publisher.py`

### Fixed - Google Form Interface Compliance
- Added missing `config_params` parameter to `GoogleFormDataSource.fetch_items()` (commit: 834dc91)
  - Method signature now matches the DataSource interface requirement
  - Prevents TypeError when main.py calls the method with config_params argument
  - Updated `sources/google_form/google_forms.py`

### Fixed - Telegram CI/CD Compatibility
- Resolved Telegram "EOF when reading a line" error in non-interactive environments (commit: f901a76)
  - Added support for both file-based sessions (local) and string sessions (CI/CD)
  - Replaced `client.start()` with `client.connect()` to avoid interactive prompts
  - Added automatic detection of `TELEGRAM_SESSION_STRING` environment variable
  - Implemented base64 decoding for secure session string storage
  - Added proper EOFError exception handling
  - Enhanced `generate_telegram_session.py` with GitHub Secrets instructions
  - Updated `sources/telegram/telegram_scraper.py` and `.gitignore`

## 2025-06-16

### Added - Time-Sensitivity Detection and Email Alerts
- Implemented comprehensive time-sensitivity detection system (commit: b510b3d)
- Created `core/email_alerts.py` (251 lines) for email notification handling
- Enhanced AI relevance evaluation in `core/ai_relevance_judge.py` (86 lines modified)
- Added database fields: `is_time_sensitive`, `time_sensitivity_reason` in `core/models.py`
- Created database migration: `core/storage/migrations/add_timeliness_fields.py` (72 lines)
- Updated `core/storage/news_items.py` (87 lines) with time-sensitivity support
- Added test scripts:
  - `tests/test_email_only.py` (226 lines) - Isolated email functionality testing
  - `tests/test_time_sensitive.py` (150 lines) - Time-sensitivity detection testing
- Enhanced `scripts/main.py` (71 lines) with email alert integration
- Added command-line options:
  - `--enable-email-alerts` - Send email notifications for time-sensitive content
  - `--email-dry-run` - Check for time-sensitive content without sending emails
- Updated GitHub Actions workflow with email configuration variables
- Enhanced `sources/notion/notion_publisher.py` (123 lines) for time-sensitive display
- System detects: upcoming events/deadlines, breaking announcements, limited-time opportunities, regulatory deadlines
- Updated LLM prompts to include timeliness assessment criteria

### Changed - Tweet Storage Performance Refactoring
- Refactored `core/storage/tweet_items.py` for reliability and performance (commit: adc0461)
  - Implemented bulk insert optimization using `executemany()` with `INSERT OR IGNORE`
  - Replaced `SELECT *` queries with explicit column names for schema independence
  - Created `_row_to_tweet_item()` helper function to eliminate code duplication (DRY)
  - Increased SQLite timeout from default to 10 seconds for concurrent access
  - Fixed timezone bugs by consistently using `datetime.now(timezone.utc)`
  - Added proper context managers (with statements) for resource management
- Updated `core/storage/db_init.py` with database index on 'source' column
- Fixed missing function exports in `core/storage/__init__.py`:
  - `update_tweet_timeliness`
  - `get_unevaluated_tweet_items`
- Updated `tests/db/test_tweet_storage.py` to use `RelevanceStatus` enum
- Improved error logging to prevent information disclosure

## 2025-06-11

### Fixed - Asyncio Event Loop Management
- Replaced manual asyncio event loop management with `asyncio.run()` (commit: 217c6e0)
  - Fixed segmentation fault during garbage collection after script completion
  - Resolved improper cleanup of asyncio event loop resources
  - Prevented memory access violations when cleaning up asyncio.Queue objects
  - Ensures proper event loop lifecycle management (creation, execution, cleanup)
  - Updated `sources/telegram/telegram_scraper.py` following Python 3.7+ best practices

## [0.1.0] - Initial Release
Core functionality of the TAC Weekly News Aggregation System:
- Multi-source data collection (RSS, X/Twitter, Telegram, Google Forms)
- AI-powered relevance evaluation using Claude
- Automated Notion publishing with weekly newsletter format
- SQLite database for content storage and state management
- Modular architecture with interface-based design pattern
- Fixed schedule publishing system tracking last publication timestamp