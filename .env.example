# TAC News Aggregation System Environment Variables
# Copy this file to .env and fill in your values

# ===== REQUIRED: Core Infrastructure =====
SUPABASE_DB_URL="postgresql://postgres:[YOUR-PASSWORD]@[YOUR-HOST]:5432/postgres"
ANTHROPIC_API_KEY="sk-ant-..."  # For relevance evaluation

# ===== REQUIRED: Data Sources (provide keys to enable) =====
# X/Twitter - Bearer Token method is primary
X_BEARER_TOKEN="AAAA..."
X_LIST_ID="1906777783583584471"  # Default TAC list

# Telegram - Session string is operationally sensitive
TELEGRAM_API_ID="12345"
TELEGRAM_API_HASH="abcdef..."
TELEGRAM_PHONE_NUMBER="+***********"
TELEGRAM_SESSION_STRING="base64-encoded-string..."  # Treat as password

# Google Form
GOOGLE_FORM_SHEET_ID="15O_GGK_gTjOyuwRSBHTWEgqM1jRXQ5jSkCYFdxw9ukA"
GOOGLE_SERVICE_ACCOUNT_JSON='{"type": "service_account", ...}'  # Full JSON as string

# ===== REQUIRED: Publishing & Notifications =====
# Notion
NOTION_API_TOKEN="secret_..."
NOTION_PAGE_ID="page-id-of-parent-newsletter-page"

# Email Alerts
EMAIL_ENABLED="true"
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USER="your-username"
SMTP_PASSWORD="your-password"
EMAIL_FROM_ADDRESS="TAC News Bot <<EMAIL>>"
EMAIL_RECIPIENTS="<EMAIL>,<EMAIL>"  # Comma-separated

# ===== OPTIONAL: Fine-tuning =====
KEYWORDS="Real World Assets,RWA,Tokenization"  # For Google News
SAMPLE_SIZE="50"  # Items to evaluate with LLM