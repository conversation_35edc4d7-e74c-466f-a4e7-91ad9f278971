# Email Notification Notion Link Fix
Date: June 20, 2025

## Issue
Email notifications were linking to the parent Notion page instead of the current week's subpage, and not navigating to the time-sensitive section.

## Solution
Modified the email notification system to:
1. Use the current weekly subpage ID instead of the parent page ID
2. Capture the time-sensitive section block ID when creating it in Notion
3. Include the block ID as an anchor in the email link

## Changes Made

### `core/email_alerts.py`
- Added `weekly_subpage_id` and `time_sensitive_block_id` parameters to `check_time_sensitive_items()`
- Updated email template rendering to use these IDs for the Notion link
- Added logging to track which IDs are being used

### `sources/notion/notion_publisher.py`
- Modified `append_to_existing_page()` to return both success status and time-sensitive block ID
- Added logic to capture the block ID from Notion API response when creating the time-sensitive section
- Updated `publish_to_notion()` to propagate the block ID

### `scripts/main.py`
- Restructured to run email notifications AFTER Notion publishing (so we have the page IDs)
- Captures both the weekly page ID and time-sensitive block ID
- Passes these IDs to the email notification function

## Result
Email notifications now link directly to:
- The correct weekly subpage (e.g., "TAC Weekly Newsletter - June 20-26, 2025")
- The time-sensitive section within that day's content (using anchor links)

## Testing
Verified with test scripts that confirmed:
- URL generation logic works correctly
- Block IDs are captured and formatted properly
- Links follow Notion's anchor format: `https://notion.so/{page_id}#{block_id_without_hyphens}`