"""Sources package for TAC Weekly News Aggregation System."""

# Export DataSource implementations
from .google_rss import GoogleNewsDataSource
from .telegram.telegram_scraper import TelegramDataSource
from .google_form.google_forms import GoogleFormDataSource
from .x.collector import XCollector

# Make these available at the package level
__all__ = [
    'GoogleNewsDataSource',
    'TelegramDataSource',
    'GoogleFormDataSource',
    'XCollector'
]