from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)

TOKEN_FILE = Path(__file__).resolve().parents[2] / "data" / "pagination_token.txt"


def read_token() -> Optional[str]:
    if TOKEN_FILE.exists():
        try:
            return TOKEN_FILE.read_text().strip() or None
        except Exception as e:
            logger.warning(f"Error reading pagination token: {e}")
    return None


def write_token(token: str) -> None:
    try:
        TOKEN_FILE.parent.mkdir(parents=True, exist_ok=True)
        TOKEN_FILE.write_text(token)
        logger.info(f"Saved pagination token ({token[:10]}...) for next run")
    except Exception as e:
        logger.error(f"Error saving pagination token: {e}") 