"""
X Collector

This module provides a high-level interface for collecting tweets from X Lists.
It integrates the API client, state manager, and tweet processor into a cohesive system.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any, Union, cast
from datetime import datetime, timedelta, timezone

# Import core modules
from core.models import TweetItem, NewsItem
from core.timezone_config import now_utc
from core.config import X_LIST_ID, X_TWEET_LIMIT
from core.storage import save_tweet_items
from core.interfaces.source_interface import DataSource

from .api_client import XApiClient
from .state_manager import XStateManager
from .tweet_processor import XTweetProcessor
from .time_filter import filter_tweets_by_time
from .collection_runner import run_complete_collection

logger = logging.getLogger(__name__)


class XCollector(DataSource):
    """Collector for X tweets. Implements the DataSource interface."""

    def __init__(self, stale_collection_hours: int = 24):
        """Initialize the X collector.
        
        Args:
            stale_collection_hours: Number of hours after which a collection is considered stale (default: 24)
        """
        self.api_client = XApiClient()
        self.state_manager = XStateManager(stale_hours=stale_collection_hours)
        self.tweet_processor = XTweetProcessor()

    def _create_page_callback(self, collection_id: Optional[str]):
        """Create a callback function for updating collection state after each page fetch.
        
        Args:
            collection_id: The collection ID to update. If None, returns None.
            
        Returns:
            Callback function or None if no collection_id provided.
        """
        if not collection_id:
            return None
            
        def page_callback(metadata, tweets, page_count, next_token):
            self.state_manager.update_collection(
                collection_id=collection_id,
                newest_tweet_id=metadata.get('response_meta', {}).get('newest_id'),
                oldest_tweet_id=metadata.get('response_meta', {}).get('oldest_id'),
                pagination_token=next_token,
                tweets_collected=len(tweets) if tweets else 0,
                requests_made=page_count,
                last_request_time=now_utc().isoformat(),
                notes=f"Page {page_count} fetched"
            )
            logger.info(f"Updated collection state after page {page_count}")
        
        return page_callback

    def _complete_collection(self, collection_id: str, tweet_count: int):
        """Mark a collection as complete and log the result."""
        self.state_manager.complete_collection(
            collection_id=collection_id,
            notes=f"Collection completed. Total tweets: {tweet_count}"
        )
        logger.info(f"Collection {collection_id} completed successfully")
    
    def _get_total_collected(self, collection_id: str, current_page_count: int) -> int:
        """Get the total number of tweets collected including the current page."""
        collection_state = self.state_manager.get_collection(collection_id)
        total_collected = collection_state.get('tweets_collected', 0) if collection_state else 0
        return total_collected + current_page_count
    
    def _log_completion_status(self, pagination_complete: bool, next_token: Optional[str]):
        """Log the completion status based on pagination state."""
        if pagination_complete and next_token:
            logger.info("Collection complete: Reached tweets older than time threshold")
        elif not next_token:
            logger.info("Collection complete: No more pages available")

    def fetch_x_list_tweets(
        self,
        list_id: str = X_LIST_ID,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        max_results: int = X_TWEET_LIMIT,
        save_to_db: bool = True,
        pagination_token: Optional[str] = None,
        collection_id: Optional[str] = None
    ) -> Tuple[List[TweetItem], Optional[str], Optional[str], bool]:
        """
        Fetch tweets from an X List.

        Args:
            list_id: ID of the list to fetch tweets from
            start_time: Only return tweets created after this time
            end_time: Only return tweets created before this time
            max_results: Maximum number of results to return
            save_to_db: Whether to save fetched tweets to the database
            pagination_token: Token for paginating through results
            collection_id: ID of the collection to associate with this fetch

        Returns:
            Tuple containing:
            - List of TweetItem objects
            - ID of the newest tweet fetched
            - Pagination token for the next page
            - Boolean indicating if pagination is complete
        """
        logger.info(f"Fetching tweets from X List {list_id}")

        # Start a new collection if time parameters are provided
        if start_time and end_time and not collection_id:
            collection_id = self.state_manager.start_collection(
                start_time=start_time,
                end_time=end_time,
                notes=f"List ID: {list_id}, Max results: {max_results}"
            )

        # Fetch tweets from the API
        # Note: List Tweets endpoint doesn't support time-based parameters
        # We'll filter by date after retrieval
        raw_tweets, metadata = self.api_client.get_all_list_tweets(
            list_id=list_id,
            max_results=max_results,
            page_callback=self._create_page_callback(collection_id),
            initial_pagination_token=pagination_token,
            stop_when_older_than=start_time if start_time else None
        )

        # Apply time filter if needed
        raw_tweets = filter_tweets_by_time(raw_tweets, start_time, end_time)

        # Final update to collection state with summary information
        if collection_id:
            self.state_manager.update_collection(
                collection_id=collection_id,
                tweets_collected=metadata.get('total_tweets', 0),
                requests_made=len(metadata.get('pages', [])),
                last_request_time=now_utc().isoformat(),
                notes=f"Collection fetch completed with {len(raw_tweets)} tweets"
            )

        # Process tweets
        processed_tweets = []
        if raw_tweets:
            # Extract includes data from the first page
            includes = metadata.get('pages', [{}])[0].get('includes', {})

            # Process tweets
            processed_tweets = self.tweet_processor.process_api_response(raw_tweets, includes)
            logger.info(f"Processed {len(processed_tweets)} tweets")

            # Save to database if requested
            if save_to_db and processed_tweets:
                saved_count = save_tweet_items(processed_tweets)
                logger.info(f"Saved {saved_count} new tweets to database")

        # Complete collection if no more pages
        if collection_id and not metadata.get('next_token'):
            self._complete_collection(collection_id, len(processed_tweets))

        # Return processed tweets, newest ID, pagination token, and pagination complete flag
        newest_id = metadata.get('newest_id')
        next_token = metadata.get('next_token')
        pagination_complete = metadata.get('pagination_complete', False)

        return processed_tweets, newest_id, next_token, pagination_complete

    def fetch_weekly_tweets(
        self,
        list_id: str = X_LIST_ID,
        max_results: int = X_TWEET_LIMIT,
        save_to_db: bool = True
    ) -> List[TweetItem]:
        """
        Fetch tweets from the past week.

        Note: This is a simple version. For more robust collection with resumption capabilities,
        use collect_weekly_tweets() instead.

        Args:
            list_id: ID of the list to fetch tweets from
            max_results: Maximum number of results to return
            save_to_db: Whether to save fetched tweets to the database

        Returns:
            List of TweetItem objects
        """
        end_time = now_utc()
        start_time = end_time - timedelta(days=7)
        tweets, _, _, _ = self.fetch_x_list_tweets(
            list_id=list_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            save_to_db=save_to_db
        )
        return tweets

    def collect_weekly_tweets(
        self,
        list_id: str = X_LIST_ID,
        max_results: int = X_TWEET_LIMIT,
        save_to_db: bool = True,
        days: int = 7,
        resume_if_exists: bool = True,
        force_new_collection: bool = False
    ) -> Tuple[List[TweetItem], str, bool]:
        """
        Collect tweets from the past week with robust state tracking and resumption capabilities.

        This method uses pagination tokens for continuous collection and post-collection time filtering.
        It provides a reliable way to collect tweets over a specified time period,
        handling rate limits and allowing for resumption if interrupted.

        Args:
            list_id: ID of the list to fetch tweets from
            max_results: Maximum number of results to return
            save_to_db: Whether to save fetched tweets to the database
            days: Number of days to look back (default: 7)
            resume_if_exists: Whether to resume an existing collection if one exists

        Returns:
            Tuple containing:
            - List of TweetItem objects
            - Collection ID for tracking/resuming
            - Boolean indicating whether the collection is complete
        """
        # Check if there's an active collection that we can resume
        if force_new_collection:
            active_collection = None  # Force fresh start for daily runs
        else:
            active_collection = self.state_manager.get_collection() if resume_if_exists else None

        if active_collection and resume_if_exists:
            logger.info(f"Found active collection {active_collection.get('collection_id')}, resuming")
            tweets, is_complete = self.resume_collection(
                collection_id=active_collection.get('collection_id'),
                max_results=max_results,
                save_to_db=save_to_db
            )
            return tweets, active_collection.get('collection_id', ''), is_complete

        # Calculate time window for the collection period
        now = datetime.now(timezone.utc)
        end_time = now
        start_time = now - timedelta(days=days)

        logger.info(f"Starting new weekly collection for period: {start_time.isoformat()} to {end_time.isoformat()}")

        # Start a new collection
        collection_id = self.state_manager.start_collection(
            start_time=start_time,
            end_time=end_time,
            notes=f"Weekly collection for {days} days, list_id={list_id}"
        )

        if not collection_id:
            logger.error("Failed to create collection state")
            return [], "", False

        # Fetch tweets using pagination token approach
        # Pass start_time to enable the "Stop-When-Old" logic
        tweets, newest_id, next_token, pagination_complete = self.fetch_x_list_tweets(
            list_id=list_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            save_to_db=save_to_db,
            collection_id=collection_id
        )

        # Log the time window we're filtering for
        logger.info(f"Filtering tweets for time window: {start_time.isoformat()} to {end_time.isoformat()}")

        # IMPORTANT: Two-stage filtering pattern (see also api_client.py)
        # Stage 1: API client stops pagination when tweets are older than start_time (efficiency)
        # Stage 2: This collector applies exact time window filtering (correctness)
        # The API's stop_when_older_than may include some tweets outside our window on the last page
        # Both stages are necessary - do not remove either one!

        # Determine if collection is complete
        is_complete = not next_token or pagination_complete
        self._log_completion_status(pagination_complete, next_token)
        
        if is_complete:
            # Mark collection as complete
            self._complete_collection(collection_id, len(tweets))
        else:
            # Update collection state for future resumption
            self.state_manager.update_collection(
                collection_id=collection_id,
                pagination_token=next_token,
                newest_tweet_id=newest_id,
                notes=f"Collection in progress. Collected {len(tweets)} tweets so far."
            )
            logger.info(f"Weekly collection in progress. Collected {len(tweets)} tweets so far.")

        return tweets, collection_id, is_complete

    def resume_collection(
        self,
        collection_id: Optional[str] = None,
        max_results: int = X_TWEET_LIMIT,
        save_to_db: bool = True
    ) -> Tuple[List[TweetItem], bool]:
        """
        Resume an interrupted collection using the pagination token approach.

        Args:
            collection_id: ID of the collection to resume (uses active collection if None)
            max_results: Maximum number of results to return
            save_to_db: Whether to save fetched tweets to the database

        Returns:
            Tuple containing:
            - List of TweetItem objects
            - Boolean indicating whether the collection is complete
        """
        # Get parameters for resuming
        params = self.state_manager.get_resume_parameters(collection_id)
        if not params:
            logger.warning("No collection to resume")
            return [], False

        collection_id = params.get('collection_id')
        pagination_token = params.get('pagination_token')
        start_time = params.get('start_time')
        end_time = params.get('end_time')

        if not collection_id:
            logger.warning("No collection ID found in resume parameters")
            return [], False

        if not pagination_token:
            logger.warning(f"No pagination token found for collection {collection_id}")
            logger.info("Starting a new collection instead of resuming")

            # Start a new collection with the same time window
            # Note: We don't use time-based parameters in the API call
            tweets, newest_id, next_token, pagination_complete = self.fetch_x_list_tweets(
                list_id=X_LIST_ID,
                start_time=start_time,
                max_results=max_results,
                save_to_db=save_to_db,
                collection_id=collection_id
            )

            # Filter tweets by the time window
            tweets = filter_tweets_by_time(tweets, start_time, end_time)

            # Update collection state
            is_complete = not next_token or pagination_complete
            if is_complete:
                total_collected = self._get_total_collected(collection_id, len(tweets))
                self._complete_collection(collection_id, total_collected)
            else:
                self.state_manager.update_collection(
                    collection_id=collection_id,
                    pagination_token=next_token,
                    newest_tweet_id=newest_id,
                    notes=f"Collection in progress. Collected {len(tweets)} tweets so far."
                )

            return tweets, is_complete

        # Resume collection using the pagination token
        logger.info(f"Resuming collection {collection_id} with pagination token: {pagination_token[:10]}...")

        # Fetch tweets using the saved pagination token
        # Note: We don't use time-based parameters in the API call when using pagination token
        tweets, newest_id, next_token, pagination_complete = self.fetch_x_list_tweets(
            list_id=X_LIST_ID,
            start_time=start_time,
            pagination_token=pagination_token,
            max_results=max_results,
            save_to_db=save_to_db,
            collection_id=collection_id
        )

        # Filter tweets by the time window
        tweets = filter_tweets_by_time(tweets, start_time, end_time)

        # Update collection state
        is_complete = not next_token or pagination_complete
        self._log_completion_status(pagination_complete, next_token)
        
        if is_complete:
            total_collected = self._get_total_collected(collection_id, len(tweets))
            self._complete_collection(collection_id, total_collected)
        else:
            # Update with the new pagination token for future resumption
            self.state_manager.update_collection(
                collection_id=collection_id,
                pagination_token=next_token,
                newest_tweet_id=newest_id or params.get('newest_tweet_id'),
                notes=f"Collection in progress. Collected {len(tweets)} tweets so far."
            )
            logger.info(f"Collection {collection_id} resumed but not complete. New token: {next_token[:10] if next_token else 'None'}...")

        return tweets, is_complete

    def collect_all_tweets_complete(
        self,
        list_id: str = X_LIST_ID,
        max_results_per_run: int = 100,
        days: int = 7,
        max_attempts: int = 5,
        delay_between_attempts: int = 0,
        save_to_db: bool = True,
        force_new_collection: bool = False
    ) -> Tuple[List[TweetItem], str, bool, Dict[str, Any]]:
        """
        Collect all tweets from the X List by running the collection process
        repeatedly until it's complete or max attempts is reached.

        This function handles pagination internally and makes multiple attempts
        if needed to collect all tweets within the specified time window.

        Args:
            list_id: ID of the list to fetch tweets from
            max_results_per_run: Maximum number of results to fetch in each run
            days: Number of days to look back
            max_attempts: Maximum number of attempts to make
            delay_between_attempts: Delay between attempts in seconds
            save_to_db: Whether to save fetched tweets to the database

        Returns:
            Tuple containing:
            - List of TweetItem objects
            - Collection ID for tracking
            - Boolean indicating whether collection is complete
            - Dictionary with collection metrics
        """
        logger.info("Starting complete tweet collection process")

        # Track attempt count to fix the force_new_collection issue
        attempt_tracker = {"count": 0}
        collection_id_holder = {"id": ""}

        def one_attempt():
            attempt_tracker["count"] += 1
            
            # Only force new collection on first attempt
            # This fixes the bug where every attempt was starting fresh
            force_on_this_attempt = force_new_collection and (attempt_tracker["count"] == 1)
            
            tweets, cid, done = self.collect_weekly_tweets(
                list_id=list_id,
                max_results=max_results_per_run,
                save_to_db=save_to_db,
                days=days,
                resume_if_exists=True,
                force_new_collection=force_on_this_attempt
            )
            collection_id_holder["id"] = cid
            return tweets, done

        all_tweets, is_complete, metrics = run_complete_collection(
            one_attempt,
            max_attempts=max_attempts,
            delay_seconds=delay_between_attempts,
        )

        return all_tweets, collection_id_holder["id"], is_complete, metrics

    def get_source_name(self) -> str:
        """Returns a human-readable name for the source."""
        return "X/Twitter List"

    def fetch_items(self, config_params: Optional[Dict[str, Any]] = None) -> List[Union[NewsItem, TweetItem]]:
        """
        Fetches tweets from X List.

        Args:
            config_params: Dictionary containing configuration parameters:
                - max_results_per_run: Maximum number of results per tweet collection attempt
                - days: Number of days to look back for tweets
                - max_attempts: Maximum number of tweet collection attempts
                - delay_between_attempts: Delay between tweet collection attempts in seconds

        Returns:
            List of TweetItem objects collected from X.
        """
        if config_params is None:
            config_params = {}

        # Extract parameters from config
        max_results = config_params.get('max_results_per_run', 100)
        days = config_params.get('days', 7)
        max_attempts = config_params.get('max_attempts', 5)
        delay_between_attempts = config_params.get('delay_between_attempts', 0)

        logger.info(f"Fetching tweets from X List with parameters: max_results={max_results}, days={days}, max_attempts={max_attempts}")

        # For daily runs (1 day or less), always start fresh
        force_new = days <= 1

        # Run the complete collection process
        # save_to_db=True here means XCollector's internal call to fetch_x_list_tweets
        # might save tweets using save_tweet_items. This is acceptable as main.py's save_tweet_items
        # has a check to skip duplicates.
        tweets, _, is_complete, metrics = self.collect_all_tweets_complete(
            max_results_per_run=max_results,
            days=days,
            max_attempts=max_attempts,
            delay_between_attempts=delay_between_attempts,
            save_to_db=True,
            force_new_collection=force_new  # Pass the flag
        )

        logger.info(f"Tweet collection completed with {metrics['attempts']} attempts")
        logger.info(f"Collected a total of {len(tweets)} tweets")
        logger.info(f"Collection complete: {'Yes' if is_complete else 'No'}")

        if not tweets:
            logger.info("No tweets collected from X List")
            return []

        # REMOVED: Relevance evaluation logic and conversion to NewsItems.
        # The main script (scripts/main.py) will now handle relevance evaluation
        # for all LLMPreparable items, including TweetItems.

        return cast(List[Union[NewsItem, TweetItem]], tweets) # Return raw TweetItems
