from __future__ import annotations

import logging
import time
from datetime import datetime
from typing import <PERSON><PERSON>, List, Dict, Any, Callable

from core.timezone_config import now_utc

logger = logging.getLogger(__name__)


def run_complete_collection(
    collect_once: Callable[[], <PERSON>ple[List[Any], bool]],
    max_attempts: int = 5,
    delay_seconds: int = 60,
) -> Tuple[List[Any], bool, Dict[str, Any]]:
    """Run `collect_once` until it reports completion or attempts exhausted.

    `collect_once` must return (items, is_complete).
    Returns (all_items, is_complete, metrics).
    """
    attempt = 0
    all_items: List[Any] = []
    seen_ids = set()  # Track unique tweet IDs to avoid duplicates
    is_complete = False
    metrics = {
        "attempts": 0,
        "total_items": 0,
        "unique_items": 0,
        "items_per_attempt": [],
        "is_complete": False,
        "start_time": now_utc().isoformat(),
        "end_time": None,
    }

    while not is_complete and attempt < max_attempts:
        attempt += 1
        metrics["attempts"] = attempt
        logger.info(f"Collection attempt {attempt}/{max_attempts}")

        try:
            items, is_complete = collect_once()
            items_this_attempt = len(items)
            
            # Deduplicate items based on tweet_id
            new_items = []
            for item in items:
                # Check if item has tweet_id attribute (TweetItem)
                if hasattr(item, 'tweet_id'):
                    if item.tweet_id not in seen_ids:
                        seen_ids.add(item.tweet_id)
                        new_items.append(item)
                else:
                    # If not a TweetItem, add it anyway
                    new_items.append(item)
            
            new_items_count = len(new_items)
            all_items.extend(new_items)
            metrics["total_items"] = len(all_items)
            metrics["items_per_attempt"].append(new_items_count)
            
            logger.info(f"Attempt {attempt} collected {items_this_attempt} items ({new_items_count} new, total unique: {len(all_items)})")
            
            if is_complete:
                logger.info("Collection completed successfully")
                break
            elif items_this_attempt == 0:
                logger.info("No new items found in this attempt")
                # If we got no new items and collection says it's not complete,
                # we should still retry in case of transient issues
            
            if not is_complete and attempt < max_attempts:
                logger.info(f"Not complete. Waiting {delay_seconds}s before next attempt…")
                time.sleep(delay_seconds)
        except Exception as e:
            logger.error(f"Error during attempt {attempt}: {e}")
            metrics["items_per_attempt"].append(0)
            if attempt < max_attempts:
                time.sleep(delay_seconds)

    # Update unique items count - now accurate since we're tracking seen_ids
    metrics["unique_items"] = len(all_items)
    metrics["is_complete"] = is_complete
    metrics["end_time"] = now_utc().isoformat()
    return all_items, is_complete, metrics 