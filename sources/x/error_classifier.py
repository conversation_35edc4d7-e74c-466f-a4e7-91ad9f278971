"""
X API Error Classifier

This module provides classification for X API errors based on type and resource.
"""

import logging

logger = logging.getLogger(__name__)


class XApiErrorClassifier:
    """Classifies X API errors based on type and resource."""
    
    # Use API error type URIs, not strings
    # These patterns identify non-critical errors that shouldn't stop collection
    NON_CRITICAL_PATTERNS = [
        # Missing referenced tweet (deleted, private, etc.)
        ("https://api.twitter.com/2/problems/resource-not-found", "tweet"),
        # Can add more patterns as discovered
    ]
    
    @staticmethod
    def is_non_critical(error_dict):
        """Check if a single error is non-critical."""
        error_type = error_dict.get('type', '')
        resource_type = error_dict.get('resource_type', '')
        
        for pattern_type, pattern_resource in XApiErrorClassifier.NON_CRITICAL_PATTERNS:
            if error_type == pattern_type and resource_type == pattern_resource:
                return True
        return False
    
    @staticmethod
    def analyze_errors(errors_list):
        """Analyze all errors in a response to determine overall criticality."""
        if not errors_list:
            return {
                'has_critical': False,
                'has_non_critical': False,
                'all_non_critical': False,
                'error_count': 0
            }
        
        has_critical = False
        has_non_critical = False
        critical_errors = []
        non_critical_errors = []
        
        for error in errors_list:
            if XApiErrorClassifier.is_non_critical(error):
                has_non_critical = True
                non_critical_errors.append(error)
            else:
                has_critical = True
                critical_errors.append(error)
        
        return {
            'has_critical': has_critical,
            'has_non_critical': has_non_critical,
            'all_non_critical': has_non_critical and not has_critical,
            'error_count': len(errors_list),
            'critical_errors': critical_errors,
            'non_critical_errors': non_critical_errors
        }