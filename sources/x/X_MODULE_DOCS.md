# X Integration Module

This module provides functionality for integrating with X (Twitter) in the TAC Weekly News Aggregation System. It includes components for fetching tweets from X Lists, processing them into structured objects, and managing collection state.

## Architecture

The X integration is built with a modular architecture consisting of the following components:

### 1. API Client (`api_client.py`)

The `XApiClient` class handles all direct interactions with the X API:
- Authentication with bearer token
- Making requests to the List Tweets endpoint
- Handling pagination
- Tracking rate limits
- Error handling

### 2. State Manager (`state_manager.py`)

The `XStateManager` class manages the state of tweet collection:
- Initializing new collections
- Tracking collection progress
- Saving and loading state from the database
- Handling interruptions and resumptions
- Marking collections as complete or failed

### 3. Tweet Processor (`tweet_processor.py`)

The `XTweetProcessor` class processes raw tweet data from the API:
- Converting raw JSON data into `TweetItem` objects
- Extracting URLs, hashtags, and mentions
- Determining tweet type (retweet, quote, reply)
- Filtering tweets based on criteria (e.g., has commentary)
- Converting tweets to news items if needed

### 4. Collector (`collector.py`)

The `XCollector` class provides a high-level interface for the entire system:
- Coordinating between the API client, state manager, and tweet processor
- Providing methods for fetching tweets with various parameters
- Handling database storage
- Supporting time-based collection
- Resuming interrupted collections

### 5. Collection Runner (`collection_runner.py`)

The `XCollectionRunner` class manages the execution of tweet collection:
- Handling multiple collection attempts
- Managing delays between attempts
- Tracking collection progress
- Providing a simplified interface for the main script

### 6. Pagination Token Store (`pagination_token_store.py`)

The `XPaginationTokenStore` class manages pagination tokens:
- Storing and retrieving pagination tokens
- Associating tokens with collection IDs
- Ensuring proper resumption of collections

### 7. Time Filter (`time_filter.py`)

The `filter_tweets_by_time` function handles time-based filtering:
- Filtering tweets based on creation date
- Supporting start and end time parameters
- Handling timezone conversions
- Ensuring tweets are within the specified time window

## Usage Examples

### Basic Usage

```python
from sources.x.collector import XCollector

# Create a collector instance
collector = XCollector()

# Fetch tweets from the default X List
tweets, newest_id, next_token = collector.fetch_x_list_tweets(max_results=10)

# Print the fetched tweets
for tweet in tweets:
    print(f"Tweet from @{tweet.author_username}: {tweet.text[:50]}...")
```

### Time-Based Collection

```python
from datetime import datetime, timedelta
from sources.x.collector import XCollector
from sources.x.time_filter import filter_tweets_by_time

# Create a collector instance
collector = XCollector()

# Define a time window for the past day
end_time = datetime.now()
start_time = end_time - timedelta(days=1)

# Fetch tweets from the time window
tweets, newest_id, next_token = collector.fetch_x_list_tweets(
    max_results=100
)

# Filter tweets by time
filtered_tweets = filter_tweets_by_time(tweets, start_time, end_time)

print(f"Fetched {len(tweets)} tweets, {len(filtered_tweets)} within the past day")
```

### Resuming a Collection

```python
from sources.x.collector import XCollector

# Create a collector instance
collector = XCollector()

# Resume an interrupted collection
tweets, is_complete = collector.resume_collection()

if is_complete:
    print(f"Collection completed with {len(tweets)} additional tweets")
else:
    print(f"Collection resumed but not complete, fetched {len(tweets)} tweets")
```

### Using the Collection Runner

```python
from sources.x.collection_runner import XCollectionRunner

# Create a collection runner instance
runner = XCollectionRunner()

# Run the collection with multiple attempts
tweets, is_complete = runner.run_collection(
    max_attempts=3,
    attempt_delay=0,
    max_results=100
)

print(f"Collection {'completed' if is_complete else 'not completed'} with {len(tweets)} tweets")
```

## Rate Limits

The X API has rate limits that restrict the number of requests that can be made within a time period:

- The List Tweets endpoint has a limit of approximately 25 requests per 15-minute window
- Each request can fetch up to 100 tweets
- When rate limits are hit, the API returns a 429 error

The `XApiClient` class handles rate limits by:
1. Tracking remaining requests from response headers
2. Logging warnings when approaching limits
3. Providing methods to wait for rate limit reset

## Known Limitations

1. **Future Dates**: The X API returns a 400 error when querying with future dates
2. **Rate Limits**: Collection of large numbers of tweets may be interrupted by rate limits
3. **Tweet Deletion**: Tweets may be deleted between collection runs, causing gaps
4. **API Changes**: The X API may change without notice, requiring updates to the integration
5. **Time Window Constraints**: The X API doesn't support direct time window filtering, requiring post-processing
6. **Pagination Limitations**: Pagination tokens expire after a certain period, limiting how far back collection can resume

## Error Handling

The X integration includes robust error handling:

1. **API Errors**: All API requests are wrapped in try/except blocks
2. **Rate Limits**: 429 errors are handled by pausing and resuming collection
3. **Invalid Parameters**: Parameter validation prevents common API errors
4. **State Tracking**: Collection state is saved frequently to enable recovery from failures
5. **Multiple Attempts**: The collection runner supports multiple attempts with configurable delays
6. **Pagination Token Management**: Pagination tokens are stored securely to enable proper resumption
7. **Time Filtering**: Robust time filtering ensures tweets are within the specified window
