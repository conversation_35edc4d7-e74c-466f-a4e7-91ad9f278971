"""
X State Manager

This module provides functionality for managing the state of X tweet collection.
It handles saving and loading state, tracking collection progress, and resuming interrupted collections.
"""

import logging
import uuid
from typing import Dict, Optional, Any
from datetime import datetime, timedelta

from core.timezone_config import now_utc

# Import storage functions
from core.storage import (
    initialize_collection_state,
    update_collection_state,
    get_collection_state,
    get_active_collection,
    clear_collection_state
)

logger = logging.getLogger(__name__)


class XStateManager:
    """Manager for X collection state."""

    def __init__(self, stale_hours: int = 24):
        """Initialize the state manager.
        
        Args:
            stale_hours: Number of hours after which a collection is considered stale (default: 24)
        """
        self.active_collection_id = None
        self.stale_hours = stale_hours
        self._load_active_collection()

    def _load_active_collection(self) -> Optional[Dict]:
        """
        Load the active collection state if one exists and is not stale.

        Returns:
            Dictionary with collection state or None if no active collection or if stale
        """
        try:
            active = get_active_collection()
            if active:
                # Check if the collection is stale
                if self._is_collection_stale(active):
                    collection_id = active.get('collection_id')
                    logger.warning(f"Found stale collection {collection_id}, marking as failed")
                    # Mark the stale collection as failed
                    self.fail_collection(
                        collection_id=collection_id,
                        error_message=f"Collection abandoned after {self.stale_hours} hours of inactivity"
                    )
                    return None
                
                self.active_collection_id = active.get('collection_id')
                logger.info(f"Loaded active collection with ID: {self.active_collection_id}")
                return active
            return None
        except Exception as e:
            logger.error(f"Error loading active collection: {e}")
            return None

    def _is_collection_stale(self, collection: Dict) -> bool:
        """
        Check if a collection is stale based on its last update time.
        
        Args:
            collection: Collection state dictionary
            
        Returns:
            True if the collection is stale, False otherwise
        """
        try:
            # Get the updated_at timestamp
            updated_at = collection.get('updated_at')
            if not updated_at:
                # If no updated_at, use created_at
                updated_at = collection.get('created_at')
            
            if not updated_at:
                # If still no timestamp, consider it stale
                logger.warning("Collection has no timestamp, considering it stale")
                return True
            
            # Handle both datetime objects and strings
            if isinstance(updated_at, str):
                updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
            elif isinstance(updated_at, datetime) and updated_at.tzinfo is None:
                # Ensure timezone awareness
                from datetime import timezone
                updated_at = updated_at.replace(tzinfo=timezone.utc)
            
            # Calculate age
            from datetime import timezone
            now = datetime.now(timezone.utc)
            age_hours = (now - updated_at).total_seconds() / 3600
            
            is_stale = age_hours > self.stale_hours
            if is_stale:
                logger.info(f"Collection is {age_hours:.1f} hours old (threshold: {self.stale_hours} hours)")
            
            return is_stale
            
        except Exception as e:
            logger.error(f"Error checking collection staleness: {e}")
            # If we can't determine staleness, consider it stale to be safe
            return True

    def start_collection(
        self,
        start_time: datetime,
        end_time: datetime,
        notes: Optional[str] = None
    ) -> Optional[str]:
        """
        Start a new collection.

        Args:
            start_time: Start time of the collection window
            end_time: End time of the collection window
            notes: Optional notes about this collection

        Returns:
            Collection ID if successful, None otherwise
        """
        # Check if there's already an active collection
        if self.active_collection_id:
            # Verify it's not stale before reusing
            collection = self.get_collection(self.active_collection_id)
            if collection and self._is_collection_stale(collection):
                logger.warning(f"Active collection {self.active_collection_id} is stale, marking as failed")
                self.fail_collection(
                    collection_id=self.active_collection_id,
                    error_message=f"Collection abandoned after {self.stale_hours} hours of inactivity"
                )
                # Clear the active collection ID so we create a new one
                self.active_collection_id = None
            else:
                logger.info(f"Reusing existing active collection: {self.active_collection_id}")
                return self.active_collection_id

        try:
            # Initialize a new collection state
            collection_id = initialize_collection_state(start_time, end_time)
            if collection_id:
                self.active_collection_id = collection_id

                # Add notes if provided
                if notes:
                    update_collection_state(collection_id, notes=notes)

                logger.info(f"Started new collection with ID: {collection_id}")
                return collection_id
            else:
                logger.error("Failed to initialize collection state")
                return None
        except Exception as e:
            logger.error(f"Error starting collection: {e}")
            return None

    def update_collection(
        self,
        collection_id: Optional[str] = None,
        **kwargs
    ) -> bool:
        """
        Update collection state.

        Args:
            collection_id: ID of the collection to update (uses active collection if None)
            **kwargs: Fields to update (oldest_tweet_id, newest_tweet_id, pagination_token,
                      status, last_request_time, requests_made, tweets_collected, notes)

        Returns:
            True if successful, False otherwise
        """
        collection_id = collection_id or self.active_collection_id
        if not collection_id:
            logger.warning("No active collection to update")
            return False

        try:
            success = update_collection_state(collection_id, **kwargs)
            if success:
                logger.debug(f"Updated collection state for ID: {collection_id}")
                return True
            else:
                logger.warning(f"Failed to update collection state for ID: {collection_id}")
                return False
        except Exception as e:
            logger.error(f"Error updating collection state: {e}")
            return False

    def complete_collection(
        self,
        collection_id: Optional[str] = None,
        notes: Optional[str] = None
    ) -> bool:
        """
        Mark a collection as complete.

        Args:
            collection_id: ID of the collection to complete (uses active collection if None)
            notes: Optional notes about completion

        Returns:
            True if successful, False otherwise
        """
        collection_id = collection_id or self.active_collection_id
        if not collection_id:
            logger.warning("No active collection to complete")
            return False

        try:
            update_args = {
                'status': 'complete',
                'updated_at': now_utc().isoformat()
            }

            if notes:
                update_args['notes'] = notes

            success = update_collection_state(collection_id, **update_args)

            if success:
                logger.info(f"Marked collection {collection_id} as complete")
                if collection_id == self.active_collection_id:
                    self.active_collection_id = None
                return True
            else:
                logger.warning(f"Failed to complete collection {collection_id}")
                return False
        except Exception as e:
            logger.error(f"Error completing collection: {e}")
            return False

    def fail_collection(
        self,
        collection_id: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Mark a collection as failed.

        Args:
            collection_id: ID of the collection to fail (uses active collection if None)
            error_message: Error message explaining the failure

        Returns:
            True if successful, False otherwise
        """
        collection_id = collection_id or self.active_collection_id
        if not collection_id:
            logger.warning("No active collection to fail")
            return False

        try:
            update_args = {
                'status': 'failed',
                'updated_at': now_utc().isoformat()
            }

            if error_message:
                update_args['notes'] = f"Failed: {error_message}"

            success = update_collection_state(collection_id, **update_args)

            if success:
                logger.info(f"Marked collection {collection_id} as failed")
                if collection_id == self.active_collection_id:
                    self.active_collection_id = None
                return True
            else:
                logger.warning(f"Failed to update collection {collection_id} status to failed")
                return False
        except Exception as e:
            logger.error(f"Error failing collection: {e}")
            return False

    def get_collection(self, collection_id: Optional[str] = None) -> Optional[Dict]:
        """
        Get collection state.

        Args:
            collection_id: ID of the collection to get (uses active collection if None)

        Returns:
            Dictionary with collection state or None if not found
        """
        collection_id = collection_id or self.active_collection_id
        if not collection_id:
            logger.warning("No active collection to get")
            return None

        try:
            state = get_collection_state(collection_id)
            return state
        except Exception as e:
            logger.error(f"Error getting collection state: {e}")
            return None

    def clear_collection(self, collection_id: Optional[str] = None) -> bool:
        """
        Clear/delete a collection.

        Args:
            collection_id: ID of the collection to clear (uses active collection if None)

        Returns:
            True if successful, False otherwise
        """
        collection_id = collection_id or self.active_collection_id
        if not collection_id:
            logger.warning("No active collection to clear")
            return False

        try:
            success = clear_collection_state(collection_id)
            if success:
                logger.info(f"Cleared collection {collection_id}")
                if collection_id == self.active_collection_id:
                    self.active_collection_id = None
                return True
            else:
                logger.warning(f"Failed to clear collection {collection_id}")
                return False
        except Exception as e:
            logger.error(f"Error clearing collection: {e}")
            return False

    def create_weekly_collection(self) -> Optional[str]:
        """
        Create a collection for the past week.

        Returns:
            Collection ID if successful, None otherwise
        """
        end_time = now_utc()
        start_time = end_time - timedelta(days=7)

        return self.start_collection(
            start_time=start_time,
            end_time=end_time,
            notes="Weekly collection"
        )

    def get_resume_parameters(self, collection_id: Optional[str] = None) -> Dict:
        """
        Get parameters for resuming a collection.

        Args:
            collection_id: ID of the collection to resume (uses active collection if None)

        Returns:
            Dictionary with parameters for resuming collection
        """
        collection_id = collection_id or self.active_collection_id
        if not collection_id:
            logger.warning("No active collection to resume")
            return {}

        try:
            state = get_collection_state(collection_id)
            if not state:
                logger.warning(f"No state found for collection {collection_id}")
                return {}

            # Extract parameters for resuming
            params = {
                'collection_id': collection_id,
                'start_time': state.get('start_time'),  # PostgreSQL returns datetime objects directly
                'end_time': state.get('end_time'),      # PostgreSQL returns datetime objects directly
                'pagination_token': state.get('pagination_token'),
                'newest_tweet_id': state.get('newest_tweet_id'),
                'oldest_tweet_id': state.get('oldest_tweet_id')
            }

            return params
        except Exception as e:
            logger.error(f"Error getting resume parameters: {e}")
            return {}
