"""
X API Time Utilities

This module provides time-related utilities for the X API client.
"""

from datetime import datetime, timedelta
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from .constants import TWITTER_EPOCH
from core.timezone_config import now_utc, utc_from_timestamp


def validate_time_window(start_time: Optional[datetime] = None, end_time: Optional[datetime] = None) -> Tuple[bool, str]:
    """
    Validate a time window for API requests.

    Args:
        start_time: Start time of the window
        end_time: End time of the window

    Returns:
        Tuple containing:
        - Boolean indicating if the time window is valid
        - String with error message if invalid, empty string if valid
    """
    # If neither is provided, that's valid (no time filtering)
    if start_time is None and end_time is None:
        return True, ""

    now = now_utc()

    # Check for future dates
    if start_time and start_time > now:
        return False, f"Start time {start_time.isoformat()} is in the future"

    if end_time and end_time > now:
        return False, f"End time {end_time.isoformat()} is in the future"

    # Check that start_time is before end_time
    if start_time and end_time and start_time >= end_time:
        return False, f"Start time {start_time.isoformat()} must be before end time {end_time.isoformat()}"

    # Check that the time window isn't too large (X API typically limits to 7 days)
    # But allow past windows of any size for historical data collection
    if start_time and end_time and (end_time - start_time).days > 7 and end_time > now - timedelta(days=7):
        return False, f"Time window exceeds 7 days: {(end_time - start_time).days} days"

    # Check that dates aren't too old (X API typically has limits)
    oldest_allowed = now - timedelta(days=30)  # Adjust based on your API access level
    if start_time and start_time < oldest_allowed:
        return False, f"Start time {start_time.isoformat()} is too old (older than 30 days)"

    return True, ""


def date_to_tweet_id(dt: datetime) -> str:
    """
    Convert a datetime to an approximate tweet ID.
    This uses Twitter's Snowflake ID format to estimate an ID for a given time.

    Args:
        dt: The datetime to convert

    Returns:
        A string containing an estimated tweet ID for that time
    """
    # Convert datetime to milliseconds since epoch
    timestamp_ms = int(dt.timestamp() * 1000)

    # Calculate milliseconds since Twitter epoch
    twitter_ms = timestamp_ms - TWITTER_EPOCH

    # Shift left by 22 bits (per Snowflake ID format)
    # This gives us just the timestamp portion of the ID
    snowflake_id = twitter_ms << 22

    return str(snowflake_id)


def tweet_id_to_date(tweet_id: str) -> datetime:
    """
    Convert a tweet ID to an approximate datetime.

    Args:
        tweet_id: The tweet ID to convert

    Returns:
        A datetime object representing the approximate creation time
    """
    # Convert to integer
    id_int = int(tweet_id)

    # Extract timestamp bits (shift right by 22 bits)
    timestamp_ms = (id_int >> 22) + TWITTER_EPOCH

    # Convert to datetime
    return utc_from_timestamp(timestamp_ms / 1000.0)


def is_tweet_in_time_window(tweet_created_at: datetime, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None) -> bool:
    """
    Check if a tweet falls within a specified time window.

    Args:
        tweet_created_at: The creation time of the tweet
        start_time: The start of the time window (inclusive)
        end_time: The end of the time window (exclusive)

    Returns:
        Boolean indicating if the tweet is within the time window
    """
    # If no time window is specified, all tweets are included
    if start_time is None and end_time is None:
        return True

    # Check against start_time if provided
    if start_time and tweet_created_at < start_time:
        return False

    # Check against end_time if provided
    if end_time and tweet_created_at >= end_time:
        return False

    # If we get here, the tweet is within the time window
    return True