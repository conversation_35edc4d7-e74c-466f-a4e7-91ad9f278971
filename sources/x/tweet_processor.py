"""
X Tweet Processor

This module provides functionality for processing raw tweet data from the X API
into structured TweetItem objects.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

# Import core models
from core.models import TweetItem, NewsItem

logger = logging.getLogger(__name__)


class XTweetProcessor:
    """Processor for X tweet data."""

    def __init__(self, include_retweets: bool = False, english_only: bool = True):
        """
        Initialize the tweet processor.

        Args:
            include_retweets: Whether to include retweets without commentary
            english_only: Whether to only include English tweets
        """
        self.include_retweets = include_retweets
        self.english_only = english_only

    def _detect_language(self, text: str) -> bool:
        """
        Simple heuristic to detect if text is in English.
        This is a fallback if the API doesn't provide language information.

        Args:
            text: The text to analyze

        Returns:
            True if the text appears to be in English, False otherwise
        """
        if not text:
            return False

        # Common English words that are unlikely to appear in non-English text
        english_words = {
            'the', 'and', 'that', 'have', 'for', 'not', 'with', 'you', 'this', 'but',
            'his', 'from', 'they', 'she', 'will', 'would', 'there', 'their', 'what', 'about',
            'which', 'when', 'make', 'can', 'like', 'time', 'just', 'him', 'know', 'take'
        }

        # Convert to lowercase and split into words
        words = set(text.lower().split())

        # Count how many common English words appear in the text
        english_word_count = len(words.intersection(english_words))

        # If at least 2 common English words are present, consider it English
        # This is a simple heuristic and not foolproof
        return english_word_count >= 2

    def process_tweets(
        self,
        tweets_data: List[Dict],
        users_data: Optional[Dict[str, Dict]] = None,
        referenced_tweets: Optional[Dict[str, Dict]] = None
    ) -> List[TweetItem]:
        """
        Process a list of tweet data dictionaries into TweetItem objects.

        Args:
            tweets_data: List of tweet data dictionaries from the API
            users_data: Dictionary mapping user IDs to user data
            referenced_tweets: Dictionary mapping tweet IDs to referenced tweet data

        Returns:
            List of TweetItem objects
        """
        processed_items = []

        # Extract users from includes if not provided
        if users_data is None:
            users_data = {}

        # Process each tweet
        for tweet_data in tweets_data:
            try:
                item = self.process_tweet(tweet_data, users_data, referenced_tweets)

                # Filter out retweets without commentary if configured
                if not self.include_retweets and not item.has_commentary:
                    logger.debug(f"Skipping tweet {item.tweet_id} as it has no commentary")
                    continue

                # Filter out non-English tweets if configured
                if self.english_only:
                    # Check language from API response
                    lang = tweet_data.get('lang')
                    is_english = lang == 'en' if lang else self._detect_language(tweet_data.get('text', ''))
                    if not is_english:
                        logger.debug(f"Skipping tweet {item.tweet_id} as it is not in English")
                        continue

                processed_items.append(item)
            except Exception as e:
                logger.warning(f"Error processing tweet data (ID: {tweet_data.get('id')}): {e}. Skipping tweet.")
                logger.debug(f"Problematic tweet data: {tweet_data}")

        return processed_items

    def process_tweet(
        self,
        tweet_data: Dict,
        users_data: Optional[Dict[str, Dict]] = None,
        referenced_tweets: Optional[Dict[str, Dict]] = None
    ) -> TweetItem:
        """
        Process a single tweet data dictionary into a TweetItem object.

        Args:
            tweet_data: Dictionary containing tweet data from the API
            users_data: Dictionary mapping user IDs to user data
            referenced_tweets: Dictionary mapping tweet IDs to referenced tweet data

        Returns:
            A TweetItem object
        """
        # Extract URLs - look in entities.urls
        urls = []
        hashtags = []
        mentions = []

        # Process entities if available
        if 'entities' in tweet_data:
            # Process URLs
            if 'urls' in tweet_data['entities']:
                for url_entity in tweet_data['entities']['urls']:
                    if 'expanded_url' in url_entity:
                        # Basic validation if it looks like a URL
                        if url_entity['expanded_url'].startswith(('http://', 'https://')):
                            urls.append(url_entity['expanded_url'])

            # Process hashtags
            if 'hashtags' in tweet_data['entities']:
                for hashtag_entity in tweet_data['entities']['hashtags']:
                    if 'tag' in hashtag_entity:
                        hashtags.append(hashtag_entity['tag'])

            # Process mentions
            if 'mentions' in tweet_data['entities']:
                for mention_entity in tweet_data['entities']['mentions']:
                    if 'username' in mention_entity:
                        mentions.append(mention_entity['username'])

        # Get author information
        author_id = tweet_data.get('author_id')
        author_username = None
        author_name = None

        # Look up author information in users_data if available
        if users_data and author_id and author_id in users_data:
            author_data = users_data[author_id]
            author_username = author_data.get('username')
            author_name = author_data.get('name')

        # Determine tweet type
        is_retweet = False
        is_quote = False
        is_reply = False
        has_commentary = True  # Default to True for original tweets
        in_reply_to_tweet_id = None
        in_reply_to_user_id = None
        quoted_tweet_id = None
        retweeted_tweet_id = None

        # Check for referenced tweets
        if 'referenced_tweets' in tweet_data:
            for ref in tweet_data['referenced_tweets']:
                ref_type = ref.get('type')
                ref_id = ref.get('id')

                if ref_type == 'retweeted' and ref_id:
                    is_retweet = True
                    retweeted_tweet_id = ref_id
                    # Standard retweets don't have commentary
                    has_commentary = False

                elif ref_type == 'quoted' and ref_id:
                    is_quote = True
                    quoted_tweet_id = ref_id
                    # Quote tweets have commentary by definition
                    has_commentary = True

                elif ref_type == 'replied_to' and ref_id:
                    is_reply = True
                    in_reply_to_tweet_id = ref_id
                    # Replies have commentary by definition
                    has_commentary = True

        # Get in_reply_to_user_id if available
        if 'in_reply_to_user_id' in tweet_data:
            in_reply_to_user_id = tweet_data['in_reply_to_user_id']
            is_reply = True

        # Extract public metrics
        metrics = tweet_data.get('public_metrics', {})
        like_count = metrics.get('like_count', 0)
        retweet_count = metrics.get('retweet_count', 0)
        reply_count = metrics.get('reply_count', 0)
        quote_count = metrics.get('quote_count', 0)
        impression_count = metrics.get('impression_count')
        bookmark_count = metrics.get('bookmark_count')

        # Create TweetItem with all available data
        return TweetItem(
            tweet_id=tweet_data.get('id'),  # Use tweet_id consistently
            text=tweet_data.get('text', ''),
            created_at=datetime.fromisoformat(tweet_data.get('created_at').replace('Z', '+00:00')),  # Ensure TZ aware

            # User information
            author_id=author_id,
            author_username=author_username,
            author_name=author_name,

            # Tweet type flags
            is_retweet=is_retweet,
            is_quote=is_quote,
            is_reply=is_reply,
            has_commentary=has_commentary,

            # Related tweet IDs
            in_reply_to_tweet_id=in_reply_to_tweet_id,
            in_reply_to_user_id=in_reply_to_user_id,
            quoted_tweet_id=quoted_tweet_id,
            retweeted_tweet_id=retweeted_tweet_id,

            # URLs and media
            urls=urls,
            hashtags=hashtags,
            mentions=mentions,

            # Individual engagement metrics
            like_count=like_count,
            retweet_count=retweet_count,
            reply_count=reply_count,
            quote_count=quote_count,
            impression_count=impression_count,
            bookmark_count=bookmark_count,

            # Processing fields are defaulted in the model
        )

    def extract_users_from_includes(self, includes: Dict) -> Dict[str, Dict]:
        """
        Extract user data from the includes section of an API response.

        Args:
            includes: The includes section of an API response

        Returns:
            Dictionary mapping user IDs to user data
        """
        users_data = {}
        if 'users' in includes:
            for user in includes['users']:
                if 'id' in user:
                    users_data[user['id']] = user
        return users_data

    def extract_referenced_tweets(self, includes: Dict) -> Dict[str, Dict]:
        """
        Extract referenced tweet data from the includes section of an API response.

        Args:
            includes: The includes section of an API response

        Returns:
            Dictionary mapping tweet IDs to referenced tweet data
        """
        referenced_tweets = {}
        if 'tweets' in includes:
            for tweet in includes['tweets']:
                if 'id' in tweet:
                    referenced_tweets[tweet['id']] = tweet
        return referenced_tweets

    def process_api_response(self, tweets_data: List[Dict], includes: Dict) -> List[TweetItem]:
        """
        Process a complete API response into TweetItem objects.

        Args:
            tweets_data: The data section of an API response
            includes: The includes section of an API response

        Returns:
            List of TweetItem objects
        """
        # Extract users and referenced tweets from includes
        users_data = self.extract_users_from_includes(includes)
        referenced_tweets = self.extract_referenced_tweets(includes)

        # Process tweets
        return self.process_tweets(tweets_data, users_data, referenced_tweets)

    def tweet_to_news_item(self, tweet: TweetItem) -> NewsItem:
        """
        Convert a TweetItem to a NewsItem for integration with the main application.

        Args:
            tweet: A TweetItem object containing tweet data

        Returns:
            A NewsItem object with data from the tweet
        """
        # Use the first URL as the link, or the Twitter status URL if no URLs in the tweet
        link = tweet.urls[0] if tweet.urls else f"https://twitter.com/i/status/{tweet.tweet_id}"

        # Create a title from the first part of the tweet text
        title_length = min(100, len(tweet.text))
        title = tweet.text[:title_length] + ("..." if len(tweet.text) > title_length else "")

        # Use the tweet text as the summary
        summary = tweet.text

        # Use the author username or ID as the publisher
        publisher = f"@{tweet.author_username}" if tweet.author_username else f"Twitter User: {tweet.author_id}"

        return NewsItem(
            title=title,
            link=link,
            published_at=tweet.created_at,
            summary=summary,
            publisher=publisher,
            source="x_list_scraper",
            fetched_at=tweet.fetched_at
        )
