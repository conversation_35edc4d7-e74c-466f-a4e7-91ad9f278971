"""
X API Rate Limiter

This module provides rate limit management for the X API client.
"""

import time
from typing import Dict, Optional
from datetime import datetime
import requests

from core.utils.logging import get_logger
from core.timezone_config import now_utc, utc_from_timestamp
from .constants import X_API_BASE_URL

logger = get_logger(__name__)


class RateLimiter:
    """Manages rate limiting for X API requests."""
    
    def __init__(self, headers: Dict[str, str]):
        """
        Initialize the rate limiter.
        
        Args:
            headers: HTTP headers containing authorization for API requests
        """
        self.headers = headers
        self.rate_limits = {
            'limit': None,       # Maximum number of requests allowed in the window
            'remaining': None,   # Number of requests remaining in current window
            'reset': None,       # Timestamp when the current window resets (Unix timestamp)
            'last_updated': None # When this information was last updated
        }
    
    def extract_rate_limit_headers(self, response: requests.Response) -> Dict:
        """
        Extract rate limit information from response headers and update internal tracking.

        Args:
            response: The HTTP response from an API request

        Returns:
            Dictionary with rate limit information
        """
        # Extract rate limit headers
        rate_limit_info = {
            'limit': response.headers.get('x-rate-limit-limit'),
            'remaining': response.headers.get('x-rate-limit-remaining'),
            'reset': response.headers.get('x-rate-limit-reset'),
            'status_code': response.status_code
        }

        # Update internal rate limit tracking if headers are present
        if rate_limit_info['limit'] and rate_limit_info['remaining'] and rate_limit_info['reset']:
            self.rate_limits['limit'] = int(rate_limit_info['limit'])
            self.rate_limits['remaining'] = int(rate_limit_info['remaining'])
            self.rate_limits['reset'] = int(rate_limit_info['reset'])
            self.rate_limits['last_updated'] = now_utc().timestamp()

            # Log rate limit information
            reset_time_str = utc_from_timestamp(int(rate_limit_info['reset'])).strftime('%Y-%m-%d %H:%M:%S')
            logger.info(f"Rate limits: {rate_limit_info['remaining']}/{rate_limit_info['limit']} requests remaining, resets at {reset_time_str}")

            # Warn if running low on requests
            if int(rate_limit_info['remaining']) < 5:
                logger.warning(f"Running low on rate limit: only {rate_limit_info['remaining']} requests remaining until {reset_time_str}")

        return rate_limit_info
    
    def is_rate_limited(self, min_remaining: int = 5, refresh_if_stale: bool = True, stale_seconds: int = 60) -> bool:
        """
        Check if we're approaching the rate limit.

        Args:
            min_remaining: Minimum number of requests that should remain
            refresh_if_stale: Whether to refresh rate limit info if it's stale
            stale_seconds: Number of seconds after which rate limit info is considered stale

        Returns:
            True if we're approaching the rate limit, False otherwise
        """
        # Check if we need to refresh rate limit info
        if refresh_if_stale and (
            self.rate_limits['last_updated'] is None or
            time.time() - self.rate_limits['last_updated'] > stale_seconds
        ):
            logger.debug("Rate limit info is stale or missing, refreshing")
            self.check_rate_limits()

        # If we still don't have rate limit info, assume we're not rate limited
        if self.rate_limits['remaining'] is None:
            logger.warning("No rate limit info available, assuming not rate limited")
            return False

        # Check if we're approaching the rate limit
        is_limited = self.rate_limits['remaining'] <= min_remaining

        # If we're approaching the limit, log a warning
        if is_limited:
            reset_time = utc_from_timestamp(self.rate_limits['reset']).strftime('%Y-%m-%d %H:%M:%S UTC')
            logger.warning(f"Approaching rate limit: {self.rate_limits['remaining']}/{self.rate_limits['limit']} requests remaining, resets at {reset_time}")

        return is_limited
    
    def check_rate_limits(self) -> Dict:
        """
        Check current rate limits by making a small request.

        Returns:
            Dictionary with rate limit information
        """
        try:
            # Make a minimal request to check rate limits
            endpoint = f"{X_API_BASE_URL}/lists"
            params = {"max_results": 1}

            response = requests.get(endpoint, headers=self.headers, params=params)

            # Extract and update rate limit information
            rate_limit_info = self.extract_rate_limit_headers(response)
            rate_limit_info['status_code'] = response.status_code

            return rate_limit_info

        except requests.exceptions.RequestException as e:
            # DO NOT log the exception 'e' directly as it may contain sensitive headers
            logger.error("Error checking rate limits: A network or request error occurred.")
            logger.debug(f"Exception type during rate limit check: {type(e).__name__}")
            return {'error': 'Failed to check rate limits due to a request exception.'}
        except Exception as e:
            # For non-request exceptions, log generic error
            logger.error("Error checking rate limits: An unexpected error occurred.")
            logger.debug(f"Exception type during rate limit check: {type(e).__name__}")
            return {'error': 'Failed to check rate limits due to an unexpected error.'}
    
    def check_and_wait_for_rate_limit(self, min_remaining: int = 5, buffer_seconds: int = 5) -> bool:
        """
        Check if we're approaching the rate limit and wait if necessary.

        Args:
            min_remaining: Minimum number of requests that should remain
            buffer_seconds: Additional seconds to wait after reset time

        Returns:
            True if waited for reset, False otherwise
        """
        # Check if we're approaching the rate limit
        if self.is_rate_limited(min_remaining=min_remaining):
            logger.warning(f"Preemptively waiting for rate limit reset (remaining: {self.rate_limits['remaining']}, min: {min_remaining})")
            return self.wait_for_rate_limit_reset(buffer_seconds=buffer_seconds, force_wait=True)
        return False
    
    def _perform_wait_with_progress(self, wait_seconds: int, message: str):
        """
        Perform a wait with progress updates for long waits.
        
        Args:
            wait_seconds: Number of seconds to wait
            message: Initial message to log before waiting
        """
        logger.warning(message)
        
        # Wait with progress updates for long waits
        if wait_seconds > 30:
            # Log progress every 15 seconds for long waits
            remaining_wait = wait_seconds
            while remaining_wait > 0:
                sleep_chunk = min(15, remaining_wait)
                time.sleep(sleep_chunk)
                remaining_wait -= sleep_chunk
                if remaining_wait > 0:
                    logger.info(f"Still waiting for rate limit reset. {remaining_wait} seconds remaining.")
        else:
            # For short waits, just sleep once
            time.sleep(wait_seconds)
        
        logger.info("Rate limit wait complete. Resuming operations.")
    
    def wait_for_rate_limit_reset(self, buffer_seconds: int = 5, force_wait: bool = False, retry_after: Optional[int] = None) -> bool:
        """
        Wait for rate limit to reset if needed or forced.

        Args:
            buffer_seconds: Additional seconds to wait after reset time
            force_wait: If True, wait regardless of remaining requests
            retry_after: Seconds to wait as specified by retry-after header (overrides calculated wait time)

        Returns:
            True if waited for reset, False otherwise
        """
        # If we already have rate limit info and are forcing a wait, use that
        if force_wait and self.rate_limits['reset'] is not None:
            reset_time = int(self.rate_limits['reset'])
            current_time = int(time.time())
            wait_seconds = max(0, reset_time - current_time + buffer_seconds)

            # If retry_after is provided, use that instead (X API's recommendation)
            if retry_after is not None:
                wait_seconds = int(retry_after) + buffer_seconds
                logger.info(f"Using retry-after value: {retry_after} seconds (plus {buffer_seconds}s buffer)")

            if wait_seconds > 0:
                reset_time_str = utc_from_timestamp(reset_time).strftime('%Y-%m-%d %H:%M:%S UTC')
                message = f"Rate limit exceeded. Waiting {wait_seconds} seconds until {reset_time_str}"
                self._perform_wait_with_progress(wait_seconds, message)
                return True
            else:
                logger.info("Rate limit should have already reset")
                return False

        # Otherwise, check current rate limits
        rate_limits = self.check_rate_limits()

        if 'error' in rate_limits:
            logger.error(f"Error checking rate limits: {rate_limits['error']}")
            return False

        remaining = int(rate_limits.get('remaining', 1000))
        reset_time = int(rate_limits.get('reset', 0))

        if remaining > 5 and not force_wait:  # Still have plenty of requests available
            logger.info(f"Rate limit remaining: {remaining}, no need to wait")
            return False

        # Calculate wait time
        current_time = int(time.time())
        wait_seconds = reset_time - current_time + buffer_seconds

        # If retry_after is provided, use that instead
        if retry_after is not None:
            wait_seconds = int(retry_after) + buffer_seconds

        if wait_seconds <= 0:
            logger.info("Rate limit should have already reset")
            return False

        reset_time_str = datetime.fromtimestamp(reset_time).strftime('%Y-%m-%d %H:%M:%S')
        message = f"Rate limit low. Waiting {wait_seconds} seconds until {reset_time_str}"
        self._perform_wait_with_progress(wait_seconds, message)
        return True