#!/usr/bin/env python3
"""
X (Twitter) List Member Addition Script

This script reads a CSV file containing X handles and adds them to a specified public list.
It processes both primary company handles and leadership handles for TAC members only.
"""

import os
import sys
import csv
import time
import requests
import re
import traceback
from dotenv import load_dotenv
import tweepy
from core.utils.logging import get_logger

# Setup logging with file handler for this script
logger = get_logger(__name__, add_file_handler=True)

# Load environment variables 
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path=dotenv_path)

# Get credentials from environment variables
CONSUMER_KEY = os.getenv('X_API_KEY')
CONSUMER_SECRET = os.getenv('X_API_SECRET')
ACCESS_TOKEN = os.getenv('X_ACCESS_TOKEN')
ACCESS_TOKEN_SECRET = os.getenv('X_ACCESS_TOKEN_SECRET')
BEARER_TOKEN = os.getenv('X_BEARER_TOKEN')

def clean_handle(handle):
    """Clean up X handle by removing @ symbol and trimming whitespace."""
    if not handle:
        return None
    
    # Remove @ symbol and any whitespace
    handle = handle.strip()
    if handle.startswith('@'):
        handle = handle.lstrip('@')
    
    # Return None for empty handles after cleaning
    if not handle:
        return None
        
    return handle

def get_user_id_from_profile_url(username):
    """
    Get user ID by scraping it from the user's profile page.
    This is a workaround when API access is limited.
    """
    try:
        # Construct URL
        url = f"https://twitter.com/{username}"
        
        # Set headers to mimic a browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        }
        
        # Send the request
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            # Look for the user ID in various patterns
            # Method 1: Look for "user_id":12345678 pattern
            user_id_match = re.search(r'"user_id"\s*:\s*"?(\d+)"?', response.text)
            if user_id_match:
                return user_id_match.group(1)
                
            # Method 2: Look for data-user-id attribute
            user_id_match = re.search(r'data-user-id="(\d+)"', response.text)
            if user_id_match:
                return user_id_match.group(1)
                
            # Method 3: Look for REST ID pattern (newer Twitter)
            user_id_match = re.search(r'"rest_id"\s*:\s*"(\d+)"', response.text)
            if user_id_match:
                return user_id_match.group(1)
                
            logger.warning(f"Could not extract user ID from {username}'s profile page")
            return None
        else:
            logger.warning(f"Failed to fetch profile page for {username}: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error fetching profile page for {username}: {e}")
        return None

def get_user_id_by_username(client, username):
    """Get X user ID from username/handle."""
    try:
        # Try using the v2 API which may work with basic access
        user = client.get_user(username=username)
        
        # Check if user and user.data exist before accessing id
        if user is None:
            logger.warning(f"API returned None for user {username}")
            raise ValueError("API returned None for user")
            
        if not hasattr(user, 'data') or user.data is None:
            logger.warning(f"API returned no data for user {username}")
            raise ValueError("API returned no data")
            
        if not hasattr(user.data, 'id'):
            logger.warning(f"API returned data but no ID for user {username}")
            raise ValueError("API returned data without ID field")
            
        # If we get here, we have a valid ID
        logger.info(f"Successfully retrieved user ID via API: {user.data.id}")
        return user.data.id
        
    except Exception as e:
        # If API fails, try web scraping approach
        logger.warning(f"API lookup failed for {username}, trying alternative method: {e}")
        
        # Try web scraping approach
        user_id = get_user_id_from_profile_url(username)
        if user_id:
            logger.info(f"Successfully retrieved user ID for {username} via web page: {user_id}")
            return user_id
            
        # Get more detailed error information
        error_msg = f"Error getting user ID for {username}: {e}"
        if isinstance(e, tweepy.TweepyException) and hasattr(e, 'response') and e.response is not None:
            status_code = e.response.status_code
            error_msg += f"\nStatus code: {status_code}"
            if status_code == 401:
                error_msg += "\nThis is an authentication error. Please check:"
                error_msg += "\n- Your API keys have the correct permissions (read AND write)"
                error_msg += "\n- Your tokens are valid and not expired"
                error_msg += "\n- Your app has the necessary permissions"
            
            if hasattr(e.response, 'text'):
                error_msg += f"\nResponse text: {e.response.text[:200]}"
                
        logger.error(error_msg)
        
        # Check if rate limited
        if isinstance(e, tweepy.TweepyException) and hasattr(e, 'response') and e.response is not None and e.response.status_code == 429:
            reset_time = int(e.response.headers.get('x-rate-limit-reset', 0))
            wait_time = max(reset_time - time.time(), 0) + 1
            logger.warning(f"Rate limited. Waiting {wait_time:.0f} seconds.")
            time.sleep(wait_time)
            # Try again after waiting
            return get_user_id_by_username(client, username)
        return None

def add_member_to_list(client, list_id, user_id):
    """Add a member to the specified X list."""
    try:
        # Try using the client directly for list operations
        response = client.add_list_member(id=list_id, user_id=user_id)
        logger.info(f"Successfully added user {user_id} to list {list_id}")
        return True
    except tweepy.TweepyException as e:
        # Handle different error cases with detailed logging
        error_msg = f"Error adding user {user_id} to list {list_id}: {e}"
        
        if hasattr(e, 'response') and e.response is not None:
            status_code = e.response.status_code
            error_msg += f"\nStatus code: {status_code}"
            
            if status_code == 429:  # Rate limit
                reset_time = int(e.response.headers.get('x-rate-limit-reset', 0))
                wait_time = max(reset_time - time.time(), 0) + 1
                logger.warning(f"Rate limited. Waiting {wait_time:.0f} seconds.")
                time.sleep(wait_time)
                # Try again after waiting
                return add_member_to_list(client, list_id, user_id)
            elif status_code == 403:
                if "already a member" in str(e):
                    logger.info(f"User {user_id} is already a member of list {list_id}")
                    return True
                else:
                    error_msg += "\nThis is a permission error. Your app may not have list write permission."
            elif status_code == 401:
                error_msg += "\nAuthentication error. Check your OAuth tokens and app permissions."
                
            if hasattr(e.response, 'text'):
                error_msg += f"\nResponse text: {e.response.text[:200]}"
        
        logger.error(error_msg)
        return False

def process_csv(client, csv_file_path, list_id):
    """Process CSV file and add handles to X list."""
    added_count = 0
    error_count = 0
    already_member_count = 0
    skipped_count = 0
    total_rows = 0
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            # Check required columns are present
            if 'Primary X Handle' not in reader.fieldnames:
                logger.error("CSV file missing 'Primary X Handle' column")
                return
                
            if 'TAC Member' not in reader.fieldnames:
                logger.error("CSV file missing 'TAC Member' column")
                return
                
            total_rows = sum(1 for _ in open(csv_file_path)) - 1  # -1 for header
            logger.info(f"Processing {total_rows} rows from {csv_file_path}")
            
            # Reset file pointer
            csvfile.seek(0)
            next(reader)  # Skip header
            
            for i, row in enumerate(reader):
                try:
                    company = row.get('Company', 'Unknown')
                    primary_handle = row.get('Primary X Handle', '')
                    leadership_handles = row.get('Leadership X Handles', '')
                    is_member = row.get('TAC Member', '').strip().upper() == 'Y'
                    
                    logger.info(f"Processing {i+1}/{total_rows}: {company} (TAC Member: {is_member})")
                    
                    # Skip if not a TAC member
                    if not is_member:
                        logger.info(f"Skipping {company} - Not a TAC member")
                        skipped_count += 1
                        continue
                    
                    # Process all handles for this company
                    handles_to_process = []
                    
                    # Add primary company handle
                    if primary_handle:
                        handles_to_process.append(primary_handle)
                    
                    # Process leadership handles (can be comma-separated)
                    if leadership_handles:
                        # Split by comma and process each handle
                        leadership_list = [h.strip() for h in leadership_handles.split(',')]
                        handles_to_process.extend([h for h in leadership_list if h])
                    
                    if not handles_to_process:
                        logger.warning(f"Skipping {company} - No valid handles found")
                        skipped_count += 1
                        continue
                    
                    logger.info(f"Found {len(handles_to_process)} handles for {company}: {handles_to_process}")
                    
                    # Process each handle
                    company_success = 0
                    company_errors = 0
                    
                    for raw_handle in handles_to_process:
                        try:
                            # Clean handle
                            handle = clean_handle(raw_handle)
                            if not handle:
                                logger.warning(f"Skipping handle '{raw_handle}' for {company} - Invalid format")
                                continue
                            
                            # Get user ID
                            user_id = get_user_id_by_username(client, handle)
                            if not user_id:
                                logger.warning(f"Skipping {company} ({handle}) - Could not retrieve user ID")
                                company_errors += 1
                                continue
                            
                            # Add to list
                            logger.info(f"Adding {company} ({handle}) with user ID {user_id} to list {list_id}")
                            success = add_member_to_list(client, list_id, user_id)
                            
                            if success:
                                company_success += 1
                                logger.info(f"Successfully added {company} ({handle}) to list")
                            else:
                                company_errors += 1
                                logger.error(f"Failed to add {company} ({handle}) to list")
                        except Exception as e:
                            logger.error(f"Error processing handle {raw_handle} for {company}: {e}")
                            logger.error(traceback.format_exc())
                            company_errors += 1
                            continue
                    
                    # Update counts
                    added_count += company_success
                    error_count += company_errors
                    
                    # Optional: add a small delay between requests to be nice to the API
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Error processing company {row.get('Company', 'Unknown')}: {e}")
                    logger.error(traceback.format_exc())
                    error_count += 1
                    continue
    
    except Exception as e:
        logger.error(f"Error processing CSV file: {e}")
        logger.error(traceback.format_exc())
    
    # Print summary even if there was an error
    logger.info(f"Completed processing {csv_file_path}")
    logger.info(f"Summary: {added_count} users added, {error_count} errors, {skipped_count} skipped")
    
    print("\n=== X List Member Addition Summary ===")
    print(f"Total rows processed: {total_rows}")
    print(f"Members added to list: {added_count}")
    print(f"Already members: {already_member_count}")
    print(f"Errors: {error_count}")
    print(f"Skipped (not TAC members): {skipped_count}")
    print("======================================")

def verify_credentials(client):
    """Verify API credentials with a simple request to check auth works."""
    try:
        # Try to get the authenticated user (me)
        me = client.get_me()
        
        # Check if the response is valid
        if me is None:
            logger.error("Authentication failed - get_me() returned None")
            return False
            
        if not hasattr(me, 'data') or me.data is None:
            logger.error("Authentication succeeded but returned no user data")
            return False
            
        if not hasattr(me.data, 'username') or not hasattr(me.data, 'id'):
            logger.error("Authentication succeeded but returned incomplete user data")
            return False
            
        # If we get here, we have valid authentication
        logger.info(f"Authentication successful - Authenticated as: {me.data.username} (ID: {me.data.id})")
        return True
        
    except tweepy.TweepyException as e:
        error_msg = f"Authentication verification failed: {e}"
        if hasattr(e, 'response') and e.response is not None:
            status_code = e.response.status_code
            error_msg += f"\nStatus code: {status_code}"
            
            if status_code == 401:
                error_msg += "\nAuthentication error. Please check:"
                error_msg += "\n- Your API keys have the correct permissions (read AND write)"
                error_msg += "\n- Your tokens are valid and not expired"
                error_msg += "\n- Your app has the necessary scopes (users.read, lists.write, follows.write)"
                error_msg += "\n- Make sure your app has proper access level"
            
            if hasattr(e.response, 'text'):
                error_msg += f"\nResponse text: {e.response.text}"
        
        logger.error(error_msg)
        return False

def main():
    """Main function to run the script."""
    # Validate credentials
    if not all([CONSUMER_KEY, CONSUMER_SECRET, ACCESS_TOKEN, ACCESS_TOKEN_SECRET]):
        logger.error("Missing one or more required OAuth credentials in .env file")
        logger.error("Required: X_API_KEY, X_API_SECRET, X_ACCESS_TOKEN, X_ACCESS_TOKEN_SECRET")
        return
    
    # Get list ID from command line or hard-code it here
    if len(sys.argv) > 1:
        list_id = sys.argv[1]
    else:
        # Ask for list ID
        list_id = input("Enter the X List ID: ")
    
    # Get CSV file path
    csv_file_path = "x_list_members.csv"
    if not os.path.exists(csv_file_path):
        logger.error(f"CSV file not found: {csv_file_path}")
        return
    
    # Initialize client with both OAuth 1.0a and Bearer Token for API v2
    logger.info("Initializing X API client with OAuth 1.0a authentication")
    client = tweepy.Client(
        bearer_token=BEARER_TOKEN,
        consumer_key=CONSUMER_KEY,
        consumer_secret=CONSUMER_SECRET,
        access_token=ACCESS_TOKEN,
        access_token_secret=ACCESS_TOKEN_SECRET
    )
    
    # First verify credentials
    logger.info("Verifying API credentials before proceeding...")
    if not verify_credentials(client):
        logger.error("Failed to verify API credentials. Please check your Twitter app settings in the Developer Portal.")
        logger.error("Make sure your app has proper access with read AND write permissions.")
        logger.error("Required scopes: users.read, lists.write, follows.write")
        return
    
    # Process CSV and add members
    logger.info(f"Starting to add members to list {list_id} from {csv_file_path}")
    process_csv(client, csv_file_path, list_id)
    logger.info("Script execution completed")

if __name__ == "__main__":
    main()