from __future__ import annotations

from datetime import datetime, timezone
from typing import List, Dict, Optional, Any

__all__ = ["filter_tweets_by_time"]

def _to_datetime(dt_or_str: Any) -> Optional[datetime]:
    """Convert ISO string or datetime to **UTC-aware** datetime."""
    if isinstance(dt_or_str, datetime):
        if dt_or_str.tzinfo is None:
            return dt_or_str.replace(tzinfo=timezone.utc)
        return dt_or_str.astimezone(timezone.utc)

    if isinstance(dt_or_str, str):
        try:
            # Ensure the string is parsed as UTC
            return (
                datetime.fromisoformat(dt_or_str.replace("Z", "+00:00"))
                .astimezone(timezone.utc)
            )
        except Exception:
            return None

    return None


def filter_tweets_by_time(
    tweets: List[Any],
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> List[Any]:
    """Return tweets whose created_at falls in [start_time, end_time).

    Tweets may be raw dicts from the API or TweetItem objects with a `created_at` attr.
    Both naive and aware datetimes are handled by converting to naive before comparison.
    """
    if start_time is None and end_time is None:
        return tweets

    # Normalise thresholds to UTC-aware datetimes
    st = start_time.astimezone(timezone.utc) if start_time else None
    et = end_time.astimezone(timezone.utc) if end_time else None

    filtered: List[Any] = []
    for tw in tweets:
        # Extract created_at
        ca = None
        if isinstance(tw, dict):
            ca = _to_datetime(tw.get("created_at"))
        elif hasattr(tw, "created_at"):
            ca_attr = getattr(tw, "created_at")
            ca = _to_datetime(ca_attr)
        if ca is None:
            # If we cannot parse date, keep conservatively
            filtered.append(tw)
            continue

        if st and ca < st:
            continue
        if et and ca >= et:
            continue
        filtered.append(tw)
    return filtered 