"""
X API Client

This module provides a client for interacting with the X (Twitter) API.
It handles authentication, rate limits, and pagination.
"""

import time
import random
from typing import Dict, List, Optional, Tuple, Callable
from datetime import datetime
import requests

# Import from core module
from core.config import X_BEARER_TOKEN
from core.timezone_config import now_utc, utc_from_timestamp
from core.utils.logging import get_logger

# Import from local modules
from .constants import X_API_BASE_URL
from .error_classifier import XApiErrorClassifier
from .rate_limiter import RateLimiter

logger = get_logger(__name__)


class XApiClient:
    """Client for interacting with the X API."""

    # --- Configuration Attributes ---
    _MAX_CONSECUTIVE_ERROR_PAGES = 3
    _DEFAULT_LIST_TWEETS_PARAMS = {
        "max_results": 100,
        "expansions": "author_id,referenced_tweets.id",
        "tweet.fields": "created_at,public_metrics,entities,referenced_tweets,lang",
        "user.fields": "username,name",
    }

    def __init__(self, bearer_token: Optional[str] = None):
        """
        Initialize the X API client.

        Args:
            bearer_token: Bearer token for authentication. If None, uses the token from config.
        """
        self.bearer_token = bearer_token or X_BEARER_TOKEN
        if not self.bearer_token:
            logger.error("No bearer token provided for X API client")
            raise ValueError("Bearer token is required for X API client")

        # WARNING: Never log self.headers as it contains the bearer token!
        self.headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }

        # Initialize rate limiter
        self.rate_limiter = RateLimiter(self.headers)
        
        # Initialize circuit breaker for error-heavy responses
        self.consecutive_error_pages = 0

    def _make_request(self, endpoint: str, params: Dict, max_retries: int = 3) -> requests.Response:
        """Make a request with retries and exponential backoff."""
        retry_count = 0
        backoff_time = 1

        while retry_count <= max_retries:
            self.rate_limiter.check_and_wait_for_rate_limit(min_remaining=3)
            try:
                logger.info(f"Making request to {endpoint} with params: {params}")
                response = requests.get(endpoint, headers=self.headers, params=params)
                self.rate_limiter.extract_rate_limit_headers(response)
                
                if response.status_code == 429 or 200 <= response.status_code < 300:
                    return response
                response.raise_for_status()

            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count > max_retries:
                    logger.error(f"Maximum retries ({max_retries}) exceeded. Last error: {e}")
                    raise

                jitter = random.uniform(0, 0.1 * backoff_time)
                wait_time = backoff_time + jitter
                logger.warning(f"Request failed: {e}. Retrying in {wait_time:.2f} seconds (attempt {retry_count}/{max_retries})")
                time.sleep(wait_time)
                backoff_time *= 2
        
        raise requests.exceptions.RequestException("Request failed after all retries")
    
    def _parse_list_tweets_response(self, response: requests.Response, metadata: Dict) -> Tuple[List[Dict], Optional[str]]:
        """Parse response from list tweets endpoint."""
        try:
            data = response.json()
            metadata['response_meta'] = data.get('meta', {})

            if 'errors' in data:
                error_analysis = XApiErrorClassifier.analyze_errors(data['errors'])
                metadata['errors'] = data['errors']
                metadata['error_analysis'] = error_analysis
                
                logger.error(f"API returned {error_analysis['error_count']} error(s)")
                if error_analysis['critical_errors']:
                    logger.error(f"Critical errors: {error_analysis['critical_errors']}")
                if error_analysis['non_critical_errors']:
                    logger.warning(f"Non-critical errors: {error_analysis['non_critical_errors']}")
                
                if error_analysis['all_non_critical']:
                    logger.warning("All errors are non-critical (will continue processing)")
                    metadata['non_critical_errors'] = True
                else:
                    logger.error("Critical error(s) detected - stopping collection")
                    return [], None

            raw_tweets = data.get('data', [])
            next_token = data.get('meta', {}).get('next_token')

            includes = data.get('includes', {})
            if includes:
                metadata['includes'] = includes
            
            if raw_tweets and includes:
                valid_tweets = [t for t in raw_tweets if self._validate_tweet_completeness(t, includes)]
                invalid_count = len(raw_tweets) - len(valid_tweets)
                if invalid_count > 0:
                    logger.warning(f"Filtered out {invalid_count} tweets with incomplete data")
                    metadata['filtered_incomplete'] = invalid_count
                tweets = valid_tweets
            else:
                tweets = raw_tweets

            return tweets, next_token

        except Exception as e:
            logger.error(f"Error parsing response from X API: {e}")
            metadata['error'] = str(e)
            return [], None





    def get_list_tweets(
        self,
        list_id: str,
        max_results: int = 100,
        pagination_token: Optional[str] = None,
        max_retries: int = 3
    ) -> Tuple[List[Dict], Dict, Optional[str]]:
        """
        Get tweets from a list.

        Args:
            list_id: ID of the list to get tweets from
            max_results: Maximum number of results to return (max 100 per request)
            pagination_token: Token for pagination
            max_retries: Maximum number of retry attempts for transient errors

        Returns:
            Tuple containing:
            - List of tweet data dictionaries
            - Metadata dictionary with rate limits and other info
            - Pagination token for the next page (or None if no more pages)
        """
        # Sanitize list_id to prevent log injection
        safe_list_id = str(list_id).replace('\n', '').replace('\r', '')
        
        # Prepare endpoint and parameters
        endpoint = f"{X_API_BASE_URL}/lists/{list_id}/tweets"
        params = self._DEFAULT_LIST_TWEETS_PARAMS.copy()
        params["max_results"] = min(max_results, 100)  # Override with argument

        # Note: We'll filter by language after fetching the tweets
        # The List tweets endpoint doesn't support the lang parameter or query operators

        # Note: List Tweets endpoint doesn't support time-based parameters
        # Time-based filtering will be done after retrieval

        if pagination_token:
            # When using pagination token, don't use other filtering parameters
            # The token already encodes the query parameters from the previous request
            params["pagination_token"] = pagination_token
            logger.debug(f"Using pagination token for request: {pagination_token[:10]}...")
            param_strategy = "pagination_token"
        else:
            # No filtering parameters
            param_strategy = "no_filters"
            logger.debug("No filtering parameters provided")

        logger.debug(f"Parameter selection strategy: {param_strategy}")

        # Pagination token is now handled in the parameter selection logic above

        # Initialize metadata
        metadata = {
            'rate_limit': {},
            'request_time': now_utc().isoformat(),
            'params': params.copy(),
            'param_strategy': param_strategy
        }

        logger.info(f"Fetching tweets for list_id: {safe_list_id}")
        
        try:
            response = self._make_request(endpoint, params, max_retries)
            metadata['status_code'] = response.status_code
            metadata['rate_limit'] = {
                'limit': self.rate_limiter.rate_limits.get('limit'),
                'remaining': self.rate_limiter.rate_limits.get('remaining'),
                'reset': self.rate_limiter.rate_limits.get('reset')
            }
            
            if response.status_code == 429:
                if self.rate_limiter.rate_limits.get('reset'):
                    reset_time = utc_from_timestamp(int(self.rate_limiter.rate_limits['reset']))
                    logger.warning(f"Rate limit exceeded. Reset at: {reset_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                retry_after = response.headers.get('retry-after')
                if retry_after:
                    metadata['retry_after'] = retry_after
                return [], metadata, None
            
            tweets, next_token = self._parse_list_tweets_response(response, metadata)
            return tweets, metadata, next_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch tweets after all retries: {e}")
            metadata['error'] = str(e)
            return [], metadata, None

    def reset_circuit_breaker(self):
        """Reset the circuit breaker counter. Call this when starting a new collection."""
        self.consecutive_error_pages = 0
        logger.debug("Circuit breaker reset")
    
    def _validate_tweet_completeness(self, tweet: Dict, includes: Dict) -> bool:
        """
        Validate that a tweet has all required expansion data.
        
        Args:
            tweet: The tweet data to validate
            includes: The includes data from the API response
            
        Returns:
            Boolean indicating if the tweet has all required data
        """
        # Check if author exists in includes
        if 'author_id' in tweet:
            users = {u['id'] for u in includes.get('users', [])}
            if tweet['author_id'] not in users:
                logger.debug(f"Tweet {tweet.get('id')} missing author {tweet['author_id']} in includes")
                return False
        
        # Check if all referenced tweets exist in includes
        if 'referenced_tweets' in tweet:
            included_tweets = {t['id'] for t in includes.get('tweets', [])}
            for ref in tweet['referenced_tweets']:
                if ref['id'] not in included_tweets:
                    logger.debug(f"Tweet {tweet.get('id')} references missing tweet {ref['id']}")
                    # Note: This is expected for deleted tweets, so we log at debug level
                    return False
        
        return True
    
    def _update_id_bounds(self, page_meta: Dict, all_metadata: Dict) -> None:
        """Update newest and oldest ID bounds from page metadata."""
        if 'newest_id' in page_meta:
            newest_id = page_meta['newest_id']
            if all_metadata['newest_id'] is None or int(newest_id) > int(all_metadata['newest_id']):
                all_metadata['newest_id'] = newest_id

        if 'oldest_id' in page_meta:
            oldest_id = page_meta['oldest_id']
            if all_metadata['oldest_id'] is None or int(oldest_id) < int(all_metadata['oldest_id']):
                all_metadata['oldest_id'] = oldest_id
    
    def _update_circuit_breaker(self, tweets: List[Dict], metadata: Dict) -> bool:
        """
        Update circuit breaker state based on page results.
        
        Returns:
            True if circuit breaker tripped and pagination should stop
        """
        error_analysis = metadata.get('error_analysis', {})
        has_errors = error_analysis.get('has_critical', False) or error_analysis.get('has_non_critical', False)
        
        if has_errors and len(tweets) == 0:
            # Page returned only errors, no valid data
            self.consecutive_error_pages += 1
            logger.warning(f"Page returned only errors (consecutive: {self.consecutive_error_pages})")
            
            if self.consecutive_error_pages >= self._MAX_CONSECUTIVE_ERROR_PAGES:
                logger.error(f"Circuit breaker tripped! {self.consecutive_error_pages} consecutive error-only pages.")
                return True
        elif len(tweets) > 0:
            # Reset counter on successful page with data
            if self.consecutive_error_pages > 0:
                logger.info(f"Resetting error page counter (was {self.consecutive_error_pages})")
            self.consecutive_error_pages = 0
        
        return False
    
    def _handle_rate_limit_response(
        self, 
        metadata: Dict, 
        page_callback: Optional[Callable],
        page_count: int
    ) -> bool:
        """
        Handle rate limit response.
        
        Returns:
            True if rate limit was handled and pagination can continue
        """
        logger.warning("Rate limit exceeded during pagination")
        
        # Call the page callback with rate limit state if provided
        if page_callback:
            try:
                # Pass rate limit information in metadata
                page_callback(metadata, None, page_count, None)
            except Exception as e:
                logger.error(f"Error in page callback: {e}")
        
        # Wait for rate limit to reset
        retry_after = metadata.get('retry_after')
        if retry_after:
            retry_after = int(retry_after)
        
        # Wait for the rate limit to reset
        waited = self.rate_limiter.wait_for_rate_limit_reset(
            buffer_seconds=5,
            force_wait=True,
            retry_after=retry_after
        )
        
        if waited:
            logger.info("Resuming pagination after rate limit wait")
        else:
            logger.warning("Could not wait for rate limit reset. Stopping pagination.")
        
        return waited
    
    def _process_page_response(
        self,
        tweets: List[Dict],
        metadata: Dict,
        all_tweets: List[Dict],
        all_metadata: Dict
    ) -> Tuple[List[Dict], bool]:
        """
        Process a page of tweets and update tracking data.
        
        Returns:
            Tuple of (filtered_tweets, should_stop_pagination)
        """
        # Note: Time filtering is now done in the collector class
        filtered_tweets = tweets
        
        if tweets:
            all_tweets.extend(filtered_tweets)
            all_metadata['total_tweets'] = len(all_tweets)
            all_metadata['filtered_count'] = all_metadata.get('filtered_count', 0) + (len(tweets) - len(filtered_tweets))
            
            # Log if we processed tweets despite errors
            error_analysis = metadata.get('error_analysis', {})
            if error_analysis.get('has_non_critical', False):
                logger.info(f"Successfully processed {len(tweets)} tweets despite non-critical errors")
            
            # Update newest and oldest IDs
            page_meta = metadata.get('response_meta', {})
            self._update_id_bounds(page_meta, all_metadata)
        
        # Store metadata for this page
        all_metadata['pages'].append(metadata)
        
        # Update rate limit info with the latest
        if 'rate_limit' in metadata:
            all_metadata['rate_limit'] = metadata['rate_limit']
        
        # Update circuit breaker and check if should stop
        circuit_breaker_tripped = self._update_circuit_breaker(tweets, metadata)
        if circuit_breaker_tripped:
            all_metadata['circuit_breaker_tripped'] = True
            all_metadata['circuit_breaker_reason'] = f"{self.consecutive_error_pages} consecutive error-only pages"
        
        return filtered_tweets, circuit_breaker_tripped
    
    def _check_stop_conditions(
        self,
        tweets: List[Dict],
        stop_when_older_than: Optional[datetime],
        all_metadata: Dict
    ) -> bool:
        """
        Check if pagination should stop based on tweet age.
        
        Returns:
            True if pagination should stop
        """
        if not stop_when_older_than or not tweets:
            return False
        
        try:
            oldest_date = min(
                datetime.fromisoformat(t['created_at'].replace('Z', '+00:00'))
                for t in tweets if 'created_at' in t
            )
            
            # Normalize timezone awareness for comparison
            threshold = stop_when_older_than
            
            # Ensure the threshold is timezone-aware (assume UTC if naive)
            if threshold.tzinfo is None:
                from datetime import timezone
                threshold = threshold.replace(tzinfo=timezone.utc)
            
            # The oldest_date is already parsed as timezone-aware UTC from the API
            
            if oldest_date < threshold:
                logger.info(
                    f"Oldest tweet in current page ({oldest_date.isoformat()}) is older than threshold "
                    f"{threshold.isoformat()}. Stopping further pagination."
                )
                all_metadata['pagination_complete'] = True
                return True
        except Exception as e:
            logger.warning(f"Error evaluating stop_when_older_than condition: {e}")
        
        return False
    
    def get_all_list_tweets(
        self,
        list_id: str,
        max_results: int = 1000,
        max_pages: int = 10,
        page_delay: float = 1.0,
        page_callback: Optional[Callable[[Dict, List[Dict], int, Optional[str]], None]] = None,
        initial_pagination_token: Optional[str] = None,
        stop_when_older_than: Optional[datetime] = None,
        max_retries: int = 3
    ) -> Tuple[List[Dict], Dict]:
        """
        Get all tweets from a list, handling pagination.

        Args:
            list_id: ID of the list to get tweets from
            max_results: Maximum total number of results to return
            max_pages: Maximum number of pages to fetch
            page_delay: Delay between pages in seconds
            page_callback: Optional callback function called after each page is fetched.
                           The callback receives (metadata, tweets, page_count, next_token)
            initial_pagination_token: Token to start pagination from (for resuming collection)
            stop_when_older_than: Stop pagination when tweets older than this datetime are found
            max_retries: Maximum number of retry attempts for transient errors

        Returns:
            Tuple containing:
            - List of tweet data dictionaries
            - Metadata dictionary with rate limits and other info
        """
        all_tweets = []
        all_metadata = {
            'pages': [],
            'total_tweets': 0,
            'newest_id': None,
            'oldest_id': None,
            'rate_limit': {},
            'pagination_complete': False,
            'start_time': now_utc().isoformat(),
            'time_window': None  # List Tweets endpoint doesn't support time-based parameters
        }

        # Note: List Tweets endpoint doesn't support time-based parameters
        # Time filtering will be done after retrieval
        
        # IMPORTANT: Two-stage filtering pattern
        # Stage 1 (API Client): stop_when_older_than is an efficiency optimization to stop 
        #                       pagination early when tweets are older than the threshold
        # Stage 2 (Collector): filter_tweets_by_time is a correctness filter to trim tweets
        #                      from the boundary-crossing page to match exact time window
        # Both stages are necessary - do not remove either one!
        
        # Reset circuit breaker at the start of a new collection
        if not initial_pagination_token:
            # Only reset if starting fresh, not resuming
            self.reset_circuit_breaker()

        # Initialize pagination token with the provided initial token (if any)
        pagination_token = initial_pagination_token
        page_count = 0

        # Log if we're resuming from a token
        if pagination_token:
            logger.info(f"Resuming collection with pagination token: {pagination_token}")

        while page_count < max_pages and len(all_tweets) < max_results:
            # Calculate remaining results to fetch
            remaining_results = max_results - len(all_tweets)
            page_max_results = min(remaining_results, 100)  # Max 100 per page

            # Fetch page of tweets
            try:
                tweets, metadata, next_token = self.get_list_tweets(
                    list_id=list_id,
                    max_results=page_max_results,
                    pagination_token=pagination_token,
                    max_retries=max_retries
                )
            except requests.exceptions.RequestException as e:
                logger.error(f"Unrecoverable error fetching page {page_count + 1}. Stopping pagination.")
                logger.debug(f"Exception type: {type(e).__name__}")
                all_metadata['error'] = 'Stopped due to unrecoverable request error'
                all_metadata['error_page'] = page_count + 1
                break

            # Check for errors or rate limiting
            if 'error' in metadata or 'errors' in metadata:
                # Check if this is a non-critical error that should allow continuation
                error_analysis = metadata.get('error_analysis', {})
                
                if error_analysis.get('all_non_critical', False):
                    logger.warning("Non-critical error(s) encountered, continuing pagination")
                    # Don't break - continue with the next page
                else:
                    logger.warning("Stopping pagination due to critical errors")

                    # Call the page callback with error state if provided
                    if page_callback:
                        try:
                            # Pass error information in metadata
                            page_callback(metadata, [], page_count, None)
                        except Exception as e:
                            logger.error(f"Error in page callback: {e}")

                    break

            if metadata.get('status_code') == 429:
                # Handle rate limit using helper method
                if self._handle_rate_limit_response(metadata, page_callback, page_count):
                    continue
                else:
                    break

            # Process the page response using helper method
            filtered_tweets, should_stop = self._process_page_response(tweets, metadata, all_tweets, all_metadata)
            
            if should_stop:
                break

            # Check if we should continue pagination
            pagination_token = next_token
            if not pagination_token:
                logger.info("No more pages available")
                all_metadata['pagination_complete'] = True
                break

            # Increment page count and add delay
            page_count += 1
            logger.info(f"Fetched page {page_count} with {len(tweets)} tweets, total: {len(all_tweets)}")

            # Call the page callback if provided
            if page_callback:
                try:
                    page_callback(metadata, filtered_tweets, page_count, next_token)
                except Exception as e:
                    logger.error(f"Error in page callback: {e}")

            # Check if we've reached our limits
            if page_count >= max_pages:
                logger.info(f"Reached maximum number of pages ({max_pages})")
                break

            if len(all_tweets) >= max_results:
                logger.info(f"Reached maximum number of results ({max_results})")
                break

            # Add delay before next page
            if pagination_token:
                time.sleep(page_delay)

            # Check if we should stop based on tweet age
            if self._check_stop_conditions(tweets, stop_when_older_than, all_metadata):
                break

        # Add end time to metadata
        all_metadata['end_time'] = now_utc().isoformat()
        all_metadata['pages_fetched'] = page_count
        all_metadata['next_token'] = pagination_token

        return all_tweets, all_metadata
