import feedparser
import logging
import re
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from urllib.parse import quote

# Import core models
from core.models import NewsItem, TweetItem
from core.interfaces.source_interface import DataSource
from core.timezone_config import utc_from_timestamp, now_utc

logger = logging.getLogger(__name__)

def parse_date(entry) -> datetime:
    """
    Parse date from RSS feed entry to datetime object.
    Uses published_parsed if available, falls back to parsing the published string.
    """
    try:
        # First try to use the pre-parsed published_parsed tuple
        if hasattr(entry, 'published_parsed') and entry.published_parsed:
            # Convert time.struct_time to timestamp then to timezone-aware datetime
            import time
            timestamp = time.mktime(entry.published_parsed)
            return utc_from_timestamp(timestamp)

        # Fall back to parsing the string if published exists
        elif hasattr(entry, 'published') and entry.published:
            from email.utils import parsedate_to_datetime
            # parsedate_to_datetime returns timezone-aware datetime
            return parsedate_to_datetime(entry.published)

        # Last resort
        return now_utc()
    except Exception as e:
        logger.warning(f"Failed to parse date for entry '{entry.get('title', 'unknown')}', using current time: {e}")
        return now_utc()


def fetch_google_news(keyword: str, num_results: int = 10) -> List[NewsItem]:
    """
    Fetch news from Google News RSS feed for a specific keyword.

    Args:
        keyword: The search term to query
        num_results: Maximum number of results to return

    Returns:
        List of NewsItem objects
    """
    # URL encode the keyword for the query
    encoded_keyword = quote(keyword)
    rss_url = f"https://news.google.com/rss/search?q={encoded_keyword}&hl=en-US&gl=US&ceid=US:en"

    logger.info(f"Fetching news for keyword: {keyword}")

    try:
        # Parse the RSS feed
        feed = feedparser.parse(rss_url)

        if feed.bozo:
            logger.warning(f"Feed parsing had errors: {feed.bozo_exception}")

        # Extract news items
        items = []
        for entry in feed.entries[:num_results]:
            try:
                # Extract publisher from source if available
                publisher = "Unknown"
                if hasattr(entry, 'source') and hasattr(entry.source, 'title'):
                    publisher = entry.source.title

                # Get summary from entry (might be in description or summary attribute)
                summary = ""
                if hasattr(entry, 'description'):
                    summary = entry.description
                elif hasattr(entry, 'summary'):
                    summary = entry.summary

                # Clean HTML from summary if needed
                if summary:
                    # Simple HTML tag removal (for basic cleaning)
                    summary = re.sub(r'<[^>]+>', ' ', summary)
                    # Replace common HTML entities
                    summary = summary.replace('&nbsp;', ' ')
                    summary = summary.replace('&amp;', '&')
                    summary = summary.replace('&lt;', '<')
                    summary = summary.replace('&gt;', '>')
                    summary = summary.replace('&quot;', '"')
                    # Normalize whitespace
                    summary = re.sub(r'\s+', ' ', summary)
                    summary = summary.strip()

                item = NewsItem(
                    title=entry.title,
                    link=entry.link,
                    published_at=parse_date(entry),
                    summary=summary,
                    publisher=publisher,
                )
                items.append(item)
            except Exception as e:
                logger.error(f"Error processing entry {entry.get('title', 'unknown')}: {e}")

        logger.info(f"Retrieved {len(items)} items for keyword '{keyword}'")
        return items

    except Exception as e:
        logger.error(f"Error fetching Google News for '{keyword}': {e}")
        return []


def fetch_all_keywords(keywords: List[str], items_per_keyword: int = 10) -> List[NewsItem]:
    """
    Fetch news for multiple keywords.

    Args:
        keywords: List of keywords to search for
        items_per_keyword: Maximum number of items to fetch per keyword

    Returns:
        Combined list of unique NewsItem objects
    """
    all_items = []
    unique_links = set()

    for keyword in keywords:
        items = fetch_google_news(keyword, items_per_keyword)

        # Add only items with unique links
        for item in items:
            link_str = str(item.link)
            if link_str not in unique_links:
                unique_links.add(link_str)
                all_items.append(item)

    logger.info(f"Total unique items fetched across all keywords: {len(all_items)}")
    return all_items


class GoogleNewsDataSource(DataSource):
    """
    DataSource implementation for Google News RSS feeds.
    """

    def get_source_name(self) -> str:
        """Returns a human-readable name for the source."""
        return "Google News RSS"

    def fetch_items(self, config_params: Optional[Dict[str, Any]] = None) -> List[Union[NewsItem, TweetItem]]:
        """
        Fetches news items from Google News RSS feeds.

        Args:
            config_params: Dictionary containing configuration parameters:
                - keywords: List of keywords to search for
                - items_per_keyword: Maximum number of items to fetch per keyword

        Returns:
            List of NewsItem objects
        """
        if config_params is None:
            config_params = {}

        keywords = config_params.get('keywords', [])
        items_per_keyword = config_params.get('items_per_keyword', 10)

        if not keywords:
            logger.warning("No keywords provided for Google News RSS search")
            return []

        return fetch_all_keywords(keywords, items_per_keyword)