import asyncio
import sys
from telethon import TelegramClient
from core.config import TELEGRAM_API_ID, TELEGRAM_API_HASH
from core.utils.logging import get_logger

logger = get_logger(__name__)

async def setup_telegram():
    """
    Interactive setup for Telegram authentication.
    This only needs to be run once to create a session file.
    """
    if TELEGRAM_API_ID == 0 or not TELEGRAM_API_HASH:
        logger.error("Telegram API credentials not found in .env file")
        logger.error("Please add TELEGRAM_API_ID and TELEGRAM_API_HASH to your .env file")
        return
    
    logger.info("Starting Telegram authentication setup...")
    
    client = TelegramClient('tac_news_session', TELEGRAM_API_ID, TELEGRAM_API_HASH)
    
    try:
        await client.start()
        
        if await client.is_user_authorized():
            logger.info("You are already authenticated!")
        else:
            logger.info("You need to authenticate with Telegram.")
            logger.info("Please check your phone for the verification code.")
            
        me = await client.get_me()
        logger.info(f"Successfully authenticated as: {me.first_name} (ID: {me.id})")
        
    except Exception as e:
        logger.error(f"Error during setup: {e}")
    finally:
        await client.disconnect()
        logger.info("Setup complete! You can now use the Telegram scraper.")

if __name__ == "__main__":
    asyncio.run(setup_telegram()) 