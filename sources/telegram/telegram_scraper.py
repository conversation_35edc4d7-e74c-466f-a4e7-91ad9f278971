import logging
import async<PERSON>
import os
import sys
import base64
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import ChatAdminRequiredError, ChannelPrivateError, SessionPasswordNeededError, AuthKeyUnregisteredError
from core.models import NewsItem, TweetItem
from core.config import TELEGRAM_API_ID, TELEGRAM_API_HASH, TELEGRAM_CHANNELS, TELEGRAM_MESSAGE_LIMIT
from core.interfaces.source_interface import DataSource
from core.timezone_config import now_utc

logger = logging.getLogger(__name__)

async def get_telegram_client() -> TelegramClient:
    """
    Creates and authenticates a Telegram client based on environment configuration.
    
    Authentication hierarchy:
    1. TELEGRAM_SESSION_STRING (CI/CD priority)
    2. TELEGRAM_BOT_TOKEN (if only accessing public channels)
    3. Interactive login (local dev only, with session string output)
    
    Returns:
        Connected TelegramClient instance or None on failure
    """
    if not TELEGRAM_API_ID or not TELEGRAM_API_HASH:
        logger.error("TELEGRAM_API_ID and TELEGRAM_API_HASH must be set in environment variables")
        return None
    
    session_string = os.getenv('TELEGRAM_SESSION_STRING')
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    
    # 1. Priority: Session String (for CI/CD)
    if session_string:
        logger.info("Attempting authentication with session string")
        try:
            # Decode base64 if needed
            try:
                decoded_session = base64.b64decode(session_string).decode('utf-8')
            except:
                # If decoding fails, assume it's already decoded
                decoded_session = session_string
                
            client = TelegramClient(StringSession(decoded_session), TELEGRAM_API_ID, TELEGRAM_API_HASH)
            await client.connect()
            
            if not await client.is_user_authorized():
                raise AuthKeyUnregisteredError("Session expired or invalid")
                
            logger.info("Successfully authenticated via session string")
            return client
            
        except AuthKeyUnregisteredError:
            logger.error("Session string is expired or invalid. Please generate a new one.")
            # In non-interactive environment, this is fatal
            if not sys.stdout.isatty():
                logger.error("FATAL: Cannot generate new session in non-interactive environment")
                return None
            # In interactive environment, fall through to interactive login
            logger.info("Falling back to interactive login to generate new session")
            
        except Exception as e:
            logger.error(f"Unexpected error with session string: {e}")
            return None
    
    # 2. Second Priority: Bot Token (for public channels)
    if bot_token:
        logger.info("Attempting authentication with bot token")
        try:
            # Use in-memory session for bot
            client = TelegramClient(StringSession(), TELEGRAM_API_ID, TELEGRAM_API_HASH)
            await client.start(bot_token=bot_token)
            logger.info("Successfully authenticated as bot")
            return client
        except Exception as e:
            logger.error(f"Bot authentication failed: {e}")
            return None
    
    # 3. Interactive Login (dev environment only)
    if sys.stdout.isatty():
        logger.info("No session string or bot token found. Starting interactive login...")
        try:
            client = TelegramClient(StringSession(), TELEGRAM_API_ID, TELEGRAM_API_HASH)
            await client.connect()
            
            phone_number = input("Please enter your phone number (with country code): ")
            await client.send_code_request(phone_number)
            
            try:
                code = input("Enter the verification code: ")
                await client.sign_in(phone_number, code)
            except SessionPasswordNeededError:
                password = input("Two-factor authentication enabled. Enter password: ")
                await client.sign_in(password=password)
            
            # Save and display the session string
            new_session_string = client.session.save()
            encoded_session = base64.b64encode(new_session_string.encode()).decode()
            
            print("\n" + "="*60)
            print("LOGIN SUCCESSFUL!")
            print("="*60)
            print("\nIMPORTANT: Save this session string as TELEGRAM_SESSION_STRING environment variable:")
            print(f"\n{encoded_session}\n")
            print("For GitHub Actions:")
            print("1. Go to repository Settings → Secrets → Actions")
            print("2. Create a new secret named TELEGRAM_SESSION_STRING")
            print("3. Paste the above string as the value")
            print("="*60 + "\n")
            
            logger.info("Interactive login successful")
            return client
            
        except Exception as e:
            logger.error(f"Interactive login failed: {e}")
            return None
    else:
        # Non-interactive environment with no valid credentials
        logger.error("FATAL: Non-interactive environment detected without valid TELEGRAM_SESSION_STRING or TELEGRAM_BOT_TOKEN")
        logger.error("To fix: Run this script locally to generate a session string, then add it to your CI/CD secrets")
        return None

async def fetch_channel_messages(client: TelegramClient, channel_name: str, limit: int | None = None) -> List[dict]:
    """
    Fetch recent messages from a Telegram channel.

    Args:
        client: Authenticated TelegramClient instance
        channel_name: Username of the channel (without '@')

    Returns:
        List of message dictionaries
    """
    messages = []
    try:
        logger.info(f"Attempting to fetch messages from channel: {channel_name}")

        # Try to get entity by username
        try:
            entity = await client.get_entity(channel_name)
        except ValueError:
            # If username doesn't work, try with @ prefix
            try:
                entity = await client.get_entity(f"@{channel_name}")
            except ValueError:
                logger.error(f"Channel not found: {channel_name}")
                return []

        # Determine per-channel limit (fallback to config default)
        effective_limit = limit if limit is not None else TELEGRAM_MESSAGE_LIMIT

        # Fetch recent messages from the channel
        count = 0
        async for message in client.iter_messages(entity, limit=effective_limit):
            # Skip empty messages and messages without text
            if not message or not message.text:
                continue

            # Skip very short messages or bot commands
            text = message.text.strip()
            if len(text) < 10 or text.startswith('/'):
                continue

            # Create message dictionary with relevant fields
            message_data = {
                'id': message.id,
                'text': text,
                'date': message.date,
                'channel': channel_name
            }

            messages.append(message_data)
            count += 1

        logger.info(f"Retrieved {count} valid messages from channel '{channel_name}'")

    except AuthKeyUnregisteredError:
        logger.error(f"SESSION EXPIRED: Your Telegram session has expired or been revoked.")
        logger.error("To fix: Run the script locally to generate a new session string, then update TELEGRAM_SESSION_STRING in your CI/CD secrets")
        # Re-raise to ensure the error propagates up
        raise
    except ChannelPrivateError:
        logger.error(f"Cannot access private channel: {channel_name}")
    except Exception as e:
        logger.error(f"Error fetching messages from '{channel_name}': {e}")

    return messages

def message_to_news_item(message: dict) -> NewsItem:
    """
    Convert a Telegram message to a NewsItem.

    Args:
        message: Dictionary containing message data

    Returns:
        NewsItem object
    """
    # Extract title from the first line or first sentence
    text = message['text'].strip()

    # Use the first line as title if possible
    if '\n' in text:
        title_text = text.split('\n')[0]
    else:
        # Otherwise use the first sentence or truncate
        title_text = text.split('.')[0] if '.' in text else text

    # Limit title length
    title = title_text[:100] + ('...' if len(title_text) > 100 else '')

    # Create a link to the telegram message
    channel = message['channel']
    # Strip @ symbol from channel name for proper URL format
    channel_clean = channel.lstrip('@')
    msg_id = message['id']
    telegram_link = f"https://t.me/{channel_clean}/{msg_id}"
    
    # Extract the last URL from the message text as the primary link
    from urlextract import URLExtract
    extractor = URLExtract()
    urls = extractor.find_urls(text)
    
    if urls:
        # Use the last URL as the primary link (article URL)
        link = urls[-1]
        # Handle both string URLs and tuple format from newer URLExtract versions
        if isinstance(link, tuple):
            link = link[0]  # Extract URL string from tuple
        # Ensure URL has protocol
        if not link.startswith(('http://', 'https://')):
            link = f'https://{link}'
    else:
        # Fallback to telegram message link if no URLs found
        link = telegram_link

    # Use Telegram's maximum message length (4096 characters)
    # This is the hard limit imposed by the Telegram API
    max_summary_length = 4096

    # Simple truncation approach
    if len(text) <= max_summary_length:
        summary = text
    else:
        # Truncate at the maximum length and add a continuation message
        summary = text[:max_summary_length] + '... [Message continues. Click the link to read the full message.]'

    return NewsItem(
        title=title,
        link=link,
        published_at=message['date'],
        summary=summary,
        publisher=f"Telegram: @{channel_clean}",
        source="telegram",
        fetched_at=now_utc()
    )

async def fetch_all_channels(limit: int | None = None) -> List[NewsItem]:
    """
    Fetch messages from all configured Telegram channels.

    Returns:
        List of NewsItem objects from all channels
    """
    client = await get_telegram_client()
    if not client:
        return []

    all_items = []
    try:
        for channel in TELEGRAM_CHANNELS:
            messages = await fetch_channel_messages(client, channel, limit=limit)
            items = [message_to_news_item(msg) for msg in messages]
            all_items.extend(items)

        logger.info(f"Total Telegram items fetched: {len(all_items)}")

    except Exception as e:
        logger.error(f"Error in fetch_all_channels: {e}")
    finally:
        logger.info("Disconnecting Telegram client...")
        await client.disconnect()
        logger.info("Telegram client disconnected successfully")

    return all_items

def fetch_telegram_news(limit: int | None = None) -> List[NewsItem]:
    """
    Synchronous wrapper for the async fetch_all_channels function.

    Returns:
        List of NewsItem objects
    """
    try:
        # asyncio.run() handles all event loop management automatically and safely
        return asyncio.run(fetch_all_channels(limit=limit))
    except Exception as e:
        logger.error(f"Error fetching Telegram news: {e}")
        return []


class TelegramDataSource(DataSource):
    """
    DataSource implementation for Telegram channels.
    """

    def get_source_name(self) -> str:
        """Returns a human-readable name for the source."""
        return "Telegram"

    def fetch_items(self, config_params: Optional[Dict[str, Any]] = None) -> List[Union[NewsItem, TweetItem]]:
        """
        Fetches news items from Telegram channels.

        Args:
            config_params: Dictionary containing configuration parameters:
                - limit: Maximum number of messages to fetch per channel

        Returns:
            List of NewsItem objects
        """
        if config_params is None:
            config_params = {}

        limit = config_params.get('limit', None)

        return fetch_telegram_news(limit=limit)