"""
Utility functions for Notion publishing operations.

This module contains helper functions for handling both NewsItem and TweetItem objects
when publishing to Notion, providing consistent formatting and data access.
"""

from datetime import datetime
from typing import Union, Optional
import pytz
from core.models import NewsItem, TweetItem
from core.timezone_config import now_utc


def get_item_publication_date(item: Union[NewsItem, TweetItem]) -> datetime:
    """
    Get the publication date from either NewsItem or TweetItem.
    
    Args:
        item: NewsItem (has published_at) or TweetItem (has created_at)
        
    Returns:
        datetime object representing when the item was published
    """
    if isinstance(item, NewsItem):
        return item.published_at
    elif isinstance(item, TweetItem):
        return item.created_at
    else:
        # Fallback to fetched_at if available
        return getattr(item, 'fetched_at', now_utc())


def get_item_link(item: Union[NewsItem, TweetItem]) -> Optional[str]:
    """
    Get the link/URL for an item, handling both NewsItem and TweetItem.
    
    Args:
        item: NewsItem or TweetItem
        
    Returns:
        String URL or None if no link available
    """
    if isinstance(item, NewsItem):
        return str(item.link) if item.link else None
    elif isinstance(item, TweetItem):
        # Construct Twitter/X URL from tweet ID and username
        if item.tweet_id and item.author_username:
            return f"https://x.com/{item.author_username}/status/{item.tweet_id}"
        elif item.tweet_id:
            # Fallback URL without username
            return f"https://x.com/i/web/status/{item.tweet_id}"
    return None


def get_item_summary(item: Union[NewsItem, TweetItem]) -> str:
    """
    Get the summary/content text for an item.
    
    Args:
        item: NewsItem (has summary) or TweetItem (has text)
        
    Returns:
        String containing the item's content
    """
    if isinstance(item, NewsItem):
        return item.summary
    elif isinstance(item, TweetItem):
        return item.text
    else:
        return getattr(item, 'summary', getattr(item, 'text', 'No content available'))


def get_item_publisher(item: Union[NewsItem, TweetItem]) -> str:
    """
    Get the publisher/author information for an item.
    
    Args:
        item: NewsItem (has publisher) or TweetItem (has author info)
        
    Returns:
        String containing publisher/author information
    """
    if isinstance(item, NewsItem):
        return item.publisher
    elif isinstance(item, TweetItem):
        # Prefer username over name for tweets
        if item.author_username:
            return f"@{item.author_username}"
        elif item.author_name:
            return item.author_name
        else:
            return f"User {item.author_id}"
    else:
        return getattr(item, 'publisher', 'Unknown')


def format_item_metadata(item: Union[NewsItem, TweetItem]) -> str:
    """
    Format metadata string for an item (publisher + date/time in PST).
    
    Args:
        item: NewsItem or TweetItem
        
    Returns:
        Formatted string like "Published by: X • Dec 18, 2024 • 7:05 PM PST"
    """
    publisher = get_item_publisher(item)
    pub_date = get_item_publication_date(item)
    
    # Convert to PST
    pst = pytz.timezone('America/Los_Angeles')
    if pub_date.tzinfo is None:
        # If timestamp is naive, assume it's UTC
        pub_date = pytz.utc.localize(pub_date)
    pst_time = pub_date.astimezone(pst)
    
    # Format date and time
    date_str = pst_time.strftime('%b %d, %Y')
    time_str = pst_time.strftime('%-I:%M %p')  # %-I removes leading zero from hour
    
    # Use different prefix for tweets vs news
    if isinstance(item, TweetItem):
        return f"Posted by: {publisher} • {date_str} • {time_str} PST"
    else:
        return f"Published by: {publisher} • {date_str} • {time_str} PST"


def format_current_time_pst() -> str:
    """
    Format the current time in PST with 12-hour format.
    
    Returns:
        Formatted string like "7:05:30 PM PST"
    """
    pst = pytz.timezone('America/Los_Angeles')
    current_time = datetime.now(pytz.utc).astimezone(pst)
    return current_time.strftime('%-I:%M:%S %p PST')


def format_date_pst(dt: Optional[datetime] = None) -> str:
    """
    Format a date in PST timezone.
    
    Args:
        dt: datetime to format (defaults to current time if None)
        
    Returns:
        Formatted date string like "January 19, 2025"
    """
    if dt is None:
        dt = datetime.now(pytz.utc)
    elif dt.tzinfo is None:
        dt = pytz.utc.localize(dt)
    
    pst = pytz.timezone('America/Los_Angeles')
    pst_time = dt.astimezone(pst)
    return pst_time.strftime("%B %d, %Y")


def format_datetime_pst(dt: Optional[datetime] = None) -> str:
    """
    Format a datetime in PST timezone with full timestamp.
    
    Args:
        dt: datetime to format (defaults to current time if None)
        
    Returns:
        Formatted datetime string like "2025-01-19 10:30:45 PST"
    """
    if dt is None:
        dt = datetime.now(pytz.utc)
    elif dt.tzinfo is None:
        dt = pytz.utc.localize(dt)
    
    pst = pytz.timezone('America/Los_Angeles')
    pst_time = dt.astimezone(pst)
    return pst_time.strftime("%Y-%m-%d %H:%M:%S PST")


def sort_items_by_date(items: list[Union[NewsItem, TweetItem]], reverse: bool = True) -> list[Union[NewsItem, TweetItem]]:
    """
    Sort a mixed list of NewsItem and TweetItem by publication date.
    
    Args:
        items: List containing NewsItem and/or TweetItem objects
        reverse: If True, sort newest first (default)
        
    Returns:
        Sorted list of items
    """
    return sorted(items, key=get_item_publication_date, reverse=reverse)


def get_source_emoji(source: str) -> str:
    """
    Get emoji indicator for a given source.
    
    Args:
        source: The source identifier from NewsItem/TweetItem
        
    Returns:
        Emoji string for the source
    """
    source_emojis = {
        "google_news_rss": "📰",
        "telegram": "💬", 
        "x_list_scraper": "🐦",
        "google_form": "📝"
    }
    return source_emojis.get(source, "📄")  # Default emoji for unknown sources


def get_formatted_title(item, include_time_sensitive_emoji: bool = False) -> str:
    """
    Get formatted title with source emoji prefix.
    
    Handles both NewsItem and TweetItem objects by safely accessing attributes.
    
    Args:
        item: NewsItem or TweetItem object
        include_time_sensitive_emoji: Whether to include ⏰ emoji for time-sensitive items
        
    Returns:
        Formatted title string with appropriate emoji prefix
    """
    source_emoji = get_source_emoji(getattr(item, 'source', 'unknown'))
    
    # Get title - handle both NewsItem (has .title) and TweetItem (needs to construct from text)
    title = getattr(item, 'title', None)
    if title is None:
        # Handle TweetItem case - construct title from author and text
        author = getattr(item, 'author_username', None) or getattr(item, 'author_id', 'Unknown')
        text = getattr(item, 'text', 'No content')
        # Truncate text for title
        text_preview = text[:70] + '...' if len(text) > 70 else text
        title = f"Tweet from @{author}: {text_preview}"
    
    # Build formatted title
    if include_time_sensitive_emoji and getattr(item, 'is_time_sensitive', False):
        return f"⏰ {source_emoji} {title}"
    return f"{source_emoji} {title}"


def format_notion_uuid(uuid_str: str) -> str:
    """
    Ensures a Notion UUID is correctly formatted with hyphens.
    
    Args:
        uuid_str: The UUID string to format
        
    Returns:
        Properly formatted UUID with hyphens
    """
    if len(uuid_str) == 32 and "-" not in uuid_str:
        return f"{uuid_str[0:8]}-{uuid_str[8:12]}-{uuid_str[12:16]}-{uuid_str[16:20]}-{uuid_str[20:]}"
    return uuid_str