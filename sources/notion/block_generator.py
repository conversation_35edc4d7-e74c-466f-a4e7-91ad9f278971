"""
Notion block generator for creating various block types.

This module provides functions to create Notion block dictionaries,
reducing duplication and improving maintainability.
"""

from typing import Dict, List, Union, Optional
from urlextract import URLExtract
from core.models import NewsItem, TweetItem


def create_heading_block(text: str, level: int = 2) -> Dict:
    """
    Creates a heading block.
    
    Args:
        text: The heading text
        level: Heading level (1-3)
        
    Returns:
        Notion heading block dictionary
    """
    heading_type = f"heading_{level}"
    return {
        "object": "block",
        "type": heading_type,
        heading_type: {
            "rich_text": [{
                "type": "text",
                "text": {"content": text}
            }]
        }
    }


def create_paragraph_block(text: str, italic: bool = False, color: str = "default") -> Dict:
    """
    Creates a paragraph block.
    
    Args:
        text: The paragraph text
        italic: Whether to italicize the text
        color: Text color (default, gray, red, etc.)
        
    Returns:
        Notion paragraph block dictionary
    """
    annotations = {}
    if italic:
        annotations["italic"] = True
    if color != "default":
        annotations["color"] = color
    
    if not text:
        return {
            "object": "block",
            "type": "paragraph",
            "paragraph": {
                "rich_text": []
            }
        }
    
    # Extract URLs from text
    extractor = URLExtract()
    urls = extractor.find_urls(text)
    
    if not urls:
        # No URLs found, return simple text
        return {
            "object": "block",
            "type": "paragraph",
            "paragraph": {
                "rich_text": [{
                    "type": "text",
                    "text": {"content": text},
                    "annotations": annotations
                }]
            }
        }
    
    # Build rich text with clickable URLs
    rich_text = []
    last_end = 0
    
    # Find URL positions and sort by appearance
    url_positions = []
    for url in urls:
        pos = text.find(url, last_end)
        if pos != -1:
            url_positions.append((pos, pos + len(url), url))
    
    url_positions.sort()
    
    # Build rich text segments
    last_end = 0
    for start, end, url in url_positions:
        # Add text before URL
        if start > last_end:
            rich_text.append({
                "type": "text",
                "text": {"content": text[last_end:start]},
                "annotations": annotations
            })
        
        # Add URL as clickable link
        link_url = url if url.startswith(('http://', 'https://')) else f'https://{url}'
        rich_text.append({
            "type": "text",
            "text": {
                "content": url,
                "link": {"url": link_url}
            },
            "annotations": annotations
        })
        
        last_end = end
    
    # Add remaining text after last URL
    if last_end < len(text):
        rich_text.append({
            "type": "text",
            "text": {"content": text[last_end:]},
            "annotations": annotations
        })
    
    return {
        "object": "block",
        "type": "paragraph",
        "paragraph": {
            "rich_text": rich_text
        }
    }


def create_divider_block() -> Dict:
    """
    Creates a divider block.
    
    Returns:
        Notion divider block dictionary
    """
    return {
        "object": "block",
        "type": "divider",
        "divider": {}
    }


def create_callout_block(text: str, icon: str, color: str = "default") -> Dict:
    """
    Creates a callout block.
    
    Args:
        text: The callout text
        icon: Emoji icon for the callout
        color: Background color (default, red_background, etc.)
        
    Returns:
        Notion callout block dictionary
    """
    return {
        "object": "block",
        "type": "callout",
        "callout": {
            "rich_text": [{
                "type": "text",
                "text": {"content": text}
            }],
            "icon": {"emoji": icon},
            "color": color
        }
    }


def create_toggle_block(title: str, link: Optional[str] = None, children: Optional[List[Dict]] = None) -> Dict:
    """
    Creates a toggle block.
    
    Args:
        title: The toggle title text
        link: Optional URL to link the title to
        children: Optional list of child blocks
        
    Returns:
        Notion toggle block dictionary
    """
    text_obj = {
        "type": "text",
        "text": {"content": title}
    }
    
    if link:
        text_obj["text"]["link"] = {"url": link}
    
    return {
        "object": "block",
        "type": "toggle",
        "toggle": {
            "rich_text": [text_obj],
            "children": children or []
        }
    }


def create_item_toggle_block(item: Union[NewsItem, TweetItem], 
                           title: str,
                           metadata: str,
                           summary: str,
                           is_time_sensitive: bool = False) -> Dict:
    """
    Creates a toggle block for a news or tweet item with standard formatting.
    
    Args:
        item: The NewsItem or TweetItem
        title: Formatted title for the toggle
        metadata: Formatted metadata string (publisher, date, etc.)
        summary: Item summary/content
        is_time_sensitive: Whether to include time-sensitive callout
        
    Returns:
        Notion toggle block dictionary with children
    """
    from sources.notion.notion_utils import get_item_link
    
    children = []
    
    # Add time-sensitive callout if needed
    if is_time_sensitive:
        reason = getattr(item, 'timeliness_reason', 'Requires immediate attention')
        children.append(
            create_callout_block(f"TIME-SENSITIVE: {reason}", "⚠️", "red_background")
        )
    
    # Add metadata paragraph
    children.append(create_paragraph_block(metadata, italic=True))
    
    # Add summary paragraph
    children.append(create_paragraph_block(summary))
    
    # Create the toggle block
    link = get_item_link(item)
    return create_toggle_block(title, link, children)