"""
Helper module for creating timestamped subpages in Notion.

This module provides functions to create new subpages within a Notion page,
with titles that include the exact timestamp of creation.
"""

import logging
from datetime import datetime
from typing import Optional
import pytz
from notion_client import Client
from core.week_boundary_utils import get_week_date_range, get_current_week_start
from core.config import NOTION_API_TOKEN

logger = logging.getLogger(__name__)

def get_notion_client() -> Client:
    """
    Get an authenticated Notion client.
    
    Returns:
        Authenticated Notion client
    """
    return Client(auth=NOTION_API_TOKEN)

def create_timestamped_subpage(client: Client, parent_page_id: str, since_timestamp: Optional[datetime] = None) -> Optional[str]:
    """
    Create a new subpage within a parent Notion page with a timestamp in the title.

    Args:
        client: Authenticated Notion client
        parent_page_id: ID of the parent page
        since_timestamp: If provided, include this timestamp in the title

    Returns:
        ID of the new subpage if successful, None otherwise
    """
    try:
        # Generate timestamp for the title in PST
        pst = pytz.timezone('America/Los_Angeles')
        current_time = datetime.now(pytz.utc).astimezone(pst)
        current_timestamp = current_time.strftime("%Y-%m-%d %H:%M:%S PST")

        # Create page title
        if since_timestamp:
            # Format the since timestamp in PST
            if since_timestamp.tzinfo is None:
                since_timestamp = pytz.utc.localize(since_timestamp)
            since_pst = since_timestamp.astimezone(pst)
            since_str = since_pst.strftime("%Y-%m-%d %H:%M:%S PST")
            page_title = f"Newsletter - {current_timestamp} (Content since {since_str})"
        else:
            page_title = f"Newsletter - {current_timestamp}"

        # Create the subpage
        response = client.pages.create(
            parent={"page_id": parent_page_id},
            properties={
                "title": {
                    "title": [
                        {
                            "text": {
                                "content": page_title
                            }
                        }
                    ]
                }
            }
        )

        subpage_id = response["id"]
        logger.info(f"Created new subpage '{page_title}' with ID: {subpage_id}")

        return subpage_id

    except Exception as e:
        logger.error(f"Error creating timestamped subpage: {e}")
        return None


def create_weekly_notion_page(client: Client, parent_page_id: str, week_start_date: Optional[datetime] = None) -> Optional[str]:
    """
    Create a new weekly Notion page with proper structure and title.
    
    Args:
        client: Authenticated Notion client
        parent_page_id: ID of the parent page
        week_start_date: Start date of the week (defaults to current week start)
        
    Returns:
        ID of the new page if successful, None otherwise
    """
    try:
        # Get week start date if not provided
        if week_start_date is None:
            week_start_date = get_current_week_start()
            
        # Create page title with week date range
        date_range = get_week_date_range(week_start_date)
        page_title = f"TAC Weekly Newsletter - {date_range}"
        
        # Check if a page with this title already exists
        logger.info(f"Checking for existing page with title: {page_title}")
        
        # Search for child pages of the parent
        has_more = True
        next_cursor = None
        
        while has_more:
            if next_cursor:
                results = client.blocks.children.list(
                    block_id=parent_page_id,
                    start_cursor=next_cursor
                )
            else:
                results = client.blocks.children.list(
                    block_id=parent_page_id
                )
            
            # Check each child block
            for block in results.get("results", []):
                if block.get("type") == "child_page":
                    # Get the title from the child_page block
                    child_page_title = block.get("child_page", {}).get("title", "")
                    
                    if child_page_title == page_title:
                        existing_page_id = block.get("id")
                        logger.info(f"Found existing page with matching title. Using page ID: {existing_page_id}")
                        return existing_page_id
            
            # Check for more pages
            has_more = results.get("has_more", False)
            next_cursor = results.get("next_cursor", None)
        
        logger.info(f"No existing page found with title '{page_title}'. Creating new page.")
        
        # Create the page
        response = client.pages.create(
            parent={"page_id": parent_page_id},
            properties={
                "title": {
                    "title": [
                        {
                            "text": {
                                "content": page_title
                            }
                        }
                    ]
                }
            }
        )
        
        page_id = response["id"]
        logger.info(f"Created new weekly page '{page_title}' with ID: {page_id}")
        
        # Add initial page structure
        initial_blocks = [
            {
                "object": "block",
                "type": "heading_1",
                "heading_1": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": page_title
                            }
                        }
                    ]
                }
            },
            {
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": f"Welcome to this week's roundup of news related to tokenized assets and real-world assets (RWA)."
                            }
                        }
                    ]
                }
            },
            {
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": f"This newsletter covers the week of {date_range} and is updated daily with incremental additions."
                            },
                            "annotations": {
                                "italic": True
                            }
                        }
                    ]
                }
            },
            {
                "object": "block",
                "type": "divider",
                "divider": {}
            }
        ]
        
        # Add the initial blocks
        client.blocks.children.append(
            block_id=page_id,
            children=initial_blocks
        )
        
        logger.info(f"Added initial structure to weekly page {page_id}")
        return page_id
        
    except Exception as e:
        logger.error(f"Error creating weekly Notion page: {e}")
        return None
