"""
Notion publisher for TAC Weekly News Aggregation System.

This module handles publishing news items to a Notion page.
"""

import logging
from datetime import datetime
from typing import List, Optional, Union, Tuple
from notion_client import Client
import pytz

from core.models import NewsItem, TweetItem
from core.enums import RelevanceStatus
from core.config import NOTION_API_TOKEN, NOTION_PAGE_ID
from sources.notion.subpage_creator import create_timestamped_subpage, create_weekly_notion_page
from sources.notion.notion_utils import (
    get_item_publication_date,
    get_item_link, 
    get_item_summary,
    format_item_metadata,
    sort_items_by_date,
    format_current_time_pst,
    format_date_pst,
    format_datetime_pst,
    get_source_emoji,
    get_formatted_title,
    format_notion_uuid
)
from sources.notion import block_generator as bg
from core.week_boundary_utils import get_week_date_range, get_current_week_start
from core.timezone_config import now_utc

logger = logging.getLogger(__name__)

def get_notion_client() -> Optional[Client]:
    """
    Create and return a Notion API client.

    Returns:
        Notion API client or None if authentication fails
    """
    try:
        if not NOTION_API_TOKEN:
            logger.error("NOTION_API_TOKEN not set in environment variables")
            return None

        # Initialize Notion client
        client = Client(auth=NOTION_API_TOKEN)
        return client

    except Exception as e:
        logger.error(f"Error creating Notion client: {e}")
        return None

def append_to_existing_page(page_id: str, news_items: List[Union[NewsItem, TweetItem]], day_timestamp: datetime,
                          time_sensitive_items: Optional[List[Tuple[Union[NewsItem, TweetItem], str]]] = None) -> Tuple[bool, Optional[str]]:
    """
    Append news items to an existing Notion page with a daily section header.
    
    Args:
        page_id: ID of the Notion page to append to
        news_items: List of NewsItem or TweetItem objects to append
        day_timestamp: Timestamp for the daily section header
        time_sensitive_items: Pre-filtered time-sensitive items from central service
        
    Returns:
        Tuple of (success: bool, time_sensitive_block_id: Optional[str])
    """
    try:
        client = get_notion_client()
        if not client:
            return False, None
            
        blocks = []
        time_sensitive_block_id = None
        
        # Add daily section header (H2) FIRST
        # Convert to PST and format
        pst = pytz.timezone('America/Los_Angeles')
        if day_timestamp.tzinfo is None:
            day_timestamp = pytz.utc.localize(day_timestamp)
        pst_time = day_timestamp.astimezone(pst)
        day_header = pst_time.strftime("%A, %B %d, %Y")
        blocks.append(bg.create_heading_block(f"📅 {day_header}", level=2))
        
        # Add timestamp of collection
        blocks.append(bg.create_paragraph_block(f"Updated at {format_current_time_pst()}", italic=True, color="gray"))
        
        # Add divider
        blocks.append(bg.create_divider_block())
        
        # Use provided time-sensitive items instead of filtering locally
        time_sensitive_list = []
        time_sensitive_links = set()
        
        if time_sensitive_items:
            # Extract just the items (without reasons) for display
            time_sensitive_list = [item for item, reason in time_sensitive_items]
            # Track their links to avoid duplicates
            time_sensitive_links = {get_item_link(item) for item in time_sensitive_list}
            time_sensitive_links = {link for link in time_sensitive_links if link}
            
            # Add time-sensitive section header (H3 now, since it's within the day)
            blocks.append(bg.create_heading_block(f"🚨 Time-Sensitive Items ({len(time_sensitive_list)})", level=3))
            
            # Add items
            for item in sort_items_by_date(time_sensitive_list):
                # Item as toggle list with time-sensitive indicator
                formatted_title = get_formatted_title(item, include_time_sensitive_emoji=True)
                blocks.append(bg.create_item_toggle_block(
                    item,
                    formatted_title,
                    format_item_metadata(item),
                    get_item_summary(item),
                    is_time_sensitive=True
                ))
            
            # Add spacing after time-sensitive section
            blocks.append(bg.create_paragraph_block(""))
        
        # Group items by relevance (excluding time-sensitive ones already shown)
        grouped_items = {
            RelevanceStatus.RELEVANT: [],
            RelevanceStatus.NEEDS_REVIEW: [],
            RelevanceStatus.NOT_RELEVANT: []
        }
        
        for item in news_items:
            if item.relevance:
                # Skip if this item was already shown in time-sensitive section
                item_link = get_item_link(item)
                if item_link and item_link in time_sensitive_links:
                    continue
                grouped_items[item.relevance].append(item)
        
        # Add items by relevance group
        for status, items in grouped_items.items():
            if not items:
                continue
                
            # Add subsection for this relevance status
            status_label = status.value.replace("_", " ").title()
            blocks.append(bg.create_heading_block(f"{'✅' if status == RelevanceStatus.RELEVANT else '🔍' if status == RelevanceStatus.NEEDS_REVIEW else '❌'} {status_label} ({len(items)} items)", level=3))
            
            # Add items
            for item in sort_items_by_date(items):
                # Item as toggle list for compact view
                formatted_title = get_formatted_title(item)
                blocks.append(bg.create_item_toggle_block(
                    item,
                    formatted_title,
                    format_item_metadata(item),
                    get_item_summary(item),
                    is_time_sensitive=False
                ))
        
        # Add spacing at the end
        blocks.append(bg.create_paragraph_block(""))
        
        # Append blocks to the page
        logger.info(f"Appending {len(blocks)} blocks to Notion page {page_id}")
        
        # Split into chunks if needed
        chunk_size = 100
        time_sensitive_header_index = None
        
        # Find the index of the time-sensitive header in blocks
        for idx, block in enumerate(blocks):
            if (block.get("type") == "heading_3" and 
                "Time-Sensitive Items" in block.get("heading_3", {}).get("rich_text", [{}])[0].get("text", {}).get("content", "")):
                time_sensitive_header_index = idx
                break
        
        # Append blocks and capture IDs
        total_blocks_appended = 0
        for i in range(0, len(blocks), chunk_size):
            chunk = blocks[i:i + chunk_size]
            response = client.blocks.children.append(
                block_id=page_id,
                children=chunk
            )
            
            # If this chunk contains the time-sensitive header, capture its ID
            if time_sensitive_header_index is not None:
                relative_index = time_sensitive_header_index - i
                if 0 <= relative_index < len(chunk):
                    # The time-sensitive header is in this chunk
                    if "results" in response and len(response["results"]) > relative_index:
                        time_sensitive_block_id = response["results"][relative_index]["id"]
                        logger.info(f"Captured time-sensitive block ID: {time_sensitive_block_id}")
            
            total_blocks_appended += len(chunk)
        
        logger.info(f"Successfully appended {len(news_items)} news items to Notion page")
        return True, time_sensitive_block_id
        
    except Exception as e:
        logger.error(f"Error appending to Notion page: {e}")
        return False, None

def clear_page_content(page_id: str) -> bool:
    """
    Clear the content of a Notion page.

    Args:
        page_id: ID of the Notion page to clear

    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_notion_client()
        if not client:
            return False

        # Get all blocks on the page
        blocks = client.blocks.children.list(page_id)

        # Delete each block
        for block in blocks.get("results", []):
            client.blocks.delete(block["id"])

        logger.info(f"Cleared content from Notion page {page_id}")
        return True

    except Exception as e:
        logger.error(f"Error clearing Notion page content: {e}")
        return False

def format_news_items_as_blocks(news_items: List[Union[NewsItem, TweetItem]], include_relevance: bool = True) -> List[dict]:
    """
    Format a list of news items as Notion blocks.

    Args:
        news_items: List of NewsItem or TweetItem objects
        include_relevance: Whether to include relevance scores

    Returns:
        List of Notion block objects
    """
    blocks = []

    # Add header
    current_date = format_date_pst()
    blocks.append(bg.create_heading_block(f"TAC Weekly Newsletter - {current_date}", level=1))

    # Add introduction
    blocks.append(bg.create_paragraph_block("Welcome to this week's roundup of news related to tokenized assets and real-world assets (RWA)."))

    # Group news items by relevance status first
    grouped_items = {
        RelevanceStatus.RELEVANT: [],
        RelevanceStatus.NEEDS_REVIEW: [],
        RelevanceStatus.NOT_RELEVANT: [],
        None: [] # Category for items with no relevance status (should ideally be empty)
    }
    for item in news_items:
        grouped_items[item.relevance].append(item)

    # Define the order and titles for sections
    section_order = [
        (RelevanceStatus.RELEVANT, "Relevant Items"),
        (RelevanceStatus.NEEDS_REVIEW, "Needs Review Items"),
        (RelevanceStatus.NOT_RELEVANT, "Not Relevant Items"),
        (None, "Items Pending Evaluation") # Display if any items have None status
    ]

    # Add items section by section
    for status, section_title in section_order:
        items_in_section = grouped_items[status]
        
        if not items_in_section:
            continue # Skip section if no items

        # Add Section Header (H2)
        blocks.append(bg.create_heading_block(section_title, level=2))
        
        # Add Divider
        blocks.append(bg.create_divider_block())
        
        # Add items for this section
        # Optional: Group by source within each section? For now, just list them.
        for item in sort_items_by_date(items_in_section): # Sort by date within section
            # Item title with link (H3)
            formatted_title = get_formatted_title(item)
            # For H3 with link, we need to create manually since block_generator doesn't support links in headings
            blocks.append({
                "object": "block",
                "type": "heading_3",
                "heading_3": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": formatted_title,
                                "link": {
                                    "url": get_item_link(item) or ""
                                }
                            }
                        }
                    ]
                }
            })

            # Publisher and date
            blocks.append(bg.create_paragraph_block(format_item_metadata(item), italic=True))

            # Summary
            blocks.append(bg.create_paragraph_block(get_item_summary(item)))

            # Relevance score (now expected for most items passed)
            # Use a slightly different formatting
            relevance_label = "Unknown" 
            if item.relevance is not None:
                relevance_label = item.relevance.value.replace("_", " ").title()
            else: # Should not happen if items are evaluated, but handle gracefully
                relevance_label = "Pending Evaluation"

            # Display relevance using emphasis or similar instead of code block?
            # For consistency, let's keep the code block for now.
            relevance_block_text = f"Status: {relevance_label}" 
            # For code block style, we need custom formatting
            blocks.append({
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": relevance_block_text 
                            },
                            "annotations": {
                                "code": True
                            }
                        }
                    ]
                }
            })

            # Add spacing between items
            blocks.append(bg.create_paragraph_block(""))

    # Add footer
    blocks.append(bg.create_divider_block())

    blocks.append(bg.create_paragraph_block(f"Generated by TAC Weekly News Aggregation System on {format_datetime_pst()}", italic=True))

    return blocks

def publish_to_notion(news_items: List[Union[NewsItem, TweetItem]], include_relevance: bool = True, since_timestamp: Optional[datetime] = None, 
                     append_mode: bool = False, target_page_id: Optional[str] = None,
                     time_sensitive_items: Optional[List[Tuple[Union[NewsItem, TweetItem], str]]] = None) -> Tuple[Optional[str], Optional[str]]:
    """
    Publish news items to a Notion page.

    Args:
        news_items: List of NewsItem or TweetItem objects to publish
        include_relevance: Whether to include relevance scores
        since_timestamp: If provided, include this timestamp in the subpage title
        append_mode: If True, append to existing page. If False, create new subpage
        target_page_id: If append_mode is True, the page ID to append to
        time_sensitive_items: Pre-filtered time-sensitive items from central service

    Returns:
        Tuple of (page_id: Optional[str], time_sensitive_block_id: Optional[str])
    """
    try:
        if append_mode:
            # Append mode - add to existing page
            if not target_page_id:
                logger.error("target_page_id required for append mode")
                return None, None
                
            # Use append function with current timestamp
            success, time_sensitive_block_id = append_to_existing_page(target_page_id, news_items, now_utc(), time_sensitive_items)
            return (target_page_id, time_sensitive_block_id) if success else (None, None)
            
        else:
            # Create new subpage mode (original behavior)
            if not NOTION_PAGE_ID:
                logger.error("NOTION_PAGE_ID not set in environment variables")
                return None, None

            client = get_notion_client()
            if not client:
                return None, None

            # Format the page ID with hyphens if needed
            page_id = format_notion_uuid(NOTION_PAGE_ID)

            # Create a new timestamped subpage
            subpage_id = create_timestamped_subpage(client, page_id, since_timestamp)
            if not subpage_id:
                logger.error("Failed to create timestamped subpage, falling back to main page")
                final_page_id = page_id
            else:
                logger.info(f"Created timestamped subpage with ID: {subpage_id}")
                final_page_id = subpage_id

            # Format news items as blocks
            blocks = format_news_items_as_blocks(news_items, include_relevance)

            # Add blocks to the page
            logger.info(f"Adding {len(blocks)} blocks to Notion page {final_page_id}")

            # Notion API has a limit on the number of blocks that can be added at once
            # Split into chunks of 100 blocks
            chunk_size = 100
            for i in range(0, len(blocks), chunk_size):
                chunk = blocks[i:i + chunk_size]
                client.blocks.children.append(
                    block_id=final_page_id,
                    children=chunk
                )

            if subpage_id:
                logger.info(f"Successfully published {len(news_items)} news items to timestamped Notion subpage")
            else:
                logger.info(f"Successfully published {len(news_items)} news items to main Notion page")
            # For create mode, we don't capture time-sensitive block ID
            return final_page_id, None

    except Exception as e:
        logger.error(f"Error publishing to Notion: {e}")
        return None, None

def test_notion_connection() -> bool:
    """
    Test the connection to Notion.

    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_notion_client()
        if not client:
            return False

        # Test API connection
        user = client.users.me()
        logger.info(f"Connected to Notion as: {user['name']} (ID: {user['id']})")

        # Test page access
        if NOTION_PAGE_ID:
            page_id = format_notion_uuid(NOTION_PAGE_ID)

            # Try to access the page
            client.blocks.children.list(page_id)
            logger.info(f"Successfully accessed Notion page {page_id}")

        return True

    except Exception as e:
        logger.error(f"Error testing Notion connection: {e}")
        return False
