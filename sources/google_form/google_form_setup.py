"""
Setup script for Google Form integration.

This script helps users set up the Google Form integration by:
1. Checking if the Google credentials file exists
2. Verifying access to the Google Sheets API
3. Testing access to the form responses spreadsheet
"""

import os
from pathlib import Path
from googleapiclient.discovery import build
from google.oauth2 import service_account

from core.config import GOOGLE_FORM_SHEET_ID, GOOGLE_CREDENTIALS_FILE
from core.utils.logging import get_logger

logger = get_logger(__name__)

def check_credentials_file():
    """Check if the Google credentials file exists and is valid."""
    if not GOOGLE_CREDENTIALS_FILE:
        logger.error("GOOGLE_CREDENTIALS_FILE not set in config.py or .env file")
        return False

    credentials_path = Path(GOOGLE_CREDENTIALS_FILE)
    if not credentials_path.exists():
        logger.error(f"Google credentials file not found at: {credentials_path}")
        logger.info("Please download a service account credentials file from the Google Cloud Console")
        logger.info("1. Go to https://console.cloud.google.com/")
        logger.info("2. Create a project or select an existing one")
        logger.info("3. Enable the Google Sheets API")
        logger.info("4. Create a service account")
        logger.info("5. Create a JSON key for the service account")
        logger.info("6. Save the JSON file to the location specified in your config")
        return False

    logger.info(f"Found Google credentials file at: {credentials_path}")
    return True

def check_sheets_api_access():
    """Check if we can access the Google Sheets API with the credentials."""
    try:
        # Set up credentials
        credentials = service_account.Credentials.from_service_account_file(
            GOOGLE_CREDENTIALS_FILE,
            scopes=['https://www.googleapis.com/auth/spreadsheets.readonly']
        )

        # Print service account email for verification
        service_account_email = credentials.service_account_email
        logger.info(f"Service account email: {service_account_email}")

        # Build the service
        service = build('sheets', 'v4', credentials=credentials)

        # Just test if we can get the service object without making an API call
        spreadsheets = service.spreadsheets()

        logger.info("Successfully connected to Google Sheets API")
        return True

    except Exception as e:
        logger.error(f"Error connecting to Google Sheets API: {e}")
        return False

def check_form_sheet_access():
    """Check if we can access the form responses spreadsheet."""
    if not GOOGLE_FORM_SHEET_ID:
        logger.error("GOOGLE_FORM_SHEET_ID not set in config.py or .env file")
        logger.info("Please set the GOOGLE_FORM_SHEET_ID to the ID of your Google Form responses spreadsheet")
        logger.info("The ID is the long string in the URL of your spreadsheet:")
        logger.info("https://docs.google.com/spreadsheets/d/YOUR_SPREADSHEET_ID/edit")
        return False

    try:
        logger.info(f"Testing access to spreadsheet ID: {GOOGLE_FORM_SHEET_ID}")

        # Set up credentials
        credentials = service_account.Credentials.from_service_account_file(
            GOOGLE_CREDENTIALS_FILE,
            scopes=['https://www.googleapis.com/auth/spreadsheets.readonly']
        )

        # Build the service
        service = build('sheets', 'v4', credentials=credentials)

        # Try to get the spreadsheet metadata with a timeout
        logger.info("Fetching spreadsheet metadata...")
        spreadsheet = service.spreadsheets().get(spreadsheetId=GOOGLE_FORM_SHEET_ID).execute()

        logger.info(f"Successfully accessed form responses spreadsheet: {spreadsheet['properties']['title']}")

        # Try to get the first sheet
        sheets = spreadsheet.get('sheets', [])
        if not sheets:
            logger.warning("No sheets found in the spreadsheet")
            return False

        sheet_title = sheets[0]['properties']['title']
        logger.info(f"First sheet title: {sheet_title}")

        # Try to get the headers
        logger.info("Fetching sheet headers...")
        result = service.spreadsheets().values().get(
            spreadsheetId=GOOGLE_FORM_SHEET_ID,
            range=f"{sheet_title}!1:1"
        ).execute()

        values = result.get('values', [[]])
        if not values or not values[0]:
            logger.warning("No headers found in the first row")
            return False

        headers = values[0]
        logger.info(f"Found {len(headers)} columns in the form responses sheet")
        logger.info(f"Headers: {', '.join(headers)}")

        # Check for required columns
        required_columns = [
            'Timestamp',
            'Email Address',
            'What is your name? (First + Last: "Johnny Reinsch")',
            'What TAC member organization do you represent?',
            'Give us the link to the content or social post you\'re submitting'
        ]
        missing_columns = [col for col in required_columns if col not in headers]

        if missing_columns:
            logger.warning(f"Missing required columns in form responses: {', '.join(missing_columns)}")
            logger.warning("Please ensure your Google Form has questions that map to these fields")
            return False

        logger.info("Form responses sheet has all required columns")
        return True

    except Exception as e:
        logger.error(f"Error accessing form responses spreadsheet: {e}")
        logger.error("Please check that:")
        logger.error("1. The spreadsheet ID is correct")
        logger.error("2. The service account has been granted access to the spreadsheet")
        logger.error("   (Share the spreadsheet with the service account email)")
        return False

def main():
    """Run the setup checks for Google Form integration."""
    print("\n=== Google Form Integration Setup ===\n")

    # Print the spreadsheet ID from config
    print(f"Using spreadsheet ID from config: {GOOGLE_FORM_SHEET_ID}")

    # Print the spreadsheet ID from environment
    import os
    print(f"Using spreadsheet ID from environment: {os.getenv('GOOGLE_FORM_SHEET_ID')}")

    # Check credentials file
    print("\nChecking Google credentials file...")
    if not check_credentials_file():
        return

    # Check Sheets API access
    print("\nChecking Google Sheets API access...")
    if not check_sheets_api_access():
        return

    # Check form sheet access
    print("\nChecking access to form responses spreadsheet...")
    if not check_form_sheet_access():
        return

    print("\n=== Setup Complete ===")
    print("Google Form integration is correctly configured!")
    print("The system will now fetch form submissions during the regular news aggregation process.")

if __name__ == "__main__":
    main()
