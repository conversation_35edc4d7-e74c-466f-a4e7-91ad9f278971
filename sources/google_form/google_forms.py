"""
Google Forms integration for TAC Weekly News Aggregation System.

This module fetches submissions from a Google Form via Google Sheets API
and converts them to NewsItem objects for inclusion in the news aggregation.
"""

import logging
import os
import json
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from googleapiclient.discovery import build
from google.oauth2 import service_account
from pydantic import HttpUrl

# Import from core module
from core.models import NewsItem, TweetItem
from core.config import GOOGLE_FORM_SHEET_ID, GOOGLE_CREDENTIALS_FILE
from core.interfaces.source_interface import DataSource
from core.timezone_config import parse_datetime_assume_utc, now_utc

logger = logging.getLogger(__name__)

def get_sheets_service():
    """
    Create and return a Google Sheets API service object.

    Returns:
        Google Sheets API service object or None if authentication fails
    """
    try:
        # First, check if credentials are provided as JSON string in environment variable
        service_account_json = os.getenv("GOOGLE_SERVICE_ACCOUNT_JSON")
        
        if service_account_json:
            # Parse the JSON string from environment variable
            import json
            service_account_info = json.loads(service_account_json)
            credentials = service_account.Credentials.from_service_account_info(
                service_account_info,
                scopes=['https://www.googleapis.com/auth/spreadsheets.readonly']
            )
            logger.info("Using Google credentials from GOOGLE_SERVICE_ACCOUNT_JSON environment variable")
        elif os.path.exists(GOOGLE_CREDENTIALS_FILE):
            # Fall back to file-based credentials
            credentials = service_account.Credentials.from_service_account_file(
                GOOGLE_CREDENTIALS_FILE,
                scopes=['https://www.googleapis.com/auth/spreadsheets.readonly']
            )
            logger.info(f"Using Google credentials from file: {GOOGLE_CREDENTIALS_FILE}")
        else:
            logger.error("Google credentials not found. Set GOOGLE_SERVICE_ACCOUNT_JSON env var or provide credentials.json file")
            return None

        # Build the service
        service = build('sheets', 'v4', credentials=credentials)
        return service

    except json.JSONDecodeError as e:
        logger.error(f"Error parsing GOOGLE_SERVICE_ACCOUNT_JSON: {e}")
        return None
    except Exception as e:
        logger.error(f"Error creating Google Sheets service: {e}")
        return None

def fetch_form_submissions(last_processed_timestamp: Optional[datetime] = None, sheet_id: Optional[str] = None) -> List[NewsItem]:
    """
    Fetch submissions from Google Form via Google Sheets API.

    Args:
        last_processed_timestamp: If provided, only fetch submissions after this timestamp
        sheet_id: If provided, use this spreadsheet ID instead of the one from config

    Returns:
        List of NewsItem objects created from form submissions
    """
    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return []

    try:
        # Get the sheet data
        sheet = service.spreadsheets()
        # Use provided sheet_id or fall back to config
        spreadsheet_id = sheet_id or GOOGLE_FORM_SHEET_ID
        result = sheet.values().get(
            spreadsheetId=spreadsheet_id,
            range='Form Responses 1!A:F'  # Adjust range based on your form structure
        ).execute()

        values = result.get('values', [])
        if not values:
            logger.info("No form submissions found")
            return []

        # First row contains headers
        headers = values[0]

        # Map expected column indices based on actual form structure
        try:
            timestamp_idx = headers.index('Timestamp')
            email_idx = headers.index('Email Address')
            telegram_idx = headers.index('What is your Telegram handle?')
            name_idx = headers.index('What is your name? (First + Last: "Johnny Reinsch")')
            org_idx = headers.index('What TAC member organization do you represent?')
            link_idx = headers.index('Give us the link to the content or social post you\'re submitting')
        except ValueError as e:
            logger.error(f"Required column not found in form responses: {e}")
            logger.error(f"Available columns: {headers}")
            return []

        # Process rows (skip header row)
        news_items = []
        for row in values[1:]:
            try:
                # Skip if row doesn't have enough columns
                if len(row) <= max(timestamp_idx, email_idx, telegram_idx, name_idx, org_idx, link_idx):
                    continue

                # Parse timestamp
                timestamp_str = row[timestamp_idx]
                try:
                    # Google Forms timestamp format: MM/DD/YYYY HH:MM:SS
                    timestamp = parse_datetime_assume_utc(timestamp_str, '%m/%d/%Y %H:%M:%S')
                except ValueError:
                    # Try alternative format
                    timestamp = parse_datetime_assume_utc(timestamp_str, '%Y-%m-%d %H:%M:%S')

                # Skip if we've already processed this submission
                if last_processed_timestamp and timestamp <= last_processed_timestamp:
                    continue

                # Get submitter information
                name = row[name_idx]
                org = row[org_idx]
                telegram = row[telegram_idx] if telegram_idx < len(row) else ""

                # Create a title from the submitter info
                title = f"Submission from {name} ({org})"

                # Create a summary that includes the submitter's information
                summary = f"Content submitted by {name} from {org}."
                if telegram:
                    summary += f" Telegram: {telegram}"

                # Create NewsItem
                item = NewsItem(
                    title=title,
                    link=row[link_idx],
                    published_at=timestamp,
                    summary=summary,
                    publisher=f"TAC Member: {org}",
                    source="google_form",
                    fetched_at=now_utc()
                )
                news_items.append(item)

            except Exception as e:
                logger.error(f"Error processing form submission row: {e}")
                continue

        logger.info(f"Fetched {len(news_items)} new submissions from Google Form")
        return news_items

    except Exception as e:
        logger.error(f"Error fetching form submissions: {e}")
        return []

def get_last_processed_timestamp() -> Optional[datetime]:
    """
    Get the timestamp of the last processed form submission.
    This could be implemented by storing the timestamp in a file or database.

    Returns:
        Datetime of last processed submission or None if no previous processing
    """
    # This is a placeholder implementation
    # In a real implementation, you might store this in a database or file
    try:
        timestamp_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'last_form_timestamp.txt')

        if not os.path.exists(timestamp_file):
            return None

        with open(timestamp_file, 'r') as f:
            timestamp_str = f.read().strip()
            if not timestamp_str:
                return None
            # Use ensure_utc to handle both naive and aware datetimes from storage
            from core.timezone_config import ensure_utc
            return ensure_utc(datetime.fromisoformat(timestamp_str))

    except Exception as e:
        logger.error(f"Error getting last processed timestamp: {e}")
        return None

def save_last_processed_timestamp(timestamp: datetime) -> bool:
    """
    Save the timestamp of the last processed form submission.

    Args:
        timestamp: Datetime to save

    Returns:
        True if successful, False otherwise
    """
    # This is a placeholder implementation
    # In a real implementation, you might store this in a database or file
    try:
        timestamp_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'last_form_timestamp.txt')

        # Ensure data directory exists
        os.makedirs(os.path.dirname(timestamp_file), exist_ok=True)

        with open(timestamp_file, 'w') as f:
            f.write(timestamp.isoformat())
        return True

    except Exception as e:
        logger.error(f"Error saving last processed timestamp: {e}")
        return False

def fetch_new_form_submissions() -> List[NewsItem]:
    """
    Fetch only new form submissions since the last processing.

    Returns:
        List of NewsItem objects created from new form submissions
    """
    last_timestamp = get_last_processed_timestamp()
    submissions = fetch_form_submissions(last_timestamp)

    if submissions:
        # Find the most recent submission timestamp
        most_recent = max(submissions, key=lambda x: x.published_at).published_at
        save_last_processed_timestamp(most_recent)

    return submissions


class GoogleFormDataSource(DataSource):
    """
    DataSource implementation for Google Form submissions.
    """

    def get_source_name(self) -> str:
        """Returns a human-readable name for the source."""
        return "Google Form Submissions"

    def fetch_items(self, config_params: Optional[Dict[str, Any]] = None) -> List[Union[NewsItem, TweetItem]]:
        """
        Fetches news items from Google Form submissions.

        Returns:
            List of NewsItem objects
        """
        return fetch_new_form_submissions()
